org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# تحسين الشبكة والأداء
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# إعدادات الشبكة
systemProp.http.socketTimeout=60000
systemProp.http.connectionTimeout=60000
systemProp.https.socketTimeout=60000
systemProp.https.connectionTimeout=60000

# تحسين Android Build
android.enableR8.fullMode=false
android.useFullClasspathForDexingTransform=true

# إعدادات إضافية للشبكة البطيئة
systemProp.http.maxRedirects=10
systemProp.https.maxRedirects=10
systemProp.http.keepAlive=true
systemProp.https.keepAlive=true

# تحسين Gradle للشبكة البطيئة
org.gradle.workers.max=4
org.gradle.vfs.watch=false
