// Dummy implementation of file_selector_windows to fix build issues
// This file provides empty implementations of the required functions

#include <flutter/plugin_registrar_windows.h>

namespace {

// Dummy class to satisfy the linker
class DummyFileSelectorWindows {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows* registrar) {
    // Do nothing
  }
};

}  // namespace

// This is the function that will be called by the plugin registrar
void FileSelectorWindowsRegisterWithRegistrar(
    flutter::PluginRegistrarWindows* registrar) {
  DummyFileSelectorWindows::RegisterWithRegistrar(registrar);
}
