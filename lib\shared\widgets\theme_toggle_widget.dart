import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/theme_service.dart';

/// ويدجت تبديل الثيم - يمكن استخدامه كزر أو في القائمة
class ThemeToggleWidget extends StatelessWidget {
  final bool showLabel;
  final bool isIconButton;
  final double? iconSize;
  final EdgeInsetsGeometry? padding;

  const ThemeToggleWidget({
    super.key,
    this.showLabel = true,
    this.isIconButton = false,
    this.iconSize,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        if (isIconButton) {
          return IconButton(
            icon: Icon(
              themeService.themeIcon,
              size: iconSize,
            ),
            onPressed: () => themeService.toggleTheme(),
            tooltip: 'تبديل الثيم',
            padding: padding ?? const EdgeInsets.all(8),
          );
        }

        return InkWell(
          onTap: () => themeService.toggleTheme(),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  themeService.themeIcon,
                  size: iconSize ?? 24,
                ),
                if (showLabel) ...[
                  const SizedBox(width: 12),
                  Text(
                    themeService.currentThemeName,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

/// ويدجت اختيار الثيم مع قائمة منسدلة
class ThemeSelectionWidget extends StatelessWidget {
  final bool showTitle;
  final String? title;

  const ThemeSelectionWidget({
    super.key,
    this.showTitle = true,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showTitle) ...[
              Text(
                title ?? 'مظهر التطبيق',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 12),
            ],
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                ),
              ),
              child: Column(
                children: themeService.availableThemeModes.map((mode) {
                  final isSelected = themeService.themeMode == mode;
                  return InkWell(
                    onTap: () => themeService.setThemeMode(mode),
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                            : null,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            themeService.getThemeModeIcon(mode),
                            color: isSelected 
                                ? Theme.of(context).primaryColor
                                : Theme.of(context).iconTheme.color,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              themeService.getThemeModeName(mode),
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: isSelected 
                                    ? Theme.of(context).primaryColor
                                    : null,
                                fontWeight: isSelected 
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                          if (isSelected)
                            Icon(
                              Icons.check,
                              color: Theme.of(context).primaryColor,
                              size: 20,
                            ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        );
      },
    );
  }
}

/// ويدجت بسيط لتبديل الثيم في AppBar
class AppBarThemeToggle extends StatelessWidget {
  const AppBarThemeToggle({super.key});

  @override
  Widget build(BuildContext context) {
    return const ThemeToggleWidget(
      showLabel: false,
      isIconButton: true,
      iconSize: 24,
    );
  }
}

/// ويدجت لعرض الثيم الحالي مع إمكانية التبديل
class CurrentThemeDisplay extends StatelessWidget {
  final bool showIcon;
  final bool showName;
  final bool allowToggle;

  const CurrentThemeDisplay({
    super.key,
    this.showIcon = true,
    this.showName = true,
    this.allowToggle = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return GestureDetector(
          onTap: allowToggle ? () => themeService.toggleTheme() : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showIcon) ...[
                  Icon(
                    themeService.themeIcon,
                    size: 18,
                    color: Theme.of(context).primaryColor,
                  ),
                  if (showName) const SizedBox(width: 6),
                ],
                if (showName)
                  Text(
                    themeService.currentThemeName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
