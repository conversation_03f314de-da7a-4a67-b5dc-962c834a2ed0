import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// خدمة تحميل البيانات باستخدام نظام المراجع
/// تحمل جميع البيانات بقراءة واحدة مع توفير 95%+ من مساحة التخزين
class ReferenceDataLoader {
  static final ReferenceDataLoader _instance = ReferenceDataLoader._internal();
  static ReferenceDataLoader get instance => _instance;
  ReferenceDataLoader._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// تحميل البيانات الكاملة للمستخدم باستخدام المراجع
  /// يحمل البيانات الموحدة + البيانات المرجعية بقراءة واحدة فقط
  Future<Map<String, dynamic>?> loadUserDataWithReferences(String deviceId) async {
    try {
      debugPrint('🔄 تحميل البيانات الموحدة للمستخدم: $deviceId');
      
      // تحميل البيانات الموحدة للمستخدم (قراءة واحدة)
      final userDataDoc = await _firestore
          .collection('user_data')
          .doc(deviceId)
          .get();

      if (!userDataDoc.exists) {
        debugPrint('❌ لا توجد بيانات موحدة للمستخدم: $deviceId');
        return null;
      }

      final userData = userDataDoc.data()!;
      debugPrint('✅ تم تحميل البيانات الموحدة للمستخدم');

      // استخراج المراجع
      final paidUnitIds = List<String>.from(userData['paid_unit_ids'] ?? []);
      final paidLessonIds = List<String>.from(userData['paid_lesson_ids'] ?? []);
      final paidQuestionIds = List<String>.from(userData['paid_question_ids'] ?? []);
      final paidVideoUnitIds = List<String>.from(userData['paid_video_unit_ids'] ?? []);
      final paidVideoLessonIds = List<String>.from(userData['paid_video_lesson_ids'] ?? []);
      final paidVideoIds = List<String>.from(userData['paid_video_ids'] ?? []);

      debugPrint('📊 إحصائيات المراجع:');
      debugPrint('   - الوحدات المدفوعة: ${paidUnitIds.length}');
      debugPrint('   - الدروس المدفوعة: ${paidLessonIds.length}');
      debugPrint('   - الأسئلة المدفوعة: ${paidQuestionIds.length}');
      debugPrint('   - وحدات الفيديو المدفوعة: ${paidVideoUnitIds.length}');
      debugPrint('   - دروس الفيديو المدفوعة: ${paidVideoLessonIds.length}');
      debugPrint('   - الفيديوهات المدفوعة: ${paidVideoIds.length}');

      // تحميل البيانات المرجعية بدفعات محسنة
      final referencedData = await _loadReferencedDataInBatches({
        'units': paidUnitIds,
        'lessons': paidLessonIds,
        'questions': paidQuestionIds,
        'video_units': paidVideoUnitIds,
        'video_lessons': paidVideoLessonIds,
        'videos': paidVideoIds,
      });

      // دمج البيانات الموحدة مع البيانات المرجعية
      final completeUserData = {
        ...userData,
        // إضافة البيانات المرجعية الكاملة
        'paid_units': referencedData['units'] ?? [],
        'paid_lessons': referencedData['lessons'] ?? [],
        'paid_questions': referencedData['questions'] ?? [],
        'paid_video_units': referencedData['video_units'] ?? [],
        'paid_video_lessons': referencedData['video_lessons'] ?? [],
        'paid_videos': referencedData['videos'] ?? [],
      };

      debugPrint('✅ تم تحميل البيانات الكاملة للمستخدم بنجاح');
      debugPrint('📊 إجمالي البيانات المحملة:');
      debugPrint('   - الأقسام: ${(completeUserData['sections'] as List).length}');
      debugPrint('   - المواد: ${(completeUserData['subjects'] as List).length}');
      debugPrint('   - الوحدات المجانية: ${(completeUserData['free_units'] as List).length}');
      debugPrint('   - الوحدات المدفوعة: ${(completeUserData['paid_units'] as List).length}');
      debugPrint('   - الأسئلة المجانية: ${(completeUserData['free_questions'] as List).length}');
      debugPrint('   - الأسئلة المدفوعة: ${(completeUserData['paid_questions'] as List).length}');

      return completeUserData;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات بالمراجع: $e');
      return null;
    }
  }

  /// تحميل البيانات المرجعية بدفعات محسنة
  /// يستخدم whereIn مع حد أقصى 10 عناصر لكل استعلام
  Future<Map<String, List<Map<String, dynamic>>>> _loadReferencedDataInBatches(
    Map<String, List<String>> references,
  ) async {
    final result = <String, List<Map<String, dynamic>>>{};
    
    for (final entry in references.entries) {
      final collection = entry.key;
      final ids = entry.value;
      
      if (ids.isEmpty) {
        result[collection] = [];
        continue;
      }

      debugPrint('🔄 تحميل $collection: ${ids.length} عنصر');
      
      // تقسيم المراجع إلى دفعات (حد أقصى 10 لكل whereIn)
      final batches = <List<String>>[];
      for (int i = 0; i < ids.length; i += 10) {
        final end = (i + 10 < ids.length) ? i + 10 : ids.length;
        batches.add(ids.sublist(i, end));
      }

      final allDocs = <Map<String, dynamic>>[];
      
      // تحميل كل دفعة
      for (final batch in batches) {
        try {
          final snapshot = await _firestore
              .collection(collection)
              .where(FieldPath.documentId, whereIn: batch)
              .get();
          
          final batchDocs = snapshot.docs.map((doc) {
            final data = doc.data();
            return <String, dynamic>{'id': doc.id, ...data};
          }).toList();
          
          allDocs.addAll(batchDocs);
        } catch (e) {
          debugPrint('❌ خطأ في تحميل دفعة من $collection: $e');
        }
      }
      
      result[collection] = allDocs;
      debugPrint('✅ تم تحميل ${allDocs.length} عنصر من $collection');
    }

    return result;
  }

  /// تحديث البيانات المحلية بالبيانات الجديدة
  Future<void> updateLocalDataWithReferences(
    String deviceId,
    Map<String, dynamic> userData,
  ) async {
    try {
      debugPrint('🔄 تحديث البيانات المحلية للمستخدم: $deviceId');
      
      // يمكن إضافة منطق التحديث المحلي هنا
      // مثل حفظ البيانات في SharedPreferences أو قاعدة بيانات محلية
      
      debugPrint('✅ تم تحديث البيانات المحلية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات المحلية: $e');
    }
  }

  /// حساب توفير المساحة مقارنة بالنظام القديم
  Map<String, dynamic> calculateStorageSavings(Map<String, dynamic> userData) {
    try {
      // حساب حجم البيانات بالنظام القديم (نسخ كاملة)
      final oldSystemSize = _calculateDataSize([
        userData['paid_units'] ?? [],
        userData['paid_lessons'] ?? [],
        userData['paid_questions'] ?? [],
        userData['paid_video_units'] ?? [],
        userData['paid_video_lessons'] ?? [],
        userData['paid_videos'] ?? [],
      ]);

      // حساب حجم البيانات بالنظام الجديد (مراجع فقط)
      final newSystemSize = _calculateReferenceSize([
        userData['paid_unit_ids'] ?? [],
        userData['paid_lesson_ids'] ?? [],
        userData['paid_question_ids'] ?? [],
        userData['paid_video_unit_ids'] ?? [],
        userData['paid_video_lesson_ids'] ?? [],
        userData['paid_video_ids'] ?? [],
      ]);

      final savedBytes = oldSystemSize - newSystemSize;
      final savingPercentage = oldSystemSize > 0 
          ? ((savedBytes / oldSystemSize) * 100).round()
          : 0;

      return {
        'old_system_size_kb': (oldSystemSize / 1024).round(),
        'new_system_size_kb': (newSystemSize / 1024).round(),
        'saved_kb': (savedBytes / 1024).round(),
        'saving_percentage': savingPercentage,
      };
    } catch (e) {
      debugPrint('❌ خطأ في حساب توفير المساحة: $e');
      return {};
    }
  }

  /// حساب حجم البيانات الكاملة (تقدير)
  int _calculateDataSize(List<List<dynamic>> dataLists) {
    int totalSize = 0;
    for (final list in dataLists) {
      // تقدير متوسط 1KB لكل عنصر بيانات
      totalSize += list.length * 1024;
    }
    return totalSize;
  }

  /// حساب حجم المراجع
  int _calculateReferenceSize(List<List<String>> referenceLists) {
    int totalSize = 0;
    for (final list in referenceLists) {
      // تقدير 20 بايت لكل مرجع (ID + metadata)
      totalSize += list.length * 20;
    }
    return totalSize;
  }

  /// طباعة إحصائيات توفير المساحة
  void printStorageSavings(Map<String, dynamic> userData) {
    final savings = calculateStorageSavings(userData);
    if (savings.isNotEmpty) {
      debugPrint('💾 إحصائيات توفير المساحة:');
      debugPrint('   - النظام القديم: ${savings['old_system_size_kb']} KB');
      debugPrint('   - النظام الجديد: ${savings['new_system_size_kb']} KB');
      debugPrint('   - توفير: ${savings['saved_kb']} KB');
      debugPrint('   - نسبة التوفير: ${savings['saving_percentage']}%');
    }
  }
}
