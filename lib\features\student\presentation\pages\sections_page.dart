import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/adaptive_sizing.dart';
import '../../../../core/widgets/adaptive_widgets.dart';
import '../../../../core/models/section.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/data_distribution_service.dart';
// النظام الجديد Offline-First
import '../../../../shared/services/single_read_data_service.dart';

import 'subjects_by_section_page.dart';
import 'free_sections_page.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';

class SectionsPage extends StatefulWidget {
  const SectionsPage({super.key});

  @override
  State<SectionsPage> createState() => _SectionsPageState();
}

class _SectionsPageState extends State<SectionsPage> {
  final DataDistributionService _dataService = DataDistributionService.instance;
  List<Section> _sections = [];

  @override
  void initState() {
    super.initState();
    debugPrint('🔥 [STUDENT MONITOR] بدء تحميل صفحة الأقسام بالنظام الجديد...');
    debugPrint('🕐 [STUDENT MONITOR] الوقت: ${DateTime.now()}');
    _loadSectionsImmediately();
  }

  /// تحميل فوري للأقسام المدفوعة فقط من النظام الجديد
  Future<void> _loadSectionsImmediately() async {
    try {
      // استخدام خدمة فرز البيانات الجديدة
      final paidSections = _dataService.getPaidSections();

      if (mounted) {
        setState(() {
          _sections = paidSections;
        });
        debugPrint(
          '⚡ [STUDENT MONITOR] تم تحميل ${_sections.length} قسم مدفوع فوراً من خدمة فرز البيانات',
        );
        debugPrint('📋 [STUDENT MONITOR] الأقسام المدفوعة:');
        for (var section in _sections) {
          debugPrint('   - ${section.name} (مجاني: ${section.isFree})');
        }
        debugPrint('🕐 [STUDENT MONITOR] انتهى في: ${DateTime.now()}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام: $e');
    }
  }

  // تم إزالة وظيفة التحديث - التحديث يتم من الصفحة الرئيسية فقط

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AdaptiveAppBar(
        title: 'الاختبارات',
        gradient: AppTheme.getPrimaryGradient(context),
        leading: IconButton(
          icon: AdaptiveIcon(Icons.search, size: 24, color: Colors.white),
          onPressed: _showQuestionSearchDialog,
          tooltip: 'البحث عن سؤال',
        ),
        actions: [
          // زر تجربة التطبيق مع شعار الاختبارات
          Container(
            margin: EdgeInsets.only(left: 8.adaptiveSpacing),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20.adaptiveRadius),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FreeSectionsPage(),
                    ),
                  );
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.adaptiveSpacing,
                    vertical: 6.adaptiveSpacing,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20.adaptiveRadius),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AdaptiveIcon(
                        Icons.quiz_outlined,
                        size: 20,
                        color: Colors.white,
                      ),
                      SizedBox(width: 4.adaptiveSpacing),
                      AdaptiveText(
                        'تجربة التطبيق',
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    // إذا كان هناك بيانات، نعرضها فوراً
    if (_sections.isNotEmpty) {
      return _buildSectionsContent();
    }

    // النظام الجديد: عرض البيانات مباشرة بدون مؤشرات تحميل

    // فقط إذا حاولنا التحميل ولم توجد بيانات، نعرض رسالة فارغة
    return _buildEmptyState();
  }

  Widget _buildSectionsContent() {
    return Padding(
      padding: EdgeInsets.all(16.adaptiveSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: 20.adaptiveSpacing),
          Expanded(
            child: AdaptiveGridView(
              shrinkWrap: false,
              children: _sections
                  .map((section) => _buildSectionCard(section))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.adaptiveSpacing),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.adaptiveRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.adaptiveSpacing),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.adaptiveRadius),
            ),
            child: AdaptiveIcon(Icons.school, size: 24, color: Colors.white),
          ),
          SizedBox(width: 16.adaptiveSpacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AdaptiveText(
                  'مكتبة الاختبارات',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                SizedBox(height: 4.adaptiveSpacing),
                AdaptiveText(
                  'اختر القسم لحل الاختبارات التعليمية',
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(Section section) {
    return AdaptiveSectionCard(
      title: section.name,
      subtitle: section.description,
      backgroundColor: _getSectionColor(section.color),
      onTap: () => _navigateToSubjects(section),
      leading: AdaptiveIcon(Icons.school, size: 32, color: Colors.white),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AdaptiveIcon(
            Icons.school_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.adaptiveSpacing),
          AdaptiveText(
            'لا توجد أقسام متاحة',
            fontSize: 18,
            color: Colors.grey[600],
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: 8.adaptiveSpacing),
          AdaptiveText(
            'تأكد من الاتصال بالإنترنت وحاول مرة أخرى',
            fontSize: 14,
            color: Colors.grey[500],
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.adaptiveSpacing),
          AdaptiveText(
            'يرجى استخدام زر التحديث في الصفحة الرئيسية',
            fontSize: 12,
            color: Colors.grey[400],
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _navigateToSubjects(Section section) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubjectsBySectionPage(section: section),
      ),
    );
  }

  Color _getSectionColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppTheme.primaryColor; // لون افتراضي في حالة الخطأ
    }
  }

  /// عرض نافذة البحث عن السؤال
  void _showQuestionSearchDialog() {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.search, color: AppTheme.primaryColor),
            SizedBox(width: 8.w),
            Text('البحث عن سؤال'),
          ],
        ),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: 'رقم السؤال',
            hintText: 'مثال: #bi5WY8W7MwLX2U2QzwAl',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.tag),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final input = controller.text.trim();
              if (input.isNotEmpty) {
                Navigator.pop(context);
                // إزالة الهاشتاغ إذا كان موجوداً
                final questionId = input.startsWith('#')
                    ? input.substring(1).trim()
                    : input;
                _searchForQuestion(questionId);
              }
            },
            child: Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// البحث عن السؤال بالرقم مع التحقق من الاشتراك
  Future<void> _searchForQuestion(String questionId) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16.h),
                  Text('جاري البحث عن السؤال...'),
                ],
              ),
            ),
          ),
        ),
      );

      // البحث عن السؤال
      final question = await ExamService.instance.getQuestionById(questionId);

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.pop(context);

      if (question != null) {
        // التحقق من الاشتراك في المادة
        final isSubscribed = SubscriptionService.instance.isSubscribedToSubject(
          question.subjectId,
        );

        if (isSubscribed) {
          // الحصول على بيانات المادة
          final subject = await ContentService.instance.getSubjectById(
            question.subjectId,
          );

          if (subject != null && mounted) {
            // الانتقال إلى السؤال
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => QuestionsViewerPage(
                  title: 'السؤال المطلوب',
                  subject: subject,
                  questionType: QuestionFilterType.single,
                  singleQuestionId: question.id,
                ),
              ),
            );
          } else if (mounted) {
            _showErrorDialog('لم يتم العثور على بيانات المادة');
          }
        } else if (mounted) {
          // عرض رسالة تطلب الاشتراك
          _showSubscriptionRequiredDialog(question.subjectId);
        }
      } else if (mounted) {
        _showErrorDialog('لم يتم العثور على سؤال بهذا الرقم');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (mounted) {
        Navigator.pop(context);
        _showErrorDialog('حدث خطأ أثناء البحث: ${e.toString()}');
      }
    }
  }

  /// عرض رسالة خطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8.w),
            Text('خطأ'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة تطلب الاشتراك
  void _showSubscriptionRequiredDialog(String subjectId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 8.w),
            Text('اشتراك مطلوب'),
          ],
        ),
        content: Text(
          'السؤال من مادة غير مشترك بها. يرجى تفعيل اشتراك للوصول إليه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }
}
