import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/subject_model.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';
import '../models/question_model.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import '../models/user_subscription_model.dart';
import '../../core/models/section.dart';
import 'device_service.dart';
import 'persistent_storage_service.dart';
import 'reference_data_loader.dart';
import 'data_distribution_service.dart';

/// خدمة القراءة الواحدة - تحمل جميع البيانات بقراءة واحدة فقط
/// هذه الخدمة تضمن عدم تجاوز قراءة واحدة يومياً للطالب الواحد
class SingleReadDataService extends ChangeNotifier {
  static final SingleReadDataService _instance =
      SingleReadDataService._internal();
  static SingleReadDataService get instance => _instance;
  SingleReadDataService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final DeviceService _deviceService = DeviceService.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;
  final ReferenceDataLoader _referenceLoader = ReferenceDataLoader.instance;

  // حالة التحميل
  bool _isLoading = false;
  double _progress = 0.0;
  String _currentStep = '';
  String? _error;

  // البيانات المحلية
  UserSubscription? _subscription;
  List<Section> _sections = [];
  List<Subject> _subjects = [];
  List<VideoSection> _videoSections = [];
  List<VideoSubject> _videoSubjects = [];
  List<Unit> _units = [];
  List<Lesson> _lessons = [];
  List<Question> _questions = [];
  List<VideoUnit> _videoUnits = [];
  List<VideoLesson> _videoLessons = [];
  List<Video> _videos = [];

  DateTime? _lastUpdateTime;

  // Getters للحالة
  bool get isLoading => _isLoading;
  double get progress => _progress;
  String get currentStep => _currentStep;
  String? get error => _error;
  DateTime? get lastUpdateTime => _lastUpdateTime;

  // Getters للبيانات
  UserSubscription? get subscription => _subscription;
  List<Section> get sections => _sections;
  List<Subject> get subjects => _subjects;
  List<VideoSection> get videoSections => _videoSections;
  List<VideoSubject> get videoSubjects => _videoSubjects;
  List<Unit> get units => _units;
  List<Lesson> get lessons => _lessons;
  List<Question> get questions => _questions;
  List<VideoUnit> get videoUnits => _videoUnits;
  List<VideoLesson> get videoLessons => _videoLessons;
  List<Video> get videos => _videos;

  /// تهيئة الخدمة وتحميل البيانات المحلية
  Future<void> initialize() async {
    try {
      await _persistentStorage.initialize();
      await _loadLocalData();

      // 🔄 تحديث خدمة فرز البيانات من البيانات المحلية
      debugPrint('🔄 تحديث خدمة فرز البيانات من البيانات المحلية...');
      await DataDistributionService.instance.refreshAllData();

      debugPrint('✅ تم تهيئة خدمة القراءة الواحدة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة القراءة الواحدة: $e');
    }
  }

  /// التحديث اليدوي - القراءة الواحدة الوحيدة المسموحة
  Future<bool> performSingleRead() async {
    if (_isLoading) {
      debugPrint('⚠️ التحديث قيد التنفيذ بالفعل');
      return false;
    }

    try {
      _setLoading(true);
      _setError(null);

      debugPrint('🚀 بدء القراءة الواحدة الشاملة...');

      // الخطوة 1: الحصول على معرف الجهاز
      _updateProgress(0.1, 'الحصول على معرف الجهاز...');
      final deviceId = await _deviceService.getDeviceId();

      // الخطوة 2: قراءة واحدة شاملة من Firebase
      _updateProgress(0.2, 'تحميل جميع البيانات من الخادم...');
      final userData = await _performSingleFirebaseRead(deviceId);

      if (userData == null) {
        throw Exception('لا توجد بيانات للمستخدم');
      }

      // الخطوة 3: معالجة البيانات محلياً
      _updateProgress(0.5, 'معالجة البيانات...');
      await _processUserData(userData);

      // الخطوة 4: حفظ البيانات محلياً
      _updateProgress(0.8, 'حفظ البيانات محلياً...');
      await _saveAllDataLocally();

      // الخطوة 5: فهرسة البيانات
      _updateProgress(0.9, 'فهرسة البيانات...');
      _indexDataLocally();

      _updateProgress(1.0, 'تم الانتهاء بنجاح!');
      _lastUpdateTime = DateTime.now();

      debugPrint('✅ تم إنجاز القراءة الواحدة بنجاح');
      debugPrint('📊 إحصائيات البيانات المحملة:');
      debugPrint('   - الأقسام: ${_sections.length}');
      debugPrint('   - المواد: ${_subjects.length}');
      debugPrint('   - أقسام الفيديو: ${_videoSections.length}');
      debugPrint('   - مواد الفيديو: ${_videoSubjects.length}');
      debugPrint('   - الوحدات: ${_units.length}');
      debugPrint('   - الدروس: ${_lessons.length}');
      debugPrint('   - الأسئلة: ${_questions.length}');
      debugPrint('   - وحدات الفيديو: ${_videoUnits.length}');
      debugPrint('   - دروس الفيديو: ${_videoLessons.length}');
      debugPrint('   - الفيديوهات: ${_videos.length}');

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في القراءة الواحدة: $e');
      _setError('فشل في تحميل البيانات: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تنفيذ القراءة الواحدة من Firebase - هذه هي القراءة الوحيدة المسموحة
  Future<Map<String, dynamic>?> _performSingleFirebaseRead(
    String deviceId,
  ) async {
    try {
      debugPrint('📡 تنفيذ القراءة الواحدة من Firebase للجهاز: $deviceId');

      // الخطوة 1: تحميل البيانات العامة (جميع الأقسام والمواد)
      final publicData = await _loadPublicData(deviceId);

      // الخطوة 2: محاولة تحميل بيانات المستخدم المشترك
      final userDocSnapshot = await _firestore
          .collection('user_data')
          .doc(deviceId)
          .get();

      if (!userDocSnapshot.exists) {
        debugPrint(
          '⚠️ لا توجد بيانات اشتراك للمستخدم، عرض البيانات العامة فقط',
        );
        return publicData;
      }

      final userData = userDocSnapshot.data()!;
      debugPrint('✅ تم تحميل بيانات المستخدم المشترك');

      // الخطوة 3: دمج البيانات العامة مع بيانات المستخدم
      final mergedData = Map<String, dynamic>.from(publicData);

      // نسخ بيانات الاشتراك
      mergedData['subscription'] = userData['subscription'];

      // نسخ بيانات المواد المشترك بها (الوحدات، الدروس، الأسئلة، إلخ)
      mergedData['units'] = userData['units'] ?? [];
      mergedData['lessons'] = userData['lessons'] ?? [];
      mergedData['questions'] = userData['questions'] ?? [];
      mergedData['video_units'] = userData['video_units'] ?? [];
      mergedData['video_lessons'] = userData['video_lessons'] ?? [];
      mergedData['videos'] = userData['videos'] ?? [];

      // 🆕 تحميل وثائق المواد المدفوعة الجديدة
      debugPrint('🔍 بدء تحميل وثائق المواد المدفوعة...');
      await _loadPaidSubjectsDocuments(mergedData, userData);
      debugPrint('✅ انتهى تحميل وثائق المواد المدفوعة');

      debugPrint('✅ تم دمج البيانات العامة مع بيانات المستخدم');
      debugPrint('📊 البيانات المدمجة:');
      debugPrint('   - الأقسام: ${(mergedData['sections'] as List).length}');
      debugPrint('   - المواد: ${(mergedData['subjects'] as List).length}');
      debugPrint(
        '   - أقسام الفيديو: ${(mergedData['video_sections'] as List).length}',
      );
      debugPrint(
        '   - مواد الفيديو: ${(mergedData['video_subjects'] as List).length}',
      );
      debugPrint('   - الوحدات: ${(mergedData['units'] as List).length}');
      debugPrint('   - الدروس: ${(mergedData['lessons'] as List).length}');
      debugPrint('   - الأسئلة: ${(mergedData['questions'] as List).length}');
      debugPrint(
        '   - وحدات الفيديو: ${(mergedData['video_units'] as List).length}',
      );
      debugPrint(
        '   - دروس الفيديو: ${(mergedData['video_lessons'] as List).length}',
      );
      debugPrint('   - الفيديوهات: ${(mergedData['videos'] as List).length}');

      return mergedData;
    } catch (e) {
      debugPrint('❌ خطأ في القراءة من Firebase: $e');
      rethrow;
    }
  }

  /// تحميل وثائق المواد المدفوعة الجديدة
  Future<void> _loadPaidSubjectsDocuments(
    Map<String, dynamic> mergedData,
    Map<String, dynamic> userData,
  ) async {
    try {
      debugPrint('🔄 تحميل وثائق المواد المدفوعة الجديدة...');

      // الحصول على قائمة المواد المشترك بها
      debugPrint('🔍 فحص بيانات الاشتراك في userData...');
      debugPrint('📋 userData keys: ${userData.keys.toList()}');

      final subscription = userData['subscription'] as Map<String, dynamic>?;
      if (subscription == null) {
        debugPrint('⚠️ لا توجد بيانات اشتراك في userData');
        return;
      }

      debugPrint('✅ تم العثور على بيانات الاشتراك');
      debugPrint('📋 subscription keys: ${subscription.keys.toList()}');

      final subscribedSubjectIds = List<String>.from(
        subscription['subjectIds'] ?? [],
      );
      final subscribedVideoSubjectIds = List<String>.from(
        subscription['videoSubjectIds'] ?? [],
      );

      debugPrint('📋 المواد المشترك بها:');
      debugPrint('   - مواد الاختبارات: ${subscribedSubjectIds.length}');
      debugPrint('   - مواد الفيديوهات: ${subscribedVideoSubjectIds.length}');

      // تحميل وثائق مواد الاختبارات المدفوعة
      for (final subjectId in subscribedSubjectIds) {
        await _loadPaidSubjectDocument(mergedData, subjectId, false);
      }

      // تحميل وثائق مواد الفيديوهات المدفوعة
      for (final subjectId in subscribedVideoSubjectIds) {
        await _loadPaidSubjectDocument(mergedData, subjectId, true);
      }

      debugPrint('✅ تم تحميل جميع وثائق المواد المدفوعة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل وثائق المواد المدفوعة: $e');
    }
  }

  /// تحميل وثيقة مادة مدفوعة واحدة
  Future<void> _loadPaidSubjectDocument(
    Map<String, dynamic> mergedData,
    String subjectId,
    bool isVideo,
  ) async {
    try {
      final docId = isVideo ? 'video_subject_$subjectId' : 'subject_$subjectId';
      debugPrint('🔍 تحميل وثيقة: $docId');

      final docSnapshot = await _firestore
          .collection('optimized_unified_data')
          .doc(docId)
          .get();

      if (!docSnapshot.exists) {
        debugPrint('! الوثيقة غير موجودة: $docId');
        return;
      }

      final docData = docSnapshot.data()!;
      debugPrint('✅ تم تحميل وثيقة: $docId');

      // دمج البيانات مع البيانات الموجودة
      if (isVideo) {
        _mergeVideoData(mergedData, docData);
      } else {
        _mergeTestData(mergedData, docData);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل وثيقة $subjectId: $e');
    }
  }

  /// دمج بيانات الفيديوهات
  void _mergeVideoData(
    Map<String, dynamic> mergedData,
    Map<String, dynamic> docData,
  ) {
    final existingVideoUnits = mergedData['video_units'] as List;
    final existingVideoLessons = mergedData['video_lessons'] as List;
    final existingVideos = mergedData['videos'] as List;

    // قراءة البيانات من docData['data'] وليس docData مباشرة
    final dataSection = docData['data'] as Map<String, dynamic>? ?? {};
    final newVideoUnits = dataSection['video_units'] as List? ?? [];
    final newVideoLessons = dataSection['video_lessons'] as List? ?? [];
    final newVideos = dataSection['videos'] as List? ?? [];

    debugPrint('🔍 [DEBUG] _mergeVideoData:');
    debugPrint('   - وحدات فيديو جديدة: ${newVideoUnits.length}');
    debugPrint('   - دروس فيديو جديدة: ${newVideoLessons.length}');
    debugPrint('   - فيديوهات جديدة: ${newVideos.length}');

    existingVideoUnits.addAll(newVideoUnits);
    existingVideoLessons.addAll(newVideoLessons);
    existingVideos.addAll(newVideos);

    debugPrint('📹 تم دمج بيانات الفيديو:');
    debugPrint('   - وحدات فيديو جديدة: ${newVideoUnits.length}');
    debugPrint('   - دروس فيديو جديدة: ${newVideoLessons.length}');
    debugPrint('   - فيديوهات جديدة: ${newVideos.length}');
  }

  /// دمج بيانات الاختبارات
  void _mergeTestData(
    Map<String, dynamic> mergedData,
    Map<String, dynamic> docData,
  ) {
    final existingUnits = mergedData['units'] as List;
    final existingLessons = mergedData['lessons'] as List;
    final existingQuestions = mergedData['questions'] as List;

    // قراءة البيانات من docData['data'] وليس docData مباشرة
    final dataSection = docData['data'] as Map<String, dynamic>? ?? {};
    final newUnits = dataSection['units'] as List? ?? [];
    final newLessons = dataSection['lessons'] as List? ?? [];
    final newQuestions = dataSection['questions'] as List? ?? [];

    existingUnits.addAll(newUnits);
    existingLessons.addAll(newLessons);
    existingQuestions.addAll(newQuestions);

    debugPrint('📝 تم دمج بيانات الاختبارات:');
    debugPrint('   - وحدات جديدة: ${newUnits.length}');
    debugPrint('   - دروس جديدة: ${newLessons.length}');
    debugPrint('   - أسئلة جديدة: ${newQuestions.length}');
  }

  /// تحميل البيانات العامة من المجموعات الأصلية
  Future<Map<String, dynamic>> _loadPublicData(String deviceId) async {
    try {
      debugPrint('📡 تحميل البيانات العامة من Firebase...');

      // محاولة تحميل البيانات من الوثيقة الموحدة أولاً (قراءة واحدة حقيقية)
      try {
        final unifiedDataSnapshot = await _firestore
            .collection('optimized_unified_data')
            .doc('general_data')
            .get();

        if (unifiedDataSnapshot.exists) {
          debugPrint('✅ تم العثور على البيانات الموحدة - قراءة واحدة فقط!');
          final unifiedData = unifiedDataSnapshot.data()!;

          // تحويل البيانات الموحدة من التنسيق الصحيح
          final dataSection =
              unifiedData['data'] as Map<String, dynamic>? ?? {};

          final sections =
              (dataSection['test_sections'] as List<dynamic>? ?? [])
                  .map(
                    (item) =>
                        _convertFirestoreData(item as Map<String, dynamic>),
                  )
                  .toList();
          final subjects =
              (dataSection['test_subjects'] as List<dynamic>? ?? [])
                  .map(
                    (item) =>
                        _convertFirestoreData(item as Map<String, dynamic>),
                  )
                  .toList();
          final videoSections =
              (dataSection['video_sections'] as List<dynamic>? ?? [])
                  .map(
                    (item) =>
                        _convertFirestoreData(item as Map<String, dynamic>),
                  )
                  .toList();
          final videoSubjects =
              (dataSection['video_subjects'] as List<dynamic>? ?? [])
                  .map(
                    (item) =>
                        _convertFirestoreData(item as Map<String, dynamic>),
                  )
                  .toList();

          debugPrint('✅ تم تحميل البيانات بقراءة واحدة:');
          debugPrint('   - الأقسام: ${sections.length}');
          debugPrint('   - المواد: ${subjects.length}');
          debugPrint('   - أقسام الفيديو: ${videoSections.length}');
          debugPrint('   - مواد الفيديو: ${videoSubjects.length}');

          return {
            'subscription': {
              'isActive': false,
              'subjectIds': <String>[],
              'videoSubjectIds': <String>[],
              'activatedAt': null,
              'expiresAt': null,
            },
            'sections': sections,
            'subjects': subjects,
            'video_sections': videoSections,
            'video_subjects': videoSubjects,
            'units': <Map<String, dynamic>>[],
            'lessons': <Map<String, dynamic>>[],
            'questions': <Map<String, dynamic>>[],
            'video_units': <Map<String, dynamic>>[],
            'video_lessons': <Map<String, dynamic>>[],
            'videos': <Map<String, dynamic>>[],
            'lastUpdated': DateTime.now().millisecondsSinceEpoch,
          };
        }
      } catch (e) {
        debugPrint('⚠️ البيانات الموحدة غير متوفرة، استخدام النظام القديم: $e');
      }

      // النظام القديم (متعدد القراءات) - كـ fallback
      debugPrint('📡 استخدام النظام القديم - قراءات متعددة...');

      // تحميل الأقسام
      final sectionsSnapshot = await _firestore.collection('sections').get();
      final sections = sectionsSnapshot.docs
          .map((doc) => _convertFirestoreData({'id': doc.id, ...doc.data()}))
          .toList();

      // تحميل المواد
      final subjectsSnapshot = await _firestore.collection('subjects').get();
      final subjects = subjectsSnapshot.docs
          .map((doc) => _convertFirestoreData({'id': doc.id, ...doc.data()}))
          .toList();

      // تحميل أقسام الفيديو
      final videoSectionsSnapshot = await _firestore
          .collection('video_sections')
          .get();
      final videoSections = videoSectionsSnapshot.docs
          .map((doc) => _convertFirestoreData({'id': doc.id, ...doc.data()}))
          .toList();

      // تحميل مواد الفيديو
      final videoSubjectsSnapshot = await _firestore
          .collection('video_subjects')
          .get();
      final videoSubjects = videoSubjectsSnapshot.docs
          .map((doc) => _convertFirestoreData({'id': doc.id, ...doc.data()}))
          .toList();

      debugPrint('✅ تم تحميل البيانات العامة (النظام القديم):');
      debugPrint('   - الأقسام: ${sections.length}');
      debugPrint('   - المواد: ${subjects.length}');
      debugPrint('   - أقسام الفيديو: ${videoSections.length}');
      debugPrint('   - مواد الفيديو: ${videoSubjects.length}');

      return {
        'subscription': {
          'isActive': false,
          'subjectIds': <String>[],
          'videoSubjectIds': <String>[],
          'activatedAt': null,
          'expiresAt': null,
        },
        'sections': sections,
        'subjects': subjects,
        'video_sections': videoSections,
        'video_subjects': videoSubjects,
        'units': <Map<String, dynamic>>[],
        'lessons': <Map<String, dynamic>>[],
        'questions': <Map<String, dynamic>>[],
        'video_units': <Map<String, dynamic>>[],
        'video_lessons': <Map<String, dynamic>>[],
        'videos': <Map<String, dynamic>>[],
        'lastUpdated': DateTime.now().millisecondsSinceEpoch,
      };
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات العامة: $e');
      rethrow;
    }
  }

  /// تحويل بيانات Firestore إلى تنسيق محلي
  Map<String, dynamic> _convertFirestoreData(Map<String, dynamic> data) {
    final converted = <String, dynamic>{};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      converted[key] = _convertValue(value);
    }

    return converted;
  }

  /// تحويل قيمة واحدة (يدعم التداخل)
  dynamic _convertValue(dynamic value) {
    if (value == null) return null;

    // تحويل Timestamp إلى int (milliseconds)
    if (value.runtimeType.toString().contains('Timestamp')) {
      try {
        final timestamp = value as Timestamp;
        return timestamp.millisecondsSinceEpoch;
      } catch (e) {
        debugPrint('خطأ في تحويل Timestamp: $e');
        return DateTime.now().millisecondsSinceEpoch;
      }
    }

    // تحويل Map متداخل
    if (value is Map<String, dynamic>) {
      return _convertFirestoreData(value);
    }

    // تحويل List
    if (value is List) {
      return value.map((item) => _convertValue(item)).toList();
    }

    // إرجاع القيم الأخرى كما هي
    return value;
  }

  /// معالجة بيانات المستخدم المحملة
  Future<void> _processUserData(Map<String, dynamic> userData) async {
    try {
      debugPrint('🔍 [DEBUG] _processUserData() بدء معالجة بيانات المستخدم...');

      // تحويل جميع Timestamp إلى تنسيق صحيح قبل المعالجة
      final convertedUserData = _convertFirestoreData(userData);
      debugPrint('🔄 [DEBUG] تم تحويل البيانات من Firestore');

      // معالجة الاشتراك
      final subscriptionData =
          convertedUserData['subscription'] as Map<String, dynamic>?;
      if (subscriptionData != null) {
        debugPrint('🔍 [DEBUG] معالجة بيانات الاشتراك...');
        _subscription = UserSubscription.fromMap({
          ...subscriptionData,
          'deviceId': await _deviceService.getDeviceId(),
        });
        debugPrint('✅ [DEBUG] تم إنشاء UserSubscription بنجاح');
      }

      // معالجة الأقسام
      final sectionsData =
          convertedUserData['sections'] as List<dynamic>? ?? [];
      _sections = sectionsData
          .map((data) => Section.fromLocalMap(data as Map<String, dynamic>))
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_sections.length} قسم');

      // معالجة المواد
      final subjectsData =
          convertedUserData['subjects'] as List<dynamic>? ?? [];
      _subjects = subjectsData
          .map((data) => Subject.fromLocalMap(data as Map<String, dynamic>))
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_subjects.length} مادة');

      // معالجة أقسام الفيديو
      final videoSectionsData =
          convertedUserData['video_sections'] as List<dynamic>? ?? [];
      _videoSections = videoSectionsData
          .map(
            (data) => VideoSection.fromLocalMap(data as Map<String, dynamic>),
          )
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_videoSections.length} قسم فيديو');

      // معالجة مواد الفيديو مع تصحيح خاصية isFree
      final videoSubjectsData =
          convertedUserData['video_subjects'] as List<dynamic>? ?? [];
      _videoSubjects = videoSubjectsData.map((data) {
        final subjectData = data as Map<String, dynamic>;
        final sectionId = subjectData['sectionId'] as String? ?? '';

        // البحث عن القسم المرتبط لتحديد isFree
        final relatedSection = _videoSections.firstWhere(
          (section) => section.id == sectionId,
          orElse: () => VideoSection(
            id: '',
            name: '',
            description: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            createdByAdminId: '',
            isFree: false,
          ),
        );

        // تصحيح خاصية isFree بناءً على القسم
        final correctedData = Map<String, dynamic>.from(subjectData);
        correctedData['isFree'] = relatedSection.isFree;

        debugPrint(
          '🔧 تصحيح مادة فيديو: ${subjectData['name']} - isFree: ${subjectData['isFree']} → ${relatedSection.isFree}',
        );

        return VideoSubject.fromLocalMap(correctedData);
      }).toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_videoSubjects.length} مادة فيديو');

      // معالجة الوحدات
      final unitsData = convertedUserData['units'] as List<dynamic>? ?? [];
      _units = unitsData
          .map((data) => Unit.fromLocalMap(data as Map<String, dynamic>))
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_units.length} وحدة');

      // معالجة الدروس
      final lessonsData = convertedUserData['lessons'] as List<dynamic>? ?? [];
      _lessons = lessonsData
          .map((data) => Lesson.fromLocalMap(data as Map<String, dynamic>))
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_lessons.length} درس');

      // معالجة الأسئلة
      final questionsData =
          convertedUserData['questions'] as List<dynamic>? ?? [];
      _questions = questionsData
          .map((data) => Question.fromLocalMap(data as Map<String, dynamic>))
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_questions.length} سؤال');

      // معالجة وحدات الفيديو
      final videoUnitsData =
          convertedUserData['video_units'] as List<dynamic>? ?? [];
      _videoUnits = videoUnitsData
          .map((data) => VideoUnit.fromLocalMap(data as Map<String, dynamic>))
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_videoUnits.length} وحدة فيديو');

      // معالجة دروس الفيديو
      final videoLessonsData =
          convertedUserData['video_lessons'] as List<dynamic>? ?? [];
      debugPrint(
        '🔍 [DEBUG] دروس الفيديو في convertedUserData: ${videoLessonsData.length}',
      );
      _videoLessons = videoLessonsData
          .map((data) => VideoLesson.fromLocalMap(data as Map<String, dynamic>))
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_videoLessons.length} درس فيديو');

      // معالجة الفيديوهات
      final videosData = convertedUserData['videos'] as List<dynamic>? ?? [];
      _videos = videosData
          .map((data) => Video.fromLocalMap(data as Map<String, dynamic>))
          .toList();
      debugPrint('✅ [DEBUG] تم معالجة ${_videos.length} فيديو');

      debugPrint('✅ تم معالجة جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في معالجة بيانات المستخدم: $e');
      rethrow;
    }
  }

  /// تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (!loading) {
      _progress = 0.0;
      _currentStep = '';
    }
    notifyListeners();
  }

  /// تحديث التقدم
  void _updateProgress(double progress, String step) {
    _progress = progress;
    _currentStep = step;
    debugPrint('📊 التقدم: ${(progress * 100).toInt()}% - $step');
    notifyListeners();
  }

  /// تعيين خطأ
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// تحميل البيانات المحلية
  Future<void> _loadLocalData() async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // تحميل الاشتراك
      final subscriptionFile = File(
        '${storageDir.path}/user_subscription.json',
      );
      if (await subscriptionFile.exists()) {
        final content = await subscriptionFile.readAsString();
        final data = jsonDecode(content) as Map<String, dynamic>;
        _subscription = UserSubscription.fromMap(data);
      }

      // تحميل الأقسام
      final sectionsFile = File('${storageDir.path}/sections.json');
      if (await sectionsFile.exists()) {
        final content = await sectionsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _sections = data.map((item) => Section.fromLocalMap(item)).toList();
      }

      // تحميل المواد
      final subjectsFile = File('${storageDir.path}/subjects.json');
      if (await subjectsFile.exists()) {
        final content = await subjectsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _subjects = data.map((item) => Subject.fromLocalMap(item)).toList();
      }

      // تحميل أقسام الفيديو
      final videoSectionsFile = File('${storageDir.path}/video_sections.json');
      if (await videoSectionsFile.exists()) {
        final content = await videoSectionsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videoSections = data
            .map((item) => VideoSection.fromLocalMap(item))
            .toList();
      }

      // تحميل مواد الفيديو مع تصحيح خاصية isFree
      final videoSubjectsFile = File('${storageDir.path}/video_subjects.json');
      if (await videoSubjectsFile.exists()) {
        final content = await videoSubjectsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videoSubjects = data.map((item) {
          final subjectData = item as Map<String, dynamic>;
          final sectionId = subjectData['sectionId'] as String? ?? '';

          // البحث عن القسم المرتبط لتحديد isFree
          final relatedSection = _videoSections.firstWhere(
            (section) => section.id == sectionId,
            orElse: () => VideoSection(
              id: '',
              name: '',
              description: '',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              createdByAdminId: '',
              isFree: false,
            ),
          );

          // تصحيح خاصية isFree بناءً على القسم
          final correctedData = Map<String, dynamic>.from(subjectData);
          correctedData['isFree'] = relatedSection.isFree;

          return VideoSubject.fromLocalMap(correctedData);
        }).toList();
      }

      // تحميل الوحدات
      final unitsFile = File('${storageDir.path}/units.json');
      if (await unitsFile.exists()) {
        final content = await unitsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _units = data.map((item) => Unit.fromLocalMap(item)).toList();
      }

      // تحميل الدروس
      final lessonsFile = File('${storageDir.path}/lessons.json');
      if (await lessonsFile.exists()) {
        final content = await lessonsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _lessons = data.map((item) => Lesson.fromLocalMap(item)).toList();
      }

      // تحميل الأسئلة
      final questionsFile = File('${storageDir.path}/questions.json');
      if (await questionsFile.exists()) {
        final content = await questionsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _questions = data.map((item) => Question.fromLocalMap(item)).toList();
      }

      // تحميل وحدات الفيديو
      final videoUnitsFile = File('${storageDir.path}/video_units.json');
      if (await videoUnitsFile.exists()) {
        final content = await videoUnitsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videoUnits = data.map((item) => VideoUnit.fromLocalMap(item)).toList();
      }

      // تحميل دروس الفيديو
      final videoLessonsFile = File('${storageDir.path}/video_lessons.json');
      if (await videoLessonsFile.exists()) {
        final content = await videoLessonsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videoLessons = data
            .map((item) => VideoLesson.fromLocalMap(item))
            .toList();
        debugPrint(
          '📁 تم تحميل ${_videoLessons.length} درس فيديو من الملف المحلي',
        );
      } else {
        debugPrint('📁 ملف دروس الفيديو غير موجود محلياً');
      }

      // تحميل الفيديوهات
      final videosFile = File('${storageDir.path}/videos.json');
      if (await videosFile.exists()) {
        final content = await videosFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videos = data.map((item) => Video.fromLocalMap(item)).toList();
      }

      // تحميل وقت آخر تحديث
      final lastUpdateFile = File('${storageDir.path}/last_update.txt');
      if (await lastUpdateFile.exists()) {
        final content = await lastUpdateFile.readAsString();
        _lastUpdateTime = DateTime.tryParse(content);
      }

      // تحميل البيانات المفعلة من SharedPreferences (بعد تحميل البيانات العادية)
      await _loadActivatedFreeData();

      debugPrint('✅ تم تحميل البيانات المحلية بنجاح');
      debugPrint('📊 البيانات المحلية المحملة:');
      debugPrint('   - الأقسام: ${_sections.length}');
      debugPrint('   - المواد: ${_subjects.length}');
      debugPrint('   - أقسام الفيديو: ${_videoSections.length}');
      debugPrint('   - مواد الفيديو: ${_videoSubjects.length}');
      debugPrint('   - الوحدات: ${_units.length}');
      debugPrint('   - الدروس: ${_lessons.length}');
      debugPrint('   - الأسئلة: ${_questions.length}');
      debugPrint('   - وحدات الفيديو: ${_videoUnits.length}');
      debugPrint('   - دروس الفيديو: ${_videoLessons.length}');
      debugPrint('   - الفيديوهات: ${_videos.length}');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المحلية: $e');
    }
  }

  /// تحميل البيانات المفعلة من SharedPreferences
  Future<void> _loadActivatedFreeData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      debugPrint(
        '🔍 [DEBUG] البحث عن البيانات المفعلة في SharedPreferences...',
      );
      debugPrint('   - عدد المفاتيح الإجمالي: ${keys.length}');

      // البحث عن الوثائق المفعلة (تبدأ بـ free_subject_)
      final freeSubjectKeys = keys
          .where((key) => key.startsWith('free_subject_'))
          .toList();
      debugPrint('   - عدد الوثائق المفعلة: ${freeSubjectKeys.length}');

      for (final key in freeSubjectKeys) {
        try {
          final dataString = prefs.getString(key);
          if (dataString != null) {
            final data = jsonDecode(dataString) as Map<String, dynamic>;
            debugPrint('🔄 [DEBUG] تحميل وثيقة مفعلة: $key');

            // دمج البيانات المفعلة مع البيانات الموجودة
            await _mergeActivatedData(data);
          }
        } catch (e) {
          debugPrint('❌ خطأ في تحميل الوثيقة المفعلة $key: $e');
        }
      }

      debugPrint('✅ تم تحميل جميع البيانات المفعلة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المفعلة: $e');
    }
  }

  /// دمج البيانات المفعلة مع البيانات الموجودة
  Future<void> _mergeActivatedData(Map<String, dynamic> activatedData) async {
    try {
      if (activatedData['data'] == null) return;

      final data = activatedData['data'] as Map<String, dynamic>;
      debugPrint('🔄 [DEBUG] دمج البيانات المفعلة...');
      debugPrint('   - المفاتيح المتاحة: ${data.keys.toList()}');

      // دمج الوحدات
      if (data['units'] != null) {
        final units = (data['units'] as List)
            .map((item) => Unit.fromLocalMap(item))
            .toList();
        _units.addAll(units);
        debugPrint('   - تم إضافة ${units.length} وحدة');
        for (final unit in units) {
          debugPrint(
            '     * وحدة مفعلة: ${unit.name} (subjectId: ${unit.subjectId})',
          );
        }
      }

      // دمج الدروس
      if (data['lessons'] != null) {
        final lessons = (data['lessons'] as List)
            .map((item) => Lesson.fromLocalMap(item))
            .toList();
        _lessons.addAll(lessons);
        debugPrint('   - تم إضافة ${lessons.length} درس');
        for (final lesson in lessons) {
          debugPrint(
            '     * درس مفعل: ${lesson.name} (subjectId: ${lesson.subjectId})',
          );
        }
      }

      // دمج الأسئلة
      if (data['questions'] != null) {
        final questions = (data['questions'] as List)
            .map((item) => Question.fromLocalMap(item))
            .toList();
        _questions.addAll(questions);
        debugPrint('   - تم إضافة ${questions.length} سؤال');
      }

      // دمج وحدات الفيديو
      if (data['video_units'] != null) {
        final videoUnits = (data['video_units'] as List)
            .map((item) => VideoUnit.fromLocalMap(item))
            .toList();
        _videoUnits.addAll(videoUnits);
        debugPrint('   - تم إضافة ${videoUnits.length} وحدة فيديو');
      }

      // دمج دروس الفيديو
      if (data['video_lessons'] != null) {
        final videoLessons = (data['video_lessons'] as List)
            .map((item) => VideoLesson.fromLocalMap(item))
            .toList();
        _videoLessons.addAll(videoLessons);
        debugPrint('   - تم إضافة ${videoLessons.length} درس فيديو');
      }

      // دمج الفيديوهات
      if (data['videos'] != null) {
        final videos = (data['videos'] as List)
            .map((item) => Video.fromLocalMap(item))
            .toList();
        _videos.addAll(videos);
        debugPrint('   - تم إضافة ${videos.length} فيديو');
      }

      debugPrint('✅ تم دمج البيانات المفعلة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في دمج البيانات المفعلة: $e');
    }
  }

  /// حفظ جميع البيانات محلياً
  Future<void> _saveAllDataLocally() async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // حفظ الاشتراك
      if (_subscription != null) {
        final subscriptionFile = File(
          '${storageDir.path}/user_subscription.json',
        );
        await subscriptionFile.writeAsString(
          jsonEncode(_subscription!.toMap()),
        );
      }

      // حفظ الأقسام
      final sectionsFile = File('${storageDir.path}/sections.json');
      final sectionsData = _sections.map((item) => item.toLocalMap()).toList();
      await sectionsFile.writeAsString(jsonEncode(sectionsData));

      // حفظ المواد
      final subjectsFile = File('${storageDir.path}/subjects.json');
      final subjectsData = _subjects.map((item) => item.toLocalMap()).toList();
      await subjectsFile.writeAsString(jsonEncode(subjectsData));

      // حفظ أقسام الفيديو
      final videoSectionsFile = File('${storageDir.path}/video_sections.json');
      final videoSectionsData = _videoSections
          .map((item) => item.toLocalMap())
          .toList();
      await videoSectionsFile.writeAsString(jsonEncode(videoSectionsData));

      // حفظ مواد الفيديو
      final videoSubjectsFile = File('${storageDir.path}/video_subjects.json');
      final videoSubjectsData = _videoSubjects
          .map((item) => item.toLocalMap())
          .toList();
      await videoSubjectsFile.writeAsString(jsonEncode(videoSubjectsData));

      // حفظ الوحدات
      final unitsFile = File('${storageDir.path}/units.json');
      final unitsData = _units.map((item) => item.toLocalMap()).toList();
      await unitsFile.writeAsString(jsonEncode(unitsData));

      // حفظ الدروس
      final lessonsFile = File('${storageDir.path}/lessons.json');
      final lessonsData = _lessons.map((item) => item.toLocalMap()).toList();
      await lessonsFile.writeAsString(jsonEncode(lessonsData));

      // حفظ الأسئلة
      final questionsFile = File('${storageDir.path}/questions.json');
      final questionsData = _questions
          .map((item) => item.toLocalMap())
          .toList();
      await questionsFile.writeAsString(jsonEncode(questionsData));

      // حفظ وحدات الفيديو
      final videoUnitsFile = File('${storageDir.path}/video_units.json');
      final videoUnitsData = _videoUnits
          .map((item) => item.toLocalMap())
          .toList();
      await videoUnitsFile.writeAsString(jsonEncode(videoUnitsData));

      // حفظ دروس الفيديو
      final videoLessonsFile = File('${storageDir.path}/video_lessons.json');
      final videoLessonsData = _videoLessons
          .map((item) => item.toLocalMap())
          .toList();
      await videoLessonsFile.writeAsString(jsonEncode(videoLessonsData));

      // حفظ الفيديوهات
      final videosFile = File('${storageDir.path}/videos.json');
      final videosData = _videos.map((item) => item.toLocalMap()).toList();
      await videosFile.writeAsString(jsonEncode(videosData));

      // حفظ وقت آخر تحديث
      final lastUpdateFile = File('${storageDir.path}/last_update.txt');
      await lastUpdateFile.writeAsString(DateTime.now().toIso8601String());

      // 🔄 حفظ البيانات العامة لخدمة فرز البيانات
      await _saveGeneralDataForDistribution();

      debugPrint('✅ تم حفظ جميع البيانات محلياً بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات محلياً: $e');
      rethrow;
    }
  }

  /// حفظ البيانات العامة لخدمة فرز البيانات
  Future<void> _saveGeneralDataForDistribution() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // إنشاء البيانات العامة بنفس تنسيق OptimizedUnifiedDataService
      final generalData = {
        'version': DateTime.now().millisecondsSinceEpoch,
        'lastUpdated': DateTime.now().millisecondsSinceEpoch,
        'data': {
          // أقسام الاختبارات
          'test_sections': _sections.map((s) => s.toLocalMap()).toList(),
          'test_subjects': _subjects.map((s) => s.toLocalMap()).toList(),

          // أقسام الفيديوهات
          'video_sections': _videoSections.map((s) => s.toLocalMap()).toList(),
          'video_subjects': _videoSubjects.map((s) => s.toLocalMap()).toList(),
        },
      };

      // حفظ البيانات تحت مفتاح general_data
      final dataString = jsonEncode(generalData);
      await prefs.setString('general_data', dataString);

      debugPrint('💾 تم حفظ البيانات العامة لخدمة فرز البيانات');
      debugPrint('   - أقسام الاختبارات: ${_sections.length}');
      debugPrint('   - مواد الاختبارات: ${_subjects.length}');
      debugPrint('   - أقسام الفيديوهات: ${_videoSections.length}');
      debugPrint('   - مواد الفيديوهات: ${_videoSubjects.length}');

      // 🔄 تحديث خدمة فرز البيانات فوراً
      debugPrint('🔄 تحديث خدمة فرز البيانات بعد حفظ البيانات العامة...');
      await DataDistributionService.instance.refreshAllData();
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات العامة لخدمة فرز البيانات: $e');
    }
  }

  /// فهرسة البيانات محلياً للوصول السريع
  void _indexDataLocally() {
    try {
      debugPrint('🔍 بدء فهرسة البيانات محلياً...');

      // فهرسة المواد حسب القسم
      final subjectsBySection = <String, List<Subject>>{};
      for (final subject in _subjects) {
        if (!subjectsBySection.containsKey(subject.sectionId)) {
          subjectsBySection[subject.sectionId] = [];
        }
        subjectsBySection[subject.sectionId]!.add(subject);
      }

      // فهرسة مواد الفيديو حسب القسم
      final videoSubjectsBySection = <String, List<VideoSubject>>{};
      for (final videoSubject in _videoSubjects) {
        if (!videoSubjectsBySection.containsKey(videoSubject.sectionId)) {
          videoSubjectsBySection[videoSubject.sectionId] = [];
        }
        videoSubjectsBySection[videoSubject.sectionId]!.add(videoSubject);
      }

      // فهرسة الوحدات حسب المادة
      final unitsBySubject = <String, List<Unit>>{};
      for (final unit in _units) {
        if (!unitsBySubject.containsKey(unit.subjectId)) {
          unitsBySubject[unit.subjectId] = [];
        }
        unitsBySubject[unit.subjectId]!.add(unit);
      }

      // فهرسة الدروس حسب الوحدة
      final lessonsByUnit = <String, List<Lesson>>{};
      for (final lesson in _lessons) {
        if (!lessonsByUnit.containsKey(lesson.unitId)) {
          lessonsByUnit[lesson.unitId] = [];
        }
        lessonsByUnit[lesson.unitId]!.add(lesson);
      }

      // فهرسة الأسئلة حسب الدرس
      final questionsByLesson = <String, List<Question>>{};
      for (final question in _questions) {
        if (!questionsByLesson.containsKey(question.lessonId)) {
          questionsByLesson[question.lessonId] = [];
        }
        questionsByLesson[question.lessonId]!.add(question);
      }

      // فهرسة وحدات الفيديو حسب المادة
      final videoUnitsBySubject = <String, List<VideoUnit>>{};
      for (final videoUnit in _videoUnits) {
        if (!videoUnitsBySubject.containsKey(videoUnit.subjectId)) {
          videoUnitsBySubject[videoUnit.subjectId] = [];
        }
        videoUnitsBySubject[videoUnit.subjectId]!.add(videoUnit);
      }

      // فهرسة دروس الفيديو حسب الوحدة
      final videoLessonsByUnit = <String, List<VideoLesson>>{};
      for (final videoLesson in _videoLessons) {
        if (!videoLessonsByUnit.containsKey(videoLesson.unitId)) {
          videoLessonsByUnit[videoLesson.unitId] = [];
        }
        videoLessonsByUnit[videoLesson.unitId]!.add(videoLesson);
      }

      // فهرسة الفيديوهات حسب الدرس
      final videosByLesson = <String, List<Video>>{};
      for (final video in _videos) {
        if (!videosByLesson.containsKey(video.lessonId)) {
          videosByLesson[video.lessonId] = [];
        }
        videosByLesson[video.lessonId]!.add(video);
      }

      debugPrint('✅ تم إنجاز فهرسة البيانات محلياً');
      debugPrint('📊 إحصائيات الفهرسة:');
      debugPrint('   - المواد حسب القسم: ${subjectsBySection.length} قسم');
      debugPrint(
        '   - مواد الفيديو حسب القسم: ${videoSubjectsBySection.length} قسم',
      );
      debugPrint('   - الوحدات حسب المادة: ${unitsBySubject.length} مادة');
      debugPrint('   - الدروس حسب الوحدة: ${lessonsByUnit.length} وحدة');
      debugPrint('   - الأسئلة حسب الدرس: ${questionsByLesson.length} درس');
      debugPrint(
        '   - وحدات الفيديو حسب المادة: ${videoUnitsBySubject.length} مادة',
      );
      debugPrint(
        '   - دروس الفيديو حسب الوحدة: ${videoLessonsByUnit.length} وحدة',
      );
      debugPrint('   - الفيديوهات حسب الدرس: ${videosByLesson.length} درس');
    } catch (e) {
      debugPrint('❌ خطأ في فهرسة البيانات: $e');
    }
  }

  // ==================== دوال الوصول للبيانات المفهرسة ====================

  /// الحصول على المواد حسب القسم
  List<Subject> getSubjectsBySection(String sectionId) {
    return _subjects
        .where((subject) => subject.sectionId == sectionId && subject.isActive)
        .toList();
  }

  /// الحصول على مواد الفيديو حسب القسم
  List<VideoSubject> getVideoSubjectsBySection(String sectionId) {
    return _videoSubjects
        .where(
          (videoSubject) =>
              videoSubject.sectionId == sectionId && videoSubject.isActive,
        )
        .toList();
  }

  /// الحصول على الوحدات حسب المادة
  List<Unit> getUnitsBySubject(String subjectId) {
    return _units
        .where((unit) => unit.subjectId == subjectId && unit.isActive)
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على الدروس حسب الوحدة
  List<Lesson> getLessonsByUnit(String unitId) {
    return _lessons
        .where((lesson) => lesson.unitId == unitId && lesson.isActive)
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على الأسئلة حسب الدرس
  List<Question> getQuestionsByLesson(String lessonId) {
    return _questions
        .where((question) => question.lessonId == lessonId && question.isActive)
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على وحدات الفيديو حسب المادة
  List<VideoUnit> getVideoUnitsBySubject(String subjectId) {
    return _videoUnits
        .where(
          (videoUnit) => videoUnit.subjectId == subjectId && videoUnit.isActive,
        )
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على دروس الفيديو حسب الوحدة
  List<VideoLesson> getVideoLessonsByUnit(String unitId) {
    return _videoLessons
        .where(
          (videoLesson) => videoLesson.unitId == unitId && videoLesson.isActive,
        )
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على الفيديوهات حسب الدرس
  List<Video> getVideosByLesson(String lessonId) {
    return _videos
        .where((video) => video.lessonId == lessonId && video.isActive)
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على الأسئلة حسب المادة
  List<Question> getQuestionsBySubject(String subjectId) {
    return _questions
        .where(
          (question) => question.subjectId == subjectId && question.isActive,
        )
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على الأسئلة حسب الوحدة
  List<Question> getQuestionsByUnit(String unitId) {
    return _questions
        .where((question) => question.unitId == unitId && question.isActive)
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على أسئلة الدورات حسب المادة
  List<Question> getCourseQuestionsBySubject(String subjectId) {
    return _questions
        .where(
          (question) =>
              question.subjectId == subjectId &&
              question.isActive &&
              question.isCourseQuestion,
        )
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على أسئلة الدورات حسب الوحدة
  List<Question> getCourseQuestionsByUnit(String unitId) {
    return _questions
        .where(
          (question) =>
              question.unitId == unitId &&
              question.isActive &&
              question.isCourseQuestion,
        )
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على أسئلة الدورات حسب الدرس
  List<Question> getCourseQuestionsByLesson(String lessonId) {
    return _questions
        .where(
          (question) =>
              question.lessonId == lessonId &&
              question.isActive &&
              question.isCourseQuestion,
        )
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// التحقق من الاشتراك في مادة
  bool isSubscribedToSubject(String subjectId) {
    return _subscription?.subscribedSubjectIds.contains(subjectId) ?? false;
  }

  /// التحقق من الاشتراك في مادة فيديو
  bool isSubscribedToVideoSubject(String videoSubjectId) {
    return _subscription?.videoSubjectIds.contains(videoSubjectId) ?? false;
  }

  /// الحصول على المواد المشترك بها فقط
  List<Subject> getSubscribedSubjects() {
    if (_subscription == null || !_subscription!.isActive) {
      return [];
    }
    return _subjects
        .where(
          (subject) =>
              _subscription!.subscribedSubjectIds.contains(subject.id) &&
              subject.isActive,
        )
        .toList();
  }

  /// الحصول على مواد الفيديو المشترك بها فقط
  List<VideoSubject> getSubscribedVideoSubjects() {
    if (_subscription == null || !_subscription!.isActive) {
      return [];
    }
    return _videoSubjects
        .where(
          (videoSubject) =>
              _subscription!.videoSubjectIds.contains(videoSubject.id) &&
              videoSubject.isActive,
        )
        .toList();
  }

  /// الحصول على الأقسام المجانية
  List<Section> getFreeTestSections() {
    return _sections
        .where((section) => section.isFree && section.isActive)
        .toList();
  }

  /// الحصول على الأقسام المدفوعة
  List<Section> getPaidTestSections() {
    return _sections
        .where((section) => !section.isFree && section.isActive)
        .toList();
  }

  /// الحصول على أقسام الفيديو المجانية
  List<VideoSection> getFreeVideoSections() {
    return _videoSections
        .where((section) => section.isFree && section.isActive)
        .toList();
  }

  /// الحصول على أقسام الفيديو المدفوعة
  List<VideoSection> getPaidVideoSections() {
    return _videoSections
        .where((section) => !section.isFree && section.isActive)
        .toList();
  }

  /// مسح جميع البيانات المحلية
  Future<void> clearAllLocalData() async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // حذف جميع ملفات البيانات
      final files = [
        'user_subscription.json',
        'sections.json',
        'subjects.json',
        'video_sections.json',
        'video_subjects.json',
        'units.json',
        'lessons.json',
        'questions.json',
        'video_units.json',
        'video_lessons.json',
        'videos.json',
        'last_update.txt',
      ];

      for (final fileName in files) {
        final file = File('${storageDir.path}/$fileName');
        if (await file.exists()) {
          await file.delete();
        }
      }

      // مسح البيانات من الذاكرة
      _subscription = null;
      _sections.clear();
      _subjects.clear();
      _videoSections.clear();
      _videoSubjects.clear();
      _units.clear();
      _lessons.clear();
      _questions.clear();
      _videoUnits.clear();
      _videoLessons.clear();
      _videos.clear();
      _lastUpdateTime = null;

      debugPrint('✅ تم مسح جميع البيانات المحلية');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في مسح البيانات المحلية: $e');
    }
  }

  /// تحميل البيانات باستخدام نظام المراجع المحسن
  /// يحمل جميع البيانات بقراءة واحدة مع توفير 95%+ من المساحة
  Future<bool> loadUnifiedUserDataWithReferences() async {
    try {
      final deviceId = await _deviceService.getDeviceId();
      debugPrint('🚀 تحميل البيانات بنظام المراجع المحسن: $deviceId');

      // تحميل البيانات باستخدام نظام المراجع
      final userData = await _referenceLoader.loadUserDataWithReferences(
        deviceId,
      );

      if (userData == null) {
        debugPrint(
          'ℹ️ لا توجد بيانات موحدة للمستخدم - سيتم استخدام النظام القديم',
        );
        return false;
      }

      // طباعة إحصائيات توفير المساحة
      _referenceLoader.printStorageSavings(userData);

      // تحديث البيانات المحلية
      await _processUserData(userData);

      debugPrint('✅ تم تحميل البيانات بنظام المراجع بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات بنظام المراجع: $e');
      return false;
    }
  }
}
