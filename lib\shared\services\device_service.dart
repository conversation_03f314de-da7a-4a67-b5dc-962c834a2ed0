import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

class DeviceService {
  static const String _deviceIdKey = 'device_id';
  static DeviceService? _instance;
  static DeviceService get instance => _instance ??= DeviceService._();
  
  DeviceService._();

  String? _cachedDeviceId;

  /// الحصول على معرف الجهاز الفريد
  Future<String> getDeviceId() async {
    if (_cachedDeviceId != null) {
      return _cachedDeviceId!;
    }

    final prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString(_deviceIdKey);

    if (deviceId == null) {
      // إنشاء معرف جديد للجهاز
      deviceId = await _generateDeviceId();
      await prefs.setString(_deviceIdKey, deviceId);
    }

    _cachedDeviceId = deviceId;
    return deviceId;
  }

  /// إنشاء معرف فريد للجهاز
  Future<String> _generateDeviceId() async {
    try {
      // جمع معلومات الجهاز
      final deviceInfo = await _getDeviceInfo();
      
      // إضافة timestamp لضمان الفرادة
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      
      // دمج المعلومات
      final combinedInfo = '$deviceInfo-$timestamp';
      
      // تشفير المعلومات لإنشاء معرف فريد
      final bytes = utf8.encode(combinedInfo);
      final digest = sha256.convert(bytes);
      
      return digest.toString().substring(0, 32); // أخذ أول 32 حرف
    } catch (e) {
      // في حالة فشل الحصول على معلومات الجهاز، إنشاء معرف عشوائي
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final randomString = _generateRandomString(16);
      final combined = '$timestamp-$randomString';
      
      final bytes = utf8.encode(combined);
      final digest = sha256.convert(bytes);
      
      return digest.toString().substring(0, 32);
    }
  }

  /// الحصول على معلومات الجهاز
  Future<String> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        // معلومات Android
        return 'android-${Platform.operatingSystemVersion}';
      } else if (Platform.isIOS) {
        // معلومات iOS
        return 'ios-${Platform.operatingSystemVersion}';
      } else if (Platform.isWindows) {
        // معلومات Windows
        return 'windows-${Platform.operatingSystemVersion}';
      } else if (Platform.isMacOS) {
        // معلومات macOS
        return 'macos-${Platform.operatingSystemVersion}';
      } else if (Platform.isLinux) {
        // معلومات Linux
        return 'linux-${Platform.operatingSystemVersion}';
      } else {
        return 'unknown-platform';
      }
    } catch (e) {
      return 'unknown-device';
    }
  }

  /// إنشاء نص عشوائي
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    String result = '';
    
    for (int i = 0; i < length; i++) {
      result += chars[(random + i) % chars.length];
    }
    
    return result;
  }

  /// التحقق من صحة معرف الجهاز
  bool isValidDeviceId(String deviceId) {
    return deviceId.isNotEmpty && deviceId.length >= 16;
  }

  /// مسح معرف الجهاز المحفوظ (للاختبار فقط)
  Future<void> clearDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_deviceIdKey);
    _cachedDeviceId = null;
  }

  /// الحصول على معلومات الجهاز للعرض
  Future<Map<String, String>> getDeviceInfo() async {
    final deviceId = await getDeviceId();
    
    return {
      'deviceId': deviceId,
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'isPhysicalDevice': Platform.isAndroid || Platform.isIOS ? 'true' : 'false',
    };
  }
}
