import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/question_model.dart';

/// خدمة إدارة أسئلة المواد - نظام التحميل الموحد
/// تحميل جميع أسئلة المادة في قراءة واحدة من Firebase
class SubjectQuestionsService extends ChangeNotifier {
  static final SubjectQuestionsService _instance =
      SubjectQuestionsService._internal();
  static SubjectQuestionsService get instance => _instance;
  SubjectQuestionsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// تحميل جميع أسئلة مادة معينة في قراءة واحدة
  Future<List<Question>> getAllSubjectQuestions(String subjectId) async {
    try {
      debugPrint('🔄 تحميل جميع أسئلة المادة: $subjectId');
      debugPrint('📊 Firebase Read #1: subject_questions/$subjectId/questions');

      // محاولة تحميل من النظام الجديد أولاً
      var querySnapshot = await _firestore
          .collection('subject_questions') // مجموعة جديدة لكل مادة
          .doc(subjectId) // وثيقة واحدة لكل مادة
          .collection('questions') // جميع أسئلة المادة
          .where('isActive', isEqualTo: true)
          .get();

      // إذا لم توجد أسئلة في النظام الجديد، تحميل من النظام القديم
      if (querySnapshot.docs.isEmpty) {
        debugPrint('📭 لا توجد أسئلة في النظام الجديد، تحميل من النظام القديم');
        debugPrint('📊 Firebase Read #2: questions (النظام القديم)');
        querySnapshot = await _firestore
            .collection('questions')
            .where('subjectId', isEqualTo: subjectId)
            .where('isActive', isEqualTo: true)
            .get();
      }

      debugPrint('📊 تم العثور على ${querySnapshot.docs.length} سؤال للمادة');
      debugPrint(
        '✅ إجمالي قراءات Firebase: ${querySnapshot.docs.isEmpty ? 2 : 1}',
      );

      final questions = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // إضافة معرف الوثيقة
              return Question.fromMap(data);
            } catch (e) {
              debugPrint('❌ خطأ في تحويل السؤال ${doc.id}: $e');
              return null;
            }
          })
          .where((question) => question != null)
          .cast<Question>()
          .toList();

      // ترتيب الأسئلة حسب تاريخ الإنشاء (الأقدم أولاً)
      questions.sort((a, b) => a.createdAt.compareTo(b.createdAt));

      debugPrint('✅ تم تحميل ${questions.length} سؤال بنجاح للمادة $subjectId');
      return questions;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أسئلة المادة: $e');
      return [];
    }
  }

  /// فلترة الأسئلة محلياً حسب الوحدة
  List<Question> filterQuestionsByUnit(
    List<Question> questions,
    String unitId,
    bool isCourseQuestion,
  ) {
    return questions
        .where(
          (q) => q.unitId == unitId && q.isCourseQuestion == isCourseQuestion,
        )
        .toList();
  }

  /// فلترة الأسئلة محلياً حسب الدرس
  List<Question> filterQuestionsByLesson(
    List<Question> questions,
    String lessonId,
    bool isCourseQuestion,
  ) {
    return questions
        .where(
          (q) =>
              q.lessonId == lessonId && q.isCourseQuestion == isCourseQuestion,
        )
        .toList();
  }

  /// فلترة أسئلة الدورات
  List<Question> filterCourseQuestions(List<Question> questions) {
    return questions.where((q) => q.isCourseQuestion == true).toList();
  }

  /// فلترة الأسئلة العادية
  List<Question> filterRegularQuestions(List<Question> questions) {
    return questions.where((q) => q.isCourseQuestion == false).toList();
  }

  /// البحث في الأسئلة محلياً
  List<Question> searchQuestions(List<Question> questions, String query) {
    if (query.trim().isEmpty) return questions;

    final searchQuery = query.toLowerCase().trim();
    return questions.where((question) {
      // البحث في نص السؤال
      final questionTextMatch = question.questionText.toLowerCase().contains(
        searchQuery,
      );

      // البحث في الشرح
      final explanationMatch = question.explanation.toLowerCase().contains(
        searchQuery,
      );

      // البحث في الإجابات
      final optionsMatch = question.options.any(
        (option) => option.toLowerCase().contains(searchQuery),
      );

      return questionTextMatch || explanationMatch || optionsMatch;
    }).toList();
  }

  /// التحقق من وجود تحديثات للمادة
  Future<bool> hasUpdates(String subjectId, DateTime lastUpdateTime) async {
    try {
      debugPrint('🔍 التحقق من تحديثات المادة: $subjectId');
      debugPrint('📅 آخر تحديث محلي: $lastUpdateTime');

      // التحقق من آخر تحديث في قاعدة البيانات
      final metadataDoc = await _firestore
          .collection('subject_metadata')
          .doc(subjectId)
          .get();

      if (!metadataDoc.exists) {
        debugPrint('❌ لا توجد بيانات وصفية للمادة، التحقق من النظام القديم');
        // التحقق من النظام القديم
        return await _checkOldSystemUpdates(subjectId, lastUpdateTime);
      }

      final data = metadataDoc.data()!;
      final serverLastUpdate = (data['lastUpdate'] as Timestamp).toDate();

      debugPrint('📅 آخر تحديث في الخادم: $serverLastUpdate');

      final hasNewUpdates = serverLastUpdate.isAfter(lastUpdateTime);
      debugPrint(hasNewUpdates ? '🆕 توجد تحديثات جديدة' : '✅ لديك آخر تحديث');

      return hasNewUpdates;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من التحديثات: $e');
      return false;
    }
  }

  /// تحديث بيانات المادة الوصفية (للاستخدام في تطبيق الإدارة)
  Future<void> updateSubjectMetadata(String subjectId) async {
    try {
      await _firestore.collection('subject_metadata').doc(subjectId).set({
        'lastUpdate': FieldValue.serverTimestamp(),
        'subjectId': subjectId,
      }, SetOptions(merge: true));

      debugPrint('✅ تم تحديث بيانات المادة الوصفية: $subjectId');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات الوصفية: $e');
    }
  }

  /// إنشاء سؤال جديد في المادة (للاستخدام في تطبيق الإدارة)
  Future<String> createQuestionInSubject(Question question) async {
    try {
      final docRef = _firestore
          .collection('subject_questions')
          .doc(question.subjectId)
          .collection('questions')
          .doc();

      final questionWithId = question.copyWith(id: docRef.id);

      await docRef.set(questionWithId.toMap());

      // تحديث بيانات المادة الوصفية
      await updateSubjectMetadata(question.subjectId);

      debugPrint('✅ تم إنشاء السؤال في المادة: ${questionWithId.id}');
      notifyListeners();
      return questionWithId.id;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء السؤال في المادة: $e');
      rethrow;
    }
  }

  /// تحديث سؤال في المادة (للاستخدام في تطبيق الإدارة)
  Future<void> updateQuestionInSubject(Question question) async {
    try {
      await _firestore
          .collection('subject_questions')
          .doc(question.subjectId)
          .collection('questions')
          .doc(question.id)
          .update(question.copyWith(updatedAt: DateTime.now()).toMap());

      // تحديث بيانات المادة الوصفية
      await updateSubjectMetadata(question.subjectId);

      debugPrint('✅ تم تحديث السؤال في المادة: ${question.id}');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحديث السؤال في المادة: $e');
      rethrow;
    }
  }

  /// حذف سؤال من المادة (للاستخدام في تطبيق الإدارة)
  Future<void> deleteQuestionFromSubject(
    String subjectId,
    String questionId,
  ) async {
    try {
      await _firestore
          .collection('subject_questions')
          .doc(subjectId)
          .collection('questions')
          .doc(questionId)
          .delete();

      // تحديث بيانات المادة الوصفية
      await updateSubjectMetadata(subjectId);

      debugPrint('✅ تم حذف السؤال من المادة: $questionId');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في حذف السؤال من المادة: $e');
      rethrow;
    }
  }

  /// التحقق من تحديثات النظام القديم
  Future<bool> _checkOldSystemUpdates(
    String subjectId,
    DateTime lastUpdateTime,
  ) async {
    try {
      debugPrint('🔍 التحقق من تحديثات النظام القديم للمادة: $subjectId');

      // البحث عن جميع أسئلة المادة في النظام القديم (بدون ترتيب لتجنب الفهرس)
      final questionsQuery = await _firestore
          .collection('questions')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      if (questionsQuery.docs.isEmpty) {
        debugPrint('📭 لا توجد أسئلة في النظام القديم');
        return false;
      }

      // البحث عن أحدث سؤال محلياً
      DateTime? latestQuestionDate;
      for (final doc in questionsQuery.docs) {
        final questionData = doc.data();
        if (questionData['createdAt'] != null) {
          final questionCreatedAt = (questionData['createdAt'] as Timestamp)
              .toDate();
          if (latestQuestionDate == null ||
              questionCreatedAt.isAfter(latestQuestionDate)) {
            latestQuestionDate = questionCreatedAt;
          }
        }
      }

      if (latestQuestionDate == null) {
        debugPrint('📭 لا توجد أسئلة بتاريخ صحيح في النظام القديم');
        return false;
      }

      debugPrint('📅 آخر سؤال في النظام القديم: $latestQuestionDate');

      final hasNewUpdates = latestQuestionDate.isAfter(lastUpdateTime);
      debugPrint(
        hasNewUpdates
            ? '🆕 توجد أسئلة جديدة في النظام القديم'
            : '✅ لديك آخر تحديث من النظام القديم',
      );

      return hasNewUpdates;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من تحديثات النظام القديم: $e');
      return false;
    }
  }
}
