import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// خدمة إنشاء البيانات الموحدة - تجمع جميع البيانات في وثيقة واحدة
/// هذا يحقق نظام القراءة الواحدة الحقيقي
class UnifiedDataGenerator {
  static final UnifiedDataGenerator _instance =
      UnifiedDataGenerator._internal();
  static UnifiedDataGenerator get instance => _instance;
  UnifiedDataGenerator._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إنشاء البيانات الموحدة - يجب تشغيلها من تطبيق الأدمن
  Future<bool> generateUnifiedData() async {
    try {
      debugPrint('🔄 بدء إنشاء البيانات الموحدة...');

      // جمع جميع البيانات من المجموعات المختلفة
      final futures = await Future.wait([
        _firestore.collection('sections').get(),
        _firestore.collection('subjects').get(),
        _firestore.collection('video_sections').get(),
        _firestore.collection('video_subjects').get(),
        _firestore.collection('units').get(),
        _firestore.collection('lessons').get(),
        _firestore.collection('questions').get(),
        _firestore.collection('video_units').get(),
        _firestore.collection('video_lessons').get(),
        _firestore.collection('videos').get(),
      ]);

      // تصفية البيانات حسب النوع (مجاني/مدفوع)
      final allSections = futures[0].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allSubjects = futures[1].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allVideoSections = futures[2].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allVideoSubjects = futures[3].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allUnits = futures[4].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allLessons = futures[5].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allQuestions = futures[6].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allVideoUnits = futures[7].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allVideoLessons = futures[8].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
      final allVideos = futures[9].docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();

      // الحصول على IDs المواد المجانية
      final freeSubjectIds = allSubjects
          .where((subject) => subject['isFree'] == true)
          .map((subject) => subject['id'] as String)
          .toSet();

      final freeVideoSubjectIds = allVideoSubjects
          .where((subject) => subject['isFree'] == true)
          .map((subject) => subject['id'] as String)
          .toSet();

      // تصفية محتوى المواد المجانية فقط
      final freeUnits = allUnits
          .where((unit) => freeSubjectIds.contains(unit['subjectId']))
          .toList();
      final freeLessons = allLessons
          .where((lesson) => freeSubjectIds.contains(lesson['subjectId']))
          .toList();
      final freeQuestions = allQuestions
          .where((question) => freeSubjectIds.contains(question['subjectId']))
          .toList();
      final freeVideoUnits = allVideoUnits
          .where((unit) => freeVideoSubjectIds.contains(unit['subjectId']))
          .toList();
      final freeVideoLessons = allVideoLessons
          .where((lesson) => freeVideoSubjectIds.contains(lesson['subjectId']))
          .toList();
      final freeVideos = allVideos
          .where((video) => freeVideoSubjectIds.contains(video['subjectId']))
          .toList();

      // تحويل البيانات إلى تنسيق موحد (البيانات العامة + المحتوى المجاني)
      final unifiedData = {
        // جميع الأقسام والمواد (مدفوعة ومجانية)
        'sections': allSections,
        'subjects': allSubjects,
        'video_sections': allVideoSections,
        'video_subjects': allVideoSubjects,

        // محتوى المواد المجانية فقط
        'free_units': freeUnits,
        'free_lessons': freeLessons,
        'free_questions': freeQuestions,
        'free_video_units': freeVideoUnits,
        'free_video_lessons': freeVideoLessons,
        'free_videos': freeVideos,

        'lastUpdated': FieldValue.serverTimestamp(),
        'version': DateTime.now().millisecondsSinceEpoch,
      };

      // حفظ البيانات الموحدة في وثيقة واحدة
      await _firestore
          .collection('optimized_unified_data')
          .doc('general_data')
          .set(unifiedData);

      debugPrint('✅ تم إنشاء البيانات الموحدة بنجاح');
      debugPrint('📊 إحصائيات البيانات الموحدة:');
      debugPrint('   - الأقسام: ${(unifiedData['sections'] as List).length}');
      debugPrint('   - المواد: ${(unifiedData['subjects'] as List).length}');
      debugPrint(
        '   - أقسام الفيديو: ${(unifiedData['video_sections'] as List).length}',
      );
      debugPrint(
        '   - مواد الفيديو: ${(unifiedData['video_subjects'] as List).length}',
      );
      debugPrint(
        '   - الوحدات المجانية: ${(unifiedData['free_units'] as List).length}',
      );
      debugPrint(
        '   - الدروس المجانية: ${(unifiedData['free_lessons'] as List).length}',
      );
      debugPrint(
        '   - الأسئلة المجانية: ${(unifiedData['free_questions'] as List).length}',
      );
      debugPrint(
        '   - وحدات الفيديو المجانية: ${(unifiedData['free_video_units'] as List).length}',
      );
      debugPrint(
        '   - دروس الفيديو المجانية: ${(unifiedData['free_video_lessons'] as List).length}',
      );
      debugPrint(
        '   - الفيديوهات المجانية: ${(unifiedData['free_videos'] as List).length}',
      );

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء البيانات الموحدة: $e');
      return false;
    }
  }

  /// إنشاء بيانات موحدة لمستخدم محدد (مع الاشتراك)
  Future<bool> generateUserUnifiedData(String deviceId) async {
    try {
      debugPrint('🔄 بدء إنشاء البيانات الموحدة للمستخدم: $deviceId');

      // جلب بيانات اشتراك المستخدم
      final subscriptionDoc = await _firestore
          .collection('user_subscriptions')
          .doc(deviceId)
          .get();

      Map<String, dynamic> subscription = {
        'isActive': false,
        'subjectIds': <String>[],
        'videoSubjectIds': <String>[],
        'activatedAt': null,
        'expiresAt': null,
      };

      if (subscriptionDoc.exists) {
        final subscriptionData = subscriptionDoc.data()!;
        subscription = {
          'isActive': subscriptionData['isActive'] ?? false,
          'subjectIds': List<String>.from(subscriptionData['subjectIds'] ?? []),
          'videoSubjectIds': List<String>.from(
            subscriptionData['videoSubjectIds'] ?? [],
          ),
          'activatedAt': subscriptionData['activatedAt'],
          'expiresAt': subscriptionData['expiresAt'],
        };
      }

      // جلب البيانات العامة
      final publicDataDoc = await _firestore
          .collection('optimized_unified_data')
          .doc('general_data')
          .get();

      if (!publicDataDoc.exists) {
        debugPrint('❌ البيانات العامة الموحدة غير موجودة، يجب إنشاؤها أولاً');
        return false;
      }

      final publicData = publicDataDoc.data()!;

      // جلب المحتوى المدفوع للمواد المشترك فيها فقط
      final subscribedSubjectIds = List<String>.from(
        subscription['subjectIds'] ?? [],
      );
      final subscribedVideoSubjectIds = List<String>.from(
        subscription['videoSubjectIds'] ?? [],
      );

      // تحميل المحتوى المدفوع للمواد المشترك فيها
      List<Map<String, dynamic>> paidUnits = [];
      List<Map<String, dynamic>> paidLessons = [];
      List<Map<String, dynamic>> paidQuestions = [];
      List<Map<String, dynamic>> paidVideoUnits = [];
      List<Map<String, dynamic>> paidVideoLessons = [];
      List<Map<String, dynamic>> paidVideos = [];

      if (subscribedSubjectIds.isNotEmpty ||
          subscribedVideoSubjectIds.isNotEmpty) {
        List<Future<QuerySnapshot>> futures = [];

        // إضافة استعلامات المحتوى العادي إذا كان هناك مواد مشترك فيها
        if (subscribedSubjectIds.isNotEmpty) {
          futures.addAll([
            _firestore
                .collection('units')
                .where('subjectId', whereIn: subscribedSubjectIds)
                .get(),
            _firestore
                .collection('lessons')
                .where('subjectId', whereIn: subscribedSubjectIds)
                .get(),
            _firestore
                .collection('questions')
                .where('subjectId', whereIn: subscribedSubjectIds)
                .get(),
          ]);
        } else {
          // إضافة استعلامات فارغة
          futures.addAll([
            _firestore
                .collection('units')
                .where('subjectId', isEqualTo: 'non_existent')
                .get(),
            _firestore
                .collection('lessons')
                .where('subjectId', isEqualTo: 'non_existent')
                .get(),
            _firestore
                .collection('questions')
                .where('subjectId', isEqualTo: 'non_existent')
                .get(),
          ]);
        }

        // إضافة استعلامات محتوى الفيديو إذا كان هناك مواد فيديو مشترك فيها
        if (subscribedVideoSubjectIds.isNotEmpty) {
          futures.addAll([
            _firestore
                .collection('video_units')
                .where('subjectId', whereIn: subscribedVideoSubjectIds)
                .get(),
            _firestore
                .collection('video_lessons')
                .where('subjectId', whereIn: subscribedVideoSubjectIds)
                .get(),
            _firestore
                .collection('videos')
                .where('subjectId', whereIn: subscribedVideoSubjectIds)
                .get(),
          ]);
        } else {
          // إضافة استعلامات فارغة
          futures.addAll([
            _firestore
                .collection('video_units')
                .where('subjectId', isEqualTo: 'non_existent')
                .get(),
            _firestore
                .collection('video_lessons')
                .where('subjectId', isEqualTo: 'non_existent')
                .get(),
            _firestore
                .collection('videos')
                .where('subjectId', isEqualTo: 'non_existent')
                .get(),
          ]);
        }

        final paidContentFutures = await Future.wait(futures);

        paidUnits = paidContentFutures[0].docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>? ?? {};
          return <String, dynamic>{'id': doc.id, ...data};
        }).toList();
        paidLessons = paidContentFutures[1].docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>? ?? {};
          return <String, dynamic>{'id': doc.id, ...data};
        }).toList();
        paidQuestions = paidContentFutures[2].docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>? ?? {};
          return <String, dynamic>{'id': doc.id, ...data};
        }).toList();
        paidVideoUnits = paidContentFutures[3].docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>? ?? {};
          return <String, dynamic>{'id': doc.id, ...data};
        }).toList();
        paidVideoLessons = paidContentFutures[4].docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>? ?? {};
          return <String, dynamic>{'id': doc.id, ...data};
        }).toList();
        paidVideos = paidContentFutures[5].docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>? ?? {};
          return <String, dynamic>{'id': doc.id, ...data};
        }).toList();
      }

      // إنشاء البيانات الموحدة للمستخدم باستخدام نظام المراجع
      final userUnifiedData = {
        'subscription': subscription,

        // البيانات العامة (من البيانات الموحدة)
        'sections': publicData['sections'],
        'subjects': publicData['subjects'],
        'video_sections': publicData['video_sections'],
        'video_subjects': publicData['video_subjects'],

        // المحتوى المجاني (من البيانات الموحدة)
        'free_units': publicData['free_units'],
        'free_lessons': publicData['free_lessons'],
        'free_questions': publicData['free_questions'],
        'free_video_units': publicData['free_video_units'],
        'free_video_lessons': publicData['free_video_lessons'],
        'free_videos': publicData['free_videos'],

        // المحتوى المدفوع (مراجع فقط لتوفير المساحة)
        'paid_unit_ids': paidUnits.map((unit) => unit['id'] as String).toList(),
        'paid_lesson_ids': paidLessons
            .map((lesson) => lesson['id'] as String)
            .toList(),
        'paid_question_ids': paidQuestions
            .map((question) => question['id'] as String)
            .toList(),
        'paid_video_unit_ids': paidVideoUnits
            .map((unit) => unit['id'] as String)
            .toList(),
        'paid_video_lesson_ids': paidVideoLessons
            .map((lesson) => lesson['id'] as String)
            .toList(),
        'paid_video_ids': paidVideos
            .map((video) => video['id'] as String)
            .toList(),

        'lastUpdated': FieldValue.serverTimestamp(),
        'version': DateTime.now().millisecondsSinceEpoch,
      };

      // حفظ البيانات الموحدة للمستخدم
      await _firestore
          .collection('user_data')
          .doc(deviceId)
          .set(userUnifiedData);

      debugPrint('✅ تم إنشاء البيانات الموحدة للمستخدم بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء البيانات الموحدة للمستخدم: $e');
      return false;
    }
  }

  /// تحديث البيانات الموحدة لجميع المستخدمين
  Future<bool> updateAllUsersUnifiedData() async {
    try {
      debugPrint('🔄 بدء تحديث البيانات الموحدة لجميع المستخدمين...');

      // جلب جميع المستخدمين
      final usersSnapshot = await _firestore
          .collection('user_subscriptions')
          .get();

      int successCount = 0;
      int totalCount = usersSnapshot.docs.length;

      for (final userDoc in usersSnapshot.docs) {
        final success = await generateUserUnifiedData(userDoc.id);
        if (success) successCount++;
      }

      debugPrint(
        '✅ تم تحديث البيانات الموحدة لـ $successCount من $totalCount مستخدم',
      );
      return successCount == totalCount;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات الموحدة للمستخدمين: $e');
      return false;
    }
  }

  /// التحقق من وجود البيانات الموحدة
  Future<bool> checkUnifiedDataExists() async {
    try {
      final doc = await _firestore
          .collection('optimized_unified_data')
          .doc('general_data')
          .get();
      return doc.exists;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من البيانات الموحدة: $e');
      return false;
    }
  }

  /// حذف البيانات الموحدة (للاختبار)
  Future<bool> deleteUnifiedData() async {
    try {
      await _firestore
          .collection('optimized_unified_data')
          .doc('general_data')
          .delete();
      debugPrint('✅ تم حذف البيانات الموحدة');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف البيانات الموحدة: $e');
      return false;
    }
  }
}
