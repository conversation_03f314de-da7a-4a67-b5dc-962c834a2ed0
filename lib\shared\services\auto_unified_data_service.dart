import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'unified_data_generator.dart';

/// خدمة التحديث التلقائي للبيانات الموحدة
/// تقوم بتحديث البيانات تلقائياً عند إضافة/تعديل/حذف المحتوى
class AutoUnifiedDataService {
  static final AutoUnifiedDataService _instance = AutoUnifiedDataService._internal();
  static AutoUnifiedDataService get instance => _instance;
  AutoUnifiedDataService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final UnifiedDataGenerator _generator = UnifiedDataGenerator.instance;

  /// تهيئة الخدمة - يجب استدعاؤها من تطبيق الأدمن
  Future<void> initialize() async {
    debugPrint('🤖 تهيئة خدمة التحديث التلقائي للبيانات الموحدة...');
    // يمكن إضافة مستمعين للتغييرات هنا إذا لزم الأمر
    debugPrint('✅ تم تهيئة خدمة التحديث التلقائي');
  }

  /// تحديث تلقائي للبيانات العامة بعد تغيير الأقسام/المواد
  Future<bool> autoUpdatePublicData({
    required String operation, // 'create', 'update', 'delete'
    required String collection, // 'sections', 'subjects', etc.
    required String documentId,
    Map<String, dynamic>? data,
  }) async {
    try {
      debugPrint('🤖 تحديث تلقائي للبيانات العامة...');
      debugPrint('   العملية: $operation');
      debugPrint('   المجموعة: $collection');
      debugPrint('   المعرف: $documentId');

      // التحقق من أن العملية تتطلب تحديث البيانات العامة
      final publicDataCollections = [
        'sections',
        'subjects', 
        'video_sections',
        'video_subjects',
      ];

      // التحقق من أن العملية تتطلب تحديث المحتوى المجاني
      final freeContentCollections = [
        'units',
        'lessons', 
        'questions',
        'video_units',
        'video_lessons',
        'videos',
      ];

      bool shouldUpdatePublicData = false;

      if (publicDataCollections.contains(collection)) {
        // تحديث مطلوب دائماً للأقسام والمواد
        shouldUpdatePublicData = true;
      } else if (freeContentCollections.contains(collection) && data != null) {
        // تحديث مطلوب فقط إذا كان المحتوى مجاني
        shouldUpdatePublicData = await _isContentFree(collection, documentId, data);
      }

      if (shouldUpdatePublicData) {
        debugPrint('🔄 تنفيذ تحديث البيانات العامة الموحدة...');
        final success = await _generator.generateUnifiedData();
        
        if (success) {
          debugPrint('✅ تم التحديث التلقائي للبيانات العامة بنجاح');
          return true;
        } else {
          debugPrint('❌ فشل في التحديث التلقائي للبيانات العامة');
          return false;
        }
      } else {
        debugPrint('ℹ️ لا يتطلب تحديث البيانات العامة');
        return true;
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث التلقائي للبيانات العامة: $e');
      return false;
    }
  }

  /// تحديث تلقائي لبيانات المستخدمين بعد تغيير المحتوى المدفوع
  Future<bool> autoUpdateUsersData({
    required String operation, // 'create', 'update', 'delete'
    required String collection, // 'units', 'lessons', etc.
    required String documentId,
    Map<String, dynamic>? data,
  }) async {
    try {
      debugPrint('🤖 تحديث تلقائي لبيانات المستخدمين...');
      debugPrint('   العملية: $operation');
      debugPrint('   المجموعة: $collection');
      debugPrint('   المعرف: $documentId');

      // التحقق من أن العملية تتطلب تحديث بيانات المستخدمين
      final paidContentCollections = [
        'units',
        'lessons', 
        'questions',
        'video_units',
        'video_lessons',
        'videos',
      ];

      if (!paidContentCollections.contains(collection)) {
        debugPrint('ℹ️ لا يتطلب تحديث بيانات المستخدمين');
        return true;
      }

      // التحقق من أن المحتوى مدفوع
      if (data != null) {
        final isContentFree = await _isContentFree(collection, documentId, data);
        if (isContentFree) {
          debugPrint('ℹ️ المحتوى مجاني - لا يتطلب تحديث بيانات المستخدمين');
          return true;
        }
      }

      debugPrint('🔄 تنفيذ تحديث بيانات جميع المستخدمين...');
      final success = await _generator.updateAllUsersUnifiedData();
      
      if (success) {
        debugPrint('✅ تم التحديث التلقائي لبيانات المستخدمين بنجاح');
        return true;
      } else {
        debugPrint('❌ فشل في التحديث التلقائي لبيانات المستخدمين');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث التلقائي لبيانات المستخدمين: $e');
      return false;
    }
  }

  /// التحقق من أن المحتوى مجاني
  Future<bool> _isContentFree(String collection, String documentId, Map<String, dynamic> data) async {
    try {
      // الحصول على subjectId من البيانات
      final subjectId = data['subjectId'] as String?;
      if (subjectId == null) return false;

      // تحديد نوع المادة (اختبار أم فيديو)
      final isVideoContent = ['video_units', 'video_lessons', 'videos'].contains(collection);
      final subjectCollection = isVideoContent ? 'video_subjects' : 'subjects';

      // جلب بيانات المادة للتحقق من أنها مجانية
      final subjectDoc = await _firestore
          .collection(subjectCollection)
          .doc(subjectId)
          .get();

      if (!subjectDoc.exists) return false;

      final subjectData = subjectDoc.data()!;
      return subjectData['isFree'] == true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من نوع المحتوى: $e');
      return false;
    }
  }

  /// تحديث شامل (البيانات العامة + بيانات المستخدمين)
  Future<bool> fullAutoUpdate({
    required String operation,
    required String collection,
    required String documentId,
    Map<String, dynamic>? data,
  }) async {
    try {
      debugPrint('🤖 بدء التحديث الشامل التلقائي...');

      // تحديث البيانات العامة أولاً
      final publicDataSuccess = await autoUpdatePublicData(
        operation: operation,
        collection: collection,
        documentId: documentId,
        data: data,
      );

      // تحديث بيانات المستخدمين ثانياً
      final usersDataSuccess = await autoUpdateUsersData(
        operation: operation,
        collection: collection,
        documentId: documentId,
        data: data,
      );

      final overallSuccess = publicDataSuccess && usersDataSuccess;
      
      if (overallSuccess) {
        debugPrint('✅ تم التحديث الشامل التلقائي بنجاح');
      } else {
        debugPrint('❌ فشل في بعض أجزاء التحديث الشامل التلقائي');
      }

      return overallSuccess;
    } catch (e) {
      debugPrint('❌ خطأ في التحديث الشامل التلقائي: $e');
      return false;
    }
  }

  /// تحديث يدوي للبيانات العامة (للاستخدام من واجهة الأدمن)
  Future<bool> manualUpdatePublicData() async {
    try {
      debugPrint('🔧 تحديث يدوي للبيانات العامة...');
      final success = await _generator.generateUnifiedData();
      
      if (success) {
        debugPrint('✅ تم التحديث اليدوي للبيانات العامة بنجاح');
      } else {
        debugPrint('❌ فشل في التحديث اليدوي للبيانات العامة');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي للبيانات العامة: $e');
      return false;
    }
  }

  /// تحديث يدوي لبيانات المستخدمين (للاستخدام من واجهة الأدمن)
  Future<bool> manualUpdateUsersData() async {
    try {
      debugPrint('🔧 تحديث يدوي لبيانات المستخدمين...');
      final success = await _generator.updateAllUsersUnifiedData();
      
      if (success) {
        debugPrint('✅ تم التحديث اليدوي لبيانات المستخدمين بنجاح');
      } else {
        debugPrint('❌ فشل في التحديث اليدوي لبيانات المستخدمين');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي لبيانات المستخدمين: $e');
      return false;
    }
  }

  /// تحديث يدوي شامل (للاستخدام من واجهة الأدمن)
  Future<bool> manualFullUpdate() async {
    try {
      debugPrint('🔧 تحديث يدوي شامل...');
      
      final publicDataSuccess = await manualUpdatePublicData();
      final usersDataSuccess = await manualUpdateUsersData();
      
      final overallSuccess = publicDataSuccess && usersDataSuccess;
      
      if (overallSuccess) {
        debugPrint('✅ تم التحديث اليدوي الشامل بنجاح');
      } else {
        debugPrint('❌ فشل في بعض أجزاء التحديث اليدوي الشامل');
      }
      
      return overallSuccess;
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي الشامل: $e');
      return false;
    }
  }
}
