import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/pricing_message_model.dart';

class PricingMessageService extends ChangeNotifier {
  static final PricingMessageService _instance =
      PricingMessageService._internal();
  static PricingMessageService get instance => _instance;
  PricingMessageService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collectionName = 'pricing_message';
  static const String _documentId = 'main_message';

  PricingMessage? _currentMessage;
  bool _isLoading = false;
  String? _error;

  // Getters
  PricingMessage? get currentMessage => _currentMessage;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMessage =>
      _currentMessage != null && _currentMessage!.message.isNotEmpty;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await loadMessage();
  }

  /// تحميل رسالة الأسعار
  Future<void> loadMessage() async {
    _setLoading(true);
    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(_documentId)
          .get();

      if (doc.exists && doc.data() != null) {
        _currentMessage = PricingMessage.fromFirestore(doc.data()!, doc.id);
      } else {
        _currentMessage = null;
      }

      _clearError();
    } catch (e) {
      _setError('فشل في تحميل رسالة الأسعار: $e');
      debugPrint('خطأ في تحميل رسالة الأسعار: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// حفظ أو تحديث رسالة الأسعار
  Future<bool> saveMessage(
    String message, {
    String? telegramUsername,
    String? whatsappNumber,
  }) async {
    if (message.trim().isEmpty) {
      _setError('يرجى إدخال رسالة الأسعار');
      return false;
    }

    _setLoading(true);
    try {
      final now = DateTime.now();
      final pricingMessage = PricingMessage(
        id: _documentId,
        message: message.trim(),
        telegramUsername:
            telegramUsername?.trim() ?? _currentMessage?.telegramUsername ?? '',
        whatsappNumber:
            whatsappNumber?.trim() ?? _currentMessage?.whatsappNumber ?? '',
        createdAt: _currentMessage?.createdAt ?? now,
        updatedAt: now,
        isActive: true,
      );

      await _firestore
          .collection(_collectionName)
          .doc(_documentId)
          .set(pricingMessage.toFirestore());

      _currentMessage = pricingMessage;
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في حفظ رسالة الأسعار: $e');
      debugPrint('خطأ في حفظ رسالة الأسعار: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف رسالة الأسعار
  Future<bool> deleteMessage() async {
    _setLoading(true);
    try {
      await _firestore.collection(_collectionName).doc(_documentId).delete();

      _currentMessage = null;
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في حذف رسالة الأسعار: $e');
      debugPrint('خطأ في حذف رسالة الأسعار: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    await loadMessage();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
