# smart_test

A new Flutter project.

## Getting Started

# Smart Test - تطبيق الأسئلة الذكي

تطبيق Flutter متقدم لإدارة الأسئلة التعليمية مع نظام flavors للطالب والأدمن.

## المميزات

### تطبيق الطالب
- واجهة عصرية وجذابة بألوان متنوعة
- نظام اشتراكات بالأكواد
- تصنيف الأسئلة (وحدات، دروس، مفضلة، خاطئة، دورات)
- نظام أسئلة مؤتمت مع خيارات متعددة
- حفظ محلي آمن للعمل بدون إنترنت
- إحصائيات مفصلة للأداء

### تطبيق الأدمن
- إدارة شاملة للأسئلة والمواد
- إدارة الاشتراكات والأكواد
- لوحة تحكم متقدمة
- إحصائيات وتقارير

## التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **Firebase** - قاعدة البيانات والمصادقة
- **Flavors** - لإدارة تطبيقين منفصلين
- **Provider** - إدارة الحالة
- **SQLite** - التخزين المحلي الآمن
- **Material Design 3** - التصميم العصري

## كيفية التشغيل

### تشغيل تطبيق الطالب
```bash
flutter run --flavor student -t lib/main_student.dart
```

### تشغيل تطبيق الأدمن
```bash
flutter run --flavor admin -t lib/main_admin.dart
```

### من VS Code
استخدم configurations الجاهزة في `.vscode/launch.json`:
- Smart Test - Student
- Smart Test - Admin

## إعداد Firebase

1. إنشاء مشروع Firebase جديد
2. إضافة تطبيقين Android منفصلين:
   - `com.smarttest.student`
   - `com.smarttest.admin`
3. تحديث ملفات `firebase_options_*.dart`
4. إضافة ملفات `google-services.json` للـ flavors

## هيكل المشروع

```
lib/
├── core/                 # الملفات الأساسية
│   ├── theme/           # الثيمات والألوان
│   ├── constants/       # الثوابت
│   └── utils/           # الأدوات المساعدة
├── features/            # المميزات
│   ├── student/         # مميزات الطالب
│   └── admin/           # مميزات الأدمن
├── shared/              # المكونات المشتركة
├── flavors.dart         # إعدادات Flavors
├── app.dart            # التطبيق الرئيسي
├── main_student.dart   # نقطة دخول الطالب
└── main_admin.dart     # نقطة دخول الأدمن
```

## حالة المشروع - مكتمل 100% ✅

### ✅ المكتمل:
1. **إعداد المشروع مع Flavors** - مكتمل
2. **إعداد Firebase وقاعدة البيانات** - مكتمل
3. **تطوير واجهات الطالب** - مكتمل
4. **تطوير نظام الأسئلة** - مكتمل
5. **تطوير تطبيق الأدمن** - مكتمل
6. **إدارة الأسئلة والاختبارات** - مكتمل
7. **نظام أكواد التفعيل** - مكتمل
8. **الإحصائيات والتقارير** - مكتمل
9. **الاختبار والتحسين** - مكتمل

### 🎯 الميزات المتوفرة:

#### تطبيق الطالب:
- ✅ تفعيل الاشتراك بالأكواد
- ✅ تصفح المواد (الوحدات، الدروس، المفضلة، الخاطئة، الدورات)
- ✅ حل الأسئلة التفاعلية
- ✅ نظام المفضلة والإحصائيات

#### تطبيق الأدمن:
- ✅ إدارة المواد والوحدات والدروس
- ✅ إدارة الأسئلة (عادية ودورات)
- ✅ إدارة الاختبارات (مسودات، منشورة، مؤرشفة)
- ✅ إدارة أكواد التفعيل (نشطة، مستخدمة، منتهية)
- ✅ إحصائيات شاملة ومفصلة
- ✅ إدارة البيانات والنسخ الاحتياطي

## 🚀 Firebase Indexes المطلوبة

لضمان عمل التطبيق بشكل صحيح، يجب إنشاء الفهارس التالية في Firebase:

### مطلوب فوراً:
```
Collection: lessons
Fields: isActive (Ascending), unitId (Ascending), order (Ascending)
```

### للأداء الأمثل:
- فهارس للأسئلة حسب المادة والنوع
- فهارس للاختبارات حسب الحالة
- فهارس لأكواد التفعيل

## 📱 التطبيق جاهز للاستخدام!

**تم تطوير هذا المشروع بواسطة Augment Agent** 🚀
