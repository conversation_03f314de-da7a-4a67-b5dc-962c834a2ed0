import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/widgets/theme_toggle_widget.dart';
import '../../../../shared/pages/theme_settings_page.dart';
import 'subjects_management_page.dart';
import 'questions_management_page.dart';
import 'codes_management_page.dart';
import 'pricing_management_page.dart';
import 'sections_management_page.dart';
import 'video_sections_page.dart';
import 'optimized_unified_data_page.dart';

class AdminHomePage extends StatelessWidget {
  const AdminHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          'Smart Edu - الأدمن',
          style: Theme.of(
            context,
          ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.getSecondaryGradient(context),
          ),
        ),
        foregroundColor: Colors.white,
        actions: [
          // زر تبديل الثيم
          const AppBarThemeToggle(),
          // زر إعدادات الثيم
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ThemeSettingsPage(),
                ),
              );
            },
            tooltip: 'إعدادات المظهر',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'لوحة التحكم',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            SizedBox(height: 24.h),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16.w,
                mainAxisSpacing: 16.h,
                childAspectRatio: 1.2,
                children: [
                  _buildAdminCard(
                    context,
                    'إدارة الأقسام',
                    Icons.school,
                    Colors.teal,
                    () => _navigateToPage(context, 'sections'),
                  ),
                  _buildAdminCard(
                    context,
                    'إدارة المواد',
                    Icons.book,
                    AppTheme.primaryColor,
                    () => _navigateToPage(context, 'subjects'),
                  ),
                  _buildAdminCard(
                    context,
                    'إدارة الأسئلة',
                    Icons.quiz,
                    AppTheme.secondaryColor,
                    () => _navigateToPage(context, 'questions'),
                  ),
                  _buildAdminCard(
                    context,
                    'إدارة الأكواد',
                    Icons.vpn_key,
                    AppTheme.accentColor,
                    () => _navigateToPage(context, 'codes'),
                  ),
                  _buildAdminCard(
                    context,
                    'أسعار الاشتراك',
                    Icons.price_check,
                    Colors.purple,
                    () => _navigateToPage(context, 'pricing'),
                  ),
                  _buildAdminCard(
                    context,
                    'إدارة الفيديوهات',
                    Icons.video_library,
                    Colors.orange,
                    () => _navigateToPage(context, 'videos'),
                  ),
                  _buildAdminCard(
                    context,
                    'البيانات الموحدة المحسنة 🚀',
                    Icons.rocket_launch,
                    Colors.purple,
                    () => _navigateToPage(context, 'optimized_unified'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      shadowColor: color.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 50.w,
                  height: 50.h,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(25.r),
                  ),
                  child: Icon(icon, color: Colors.white, size: 24.sp),
                ),
                SizedBox(height: 12.h),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToPage(BuildContext context, String page) {
    Widget? targetPage;
    String message = '';

    switch (page) {
      case 'sections':
        targetPage = const SectionsManagementPage();
        break;
      case 'subjects':
        targetPage = const SubjectsManagementPage();
        break;
      case 'questions':
        targetPage = const QuestionsManagementPage();
        break;
      case 'codes':
        targetPage = const CodesManagementPage();
        break;
      case 'pricing':
        targetPage = const PricingManagementPage();
        break;
      case 'videos':
        targetPage = const VideoSectionsPage();
        break;
      case 'optimized_unified':
        targetPage = const OptimizedUnifiedDataPage();
        break;
      default:
        message = 'هذه الميزة قيد التطوير';
    }

    if (targetPage != null) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => targetPage!),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.primaryColor,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
