@echo off
echo Fixing video_player_win CMake policy...

REM تنظيف build أولاً
flutter clean

REM إنشاء مجلد build مؤقت لتحديد مسار video_player_win
flutter build windows --debug --verbose > build_log.txt 2>&1

REM البحث عن ملف CMakeLists.txt لـ video_player_win
for /f "delims=" %%i in ('dir /s /b "build\windows\x64\plugins\video_player_win\CMakeLists.txt" 2^>nul') do (
    echo Found video_player_win CMakeLists.txt at: %%i
    
    REM إضافة سياسة CMake في بداية الملف
    echo if^(POLICY CMP0175^) > temp_cmake.txt
    echo   cmake_policy^(SET CMP0175 NEW^) >> temp_cmake.txt
    echo endif^(^) >> temp_cmake.txt
    echo. >> temp_cmake.txt
    type "%%i" >> temp_cmake.txt
    move temp_cmake.txt "%%i"
    
    echo Fixed CMake policy for video_player_win
)

echo Done! Now you can run: flutter run -d windows --dart-define=FLAVOR=student
