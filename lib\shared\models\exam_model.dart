import 'package:cloud_firestore/cloud_firestore.dart';

enum ExamStatus {
  draft,
  published,
  active,
  completed,
  archived,
}

class Exam {
  final String id;
  final String title;
  final String description;
  final String subjectId;
  final List<String> questionIds;
  final int duration; // بالدقائق
  final int totalPoints;
  final double passingScore; // النسبة المئوية للنجاح
  final ExamStatus status;
  final DateTime? startTime;
  final DateTime? endTime;
  final bool shuffleQuestions;
  final bool shuffleOptions;
  final bool showResults;
  final bool allowRetake;
  final int maxAttempts;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdByAdminId;

  const Exam({
    required this.id,
    required this.title,
    required this.description,
    required this.subjectId,
    required this.questionIds,
    required this.duration,
    required this.totalPoints,
    required this.passingScore,
    required this.status,
    this.startTime,
    this.endTime,
    required this.shuffleQuestions,
    required this.shuffleOptions,
    required this.showResults,
    required this.allowRetake,
    required this.maxAttempts,
    required this.settings,
    required this.createdAt,
    required this.updatedAt,
    required this.createdByAdminId,
  });

  factory Exam.fromMap(Map<String, dynamic> map) {
    return Exam(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      subjectId: map['subjectId'] ?? '',
      questionIds: List<String>.from(map['questionIds'] ?? []),
      duration: map['duration'] ?? 60,
      totalPoints: map['totalPoints'] ?? 0,
      passingScore: (map['passingScore'] ?? 50.0).toDouble(),
      status: ExamStatus.values.firstWhere(
        (e) => e.toString() == 'ExamStatus.${map['status']}',
        orElse: () => ExamStatus.draft,
      ),
      startTime: map['startTime'] != null 
          ? (map['startTime'] as Timestamp).toDate() 
          : null,
      endTime: map['endTime'] != null 
          ? (map['endTime'] as Timestamp).toDate() 
          : null,
      shuffleQuestions: map['shuffleQuestions'] ?? false,
      shuffleOptions: map['shuffleOptions'] ?? false,
      showResults: map['showResults'] ?? true,
      allowRetake: map['allowRetake'] ?? false,
      maxAttempts: map['maxAttempts'] ?? 1,
      settings: Map<String, dynamic>.from(map['settings'] ?? {}),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      createdByAdminId: map['createdByAdminId'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'subjectId': subjectId,
      'questionIds': questionIds,
      'duration': duration,
      'totalPoints': totalPoints,
      'passingScore': passingScore,
      'status': status.toString().split('.').last,
      'startTime': startTime != null ? Timestamp.fromDate(startTime!) : null,
      'endTime': endTime != null ? Timestamp.fromDate(endTime!) : null,
      'shuffleQuestions': shuffleQuestions,
      'shuffleOptions': shuffleOptions,
      'showResults': showResults,
      'allowRetake': allowRetake,
      'maxAttempts': maxAttempts,
      'settings': settings,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdByAdminId': createdByAdminId,
    };
  }

  Exam copyWith({
    String? id,
    String? title,
    String? description,
    String? subjectId,
    List<String>? questionIds,
    int? duration,
    int? totalPoints,
    double? passingScore,
    ExamStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    bool? shuffleQuestions,
    bool? shuffleOptions,
    bool? showResults,
    bool? allowRetake,
    int? maxAttempts,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByAdminId,
  }) {
    return Exam(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      subjectId: subjectId ?? this.subjectId,
      questionIds: questionIds ?? this.questionIds,
      duration: duration ?? this.duration,
      totalPoints: totalPoints ?? this.totalPoints,
      passingScore: passingScore ?? this.passingScore,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      shuffleQuestions: shuffleQuestions ?? this.shuffleQuestions,
      shuffleOptions: shuffleOptions ?? this.shuffleOptions,
      showResults: showResults ?? this.showResults,
      allowRetake: allowRetake ?? this.allowRetake,
      maxAttempts: maxAttempts ?? this.maxAttempts,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
    );
  }

  // Helper methods
  String get statusDisplayName {
    switch (status) {
      case ExamStatus.draft:
        return 'مسودة';
      case ExamStatus.published:
        return 'منشور';
      case ExamStatus.active:
        return 'نشط';
      case ExamStatus.completed:
        return 'مكتمل';
      case ExamStatus.archived:
        return 'مؤرشف';
    }
  }

  bool get isActive {
    if (status != ExamStatus.active) return false;
    final now = DateTime.now();
    if (startTime != null && now.isBefore(startTime!)) return false;
    if (endTime != null && now.isAfter(endTime!)) return false;
    return true;
  }

  bool get isAvailable {
    return status == ExamStatus.published || status == ExamStatus.active;
  }

  String get durationDisplayText {
    if (duration < 60) {
      return '$duration دقيقة';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      if (minutes == 0) {
        return '$hours ساعة';
      } else {
        return '$hours ساعة و $minutes دقيقة';
      }
    }
  }

  int get questionCount => questionIds.length;
}
