import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/unit_model.dart';
import '../../../../shared/models/lesson_model.dart';
import '../../../../shared/services/content_service.dart';

class UnitsLessonsManagementPage extends StatefulWidget {
  final Subject subject;

  const UnitsLessonsManagementPage({super.key, required this.subject});

  @override
  State<UnitsLessonsManagementPage> createState() =>
      _UnitsLessonsManagementPageState();
}

class _UnitsLessonsManagementPageState extends State<UnitsLessonsManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Unit> _units = [];
  List<Lesson> _lessons = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final units = await ContentService.instance.getSubjectUnits(
        widget.subject.id,
      );

      List<Lesson> allLessons = [];
      for (final unit in units) {
        final unitLessons = await ContentService.instance.getUnitLessons(
          unit.id,
        );
        allLessons.addAll(unitLessons);
      }

      setState(() {
        _units = units;
        _lessons = allLessons;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final subjectColor = Color(
      int.parse(widget.subject.color.replaceFirst('#', '0xFF')),
    );

    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          'إدارة ${widget.subject.name}',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [subjectColor, subjectColor.withValues(alpha: 0.8)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
          tabs: const [
            Tab(icon: Icon(Icons.folder), text: 'الوحدات'),
            Tab(icon: Icon(Icons.play_lesson), text: 'الدروس'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [_buildUnitsTab(), _buildLessonsTab()],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (_tabController.index == 0) {
            _showAddUnitDialog();
          } else {
            _showAddLessonDialog();
          }
        },
        backgroundColor: subjectColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildUnitsTab() {
    if (_units.isEmpty) {
      return _buildEmptyState(
        icon: Icons.folder_outlined,
        title: 'لا توجد وحدات',
        subtitle: 'اضغط على + لإضافة وحدة جديدة',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ReorderableListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _units.length,
        onReorder: _reorderUnits,
        itemBuilder: (context, index) {
          final unit = _units[index];
          return _buildUnitCard(unit, index, key: ValueKey(unit.id));
        },
      ),
    );
  }

  Widget _buildLessonsTab() {
    if (_lessons.isEmpty) {
      return _buildEmptyState(
        icon: Icons.play_lesson_outlined,
        title: 'لا توجد دروس',
        subtitle: 'اضغط على + لإضافة درس جديد',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _lessons.length,
        itemBuilder: (context, index) {
          final lesson = _lessons[index];
          return _buildLessonCard(lesson);
        },
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 80.sp, color: AppTheme.textSecondaryColor),
          SizedBox(height: 16.h),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitCard(Unit unit, int index, {required Key key}) {
    final unitColor = Color(int.parse(unit.color.replaceFirst('#', '0xFF')));

    return Card(
      key: key,
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              unitColor.withValues(alpha: 0.1),
              unitColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // رقم الترتيب
                  Container(
                    width: 30.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                      color: unitColor,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Center(
                      child: Text(
                        '${unit.order}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // معلومات الوحدة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          unit.name,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        if (unit.description.isNotEmpty) ...[
                          SizedBox(height: 4.h),
                          Text(
                            unit.description,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppTheme.textSecondaryColor),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),

                  // حالة التفعيل
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: unit.isActive
                          ? AppTheme.successColor.withValues(alpha: 0.1)
                          : AppTheme.textSecondaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      unit.isActive ? 'مفعل' : 'معطل',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: unit.isActive
                            ? AppTheme.successColor
                            : AppTheme.textSecondaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  // أيقونة السحب
                  Icon(Icons.drag_handle, color: AppTheme.textSecondaryColor),
                ],
              ),

              SizedBox(height: 12.h),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _showUnitLessons(unit),
                      icon: Icon(Icons.play_lesson, size: 16.sp),
                      label: Text('الدروس'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: unitColor.withValues(alpha: 0.1),
                        foregroundColor: unitColor,
                        elevation: 0,
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  IconButton(
                    onPressed: () => _showEditUnitDialog(unit),
                    icon: Icon(Icons.edit, color: AppTheme.primaryColor),
                    tooltip: 'تعديل',
                  ),

                  IconButton(
                    onPressed: () => _toggleUnitStatus(unit),
                    icon: Icon(
                      unit.isActive ? Icons.visibility_off : Icons.visibility,
                      color: unit.isActive
                          ? AppTheme.warningColor
                          : AppTheme.successColor,
                    ),
                    tooltip: unit.isActive ? 'إلغاء تفعيل' : 'تفعيل',
                  ),

                  IconButton(
                    onPressed: () => _deleteUnit(unit),
                    icon: Icon(Icons.delete, color: AppTheme.errorColor),
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLessonCard(Lesson lesson) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              AppTheme.primaryColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // رقم الترتيب
                  Container(
                    width: 30.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Center(
                      child: Text(
                        '${lesson.order}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // معلومات الدرس
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lesson.name,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        if (lesson.description.isNotEmpty) ...[
                          SizedBox(height: 4.h),
                          Text(
                            lesson.description,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppTheme.textSecondaryColor),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),

                  // حالة التفعيل
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: lesson.isActive
                          ? AppTheme.successColor.withValues(alpha: 0.1)
                          : AppTheme.textSecondaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      lesson.isActive ? 'مفعل' : 'معطل',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: lesson.isActive
                            ? AppTheme.successColor
                            : AppTheme.textSecondaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'الوحدة: ${_getUnitName(lesson.unitId)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),

                  IconButton(
                    onPressed: () => _showEditLessonDialog(lesson),
                    icon: Icon(Icons.edit, color: AppTheme.primaryColor),
                    tooltip: 'تعديل',
                  ),

                  IconButton(
                    onPressed: () => _toggleLessonStatus(lesson),
                    icon: Icon(
                      lesson.isActive ? Icons.visibility_off : Icons.visibility,
                      color: lesson.isActive
                          ? AppTheme.warningColor
                          : AppTheme.successColor,
                    ),
                    tooltip: lesson.isActive ? 'إلغاء تفعيل' : 'تفعيل',
                  ),

                  IconButton(
                    onPressed: () => _deleteLesson(lesson),
                    icon: Icon(Icons.delete, color: AppTheme.errorColor),
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getUnitName(String unitId) {
    final unit = _units.firstWhere(
      (u) => u.id == unitId,
      orElse: () => Unit(
        id: '',
        name: 'غير محدد',
        description: '',
        subjectId: '',
        order: 0,
        color: '#000000',
        iconUrl: '',
        isActive: false,
        createdByAdminId: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );
    return unit.name;
  }

  void _reorderUnits(int oldIndex, int newIndex) {
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }

    setState(() {
      final unit = _units.removeAt(oldIndex);
      _units.insert(newIndex, unit);

      // تحديث ترتيب الوحدات
      for (int i = 0; i < _units.length; i++) {
        _units[i] = _units[i].copyWith(order: i + 1);
      }
    });

    // حفظ الترتيب الجديد
    _saveUnitsOrder();
  }

  Future<void> _saveUnitsOrder() async {
    try {
      for (final unit in _units) {
        await ContentService.instance.updateUnit(unit);
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ الترتيب: $e')));
      }
    }
  }

  void _showUnitLessons(Unit unit) {
    // عرض دروس الوحدة المحددة
    _tabController.animateTo(1);
    setState(() {
      _lessons = _lessons.where((lesson) => lesson.unitId == unit.id).toList();
    });
  }

  void _showAddUnitDialog() {
    _showUnitDialog();
  }

  void _showEditUnitDialog(Unit unit) {
    _showUnitDialog(unit: unit);
  }

  void _showUnitDialog({Unit? unit}) {
    final isEditing = unit != null;
    final nameController = TextEditingController(text: unit?.name ?? '');
    final descriptionController = TextEditingController(
      text: unit?.description ?? '',
    );
    Color selectedColor = unit != null
        ? Color(int.parse(unit.color.replaceFirst('#', '0xFF')))
        : AppTheme.primaryColor;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل الوحدة' : 'إضافة وحدة جديدة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الوحدة',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),
                TextField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'وصف الوحدة',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),
                Text(
                  'اختر لون الوحدة:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                SizedBox(height: 8.h),
                Wrap(
                  spacing: 8.w,
                  children:
                      [
                            AppTheme.primaryColor,
                            AppTheme.secondaryColor,
                            AppTheme.accentColor,
                            AppTheme.successColor,
                            AppTheme.warningColor,
                            AppTheme.errorColor,
                          ]
                          .map(
                            (color) => GestureDetector(
                              onTap: () {
                                setDialogState(() {
                                  selectedColor = color;
                                });
                              },
                              child: Container(
                                width: 40.w,
                                height: 40.h,
                                decoration: BoxDecoration(
                                  color: color,
                                  shape: BoxShape.circle,
                                  border: selectedColor == color
                                      ? Border.all(
                                          color: Colors.black,
                                          width: 3,
                                        )
                                      : null,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('يرجى إدخال اسم الوحدة')),
                  );
                  return;
                }

                try {
                  final colorHex =
                      '#${selectedColor.toARGB32().toRadixString(16).substring(2)}';

                  if (isEditing) {
                    final updatedUnit = unit.copyWith(
                      name: nameController.text.trim(),
                      description: descriptionController.text.trim(),
                      color: colorHex,
                    );
                    await ContentService.instance.updateUnit(updatedUnit);
                  } else {
                    final newUnit = Unit(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: nameController.text.trim(),
                      description: descriptionController.text.trim(),
                      subjectId: widget.subject.id,
                      order: _units.length + 1,
                      color: colorHex,
                      iconUrl: '',
                      isActive: true,
                      createdByAdminId: 'admin',
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    );
                    await ContentService.instance.addUnit(newUnit);
                  }

                  if (mounted && context.mounted) {
                    Navigator.pop(context);
                    _loadData();

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          isEditing
                              ? 'تم تحديث الوحدة بنجاح'
                              : 'تم إضافة الوحدة بنجاح',
                        ),
                        backgroundColor: AppTheme.successColor,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted && context.mounted) {
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
                  }
                }
              },
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddLessonDialog() {
    _showLessonDialog();
  }

  void _showEditLessonDialog(Lesson lesson) {
    _showLessonDialog(lesson: lesson);
  }

  void _showLessonDialog({Lesson? lesson}) {
    final isEditing = lesson != null;
    final nameController = TextEditingController(text: lesson?.name ?? '');
    final descriptionController = TextEditingController(
      text: lesson?.description ?? '',
    );
    String selectedUnitId =
        lesson?.unitId ?? (_units.isNotEmpty ? _units.first.id : '');

    if (_units.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب إضافة وحدة أولاً قبل إضافة الدروس')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل الدرس' : 'إضافة درس جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الدرس',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),
                TextField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'وصف الدرس',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16.h),
                DropdownButtonFormField<String>(
                  value: selectedUnitId,
                  decoration: const InputDecoration(
                    labelText: 'الوحدة',
                    border: OutlineInputBorder(),
                  ),
                  items: _units
                      .map(
                        (unit) => DropdownMenuItem(
                          value: unit.id,
                          child: Text(unit.name),
                        ),
                      )
                      .toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      selectedUnitId = value ?? '';
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('يرجى إدخال اسم الدرس')),
                  );
                  return;
                }

                try {
                  if (isEditing) {
                    final updatedLesson = lesson.copyWith(
                      name: nameController.text.trim(),
                      description: descriptionController.text.trim(),
                      unitId: selectedUnitId,
                    );
                    await ContentService.instance.updateLesson(updatedLesson);
                  } else {
                    final unitLessons = _lessons
                        .where((l) => l.unitId == selectedUnitId)
                        .length;
                    final newLesson = Lesson(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: nameController.text.trim(),
                      description: descriptionController.text.trim(),
                      subjectId: widget.subject.id,
                      unitId: selectedUnitId,
                      order: unitLessons + 1,
                      videoUrl: '',
                      contentUrl: '',
                      iconUrl: '',
                      isActive: true,
                      createdByAdminId: 'admin',
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    );
                    await ContentService.instance.addLesson(newLesson);
                  }

                  if (mounted && context.mounted) {
                    Navigator.pop(context);
                    _loadData();

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          isEditing
                              ? 'تم تحديث الدرس بنجاح'
                              : 'تم إضافة الدرس بنجاح',
                        ),
                        backgroundColor: AppTheme.successColor,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted && context.mounted) {
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
                  }
                }
              },
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleUnitStatus(Unit unit) async {
    try {
      final updatedUnit = unit.copyWith(isActive: !unit.isActive);
      await ContentService.instance.updateUnit(updatedUnit);
      _loadData();

      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              unit.isActive ? 'تم إلغاء تفعيل الوحدة' : 'تم تفعيل الوحدة',
            ),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
      }
    }
  }

  Future<void> _toggleLessonStatus(Lesson lesson) async {
    try {
      final updatedLesson = lesson.copyWith(isActive: !lesson.isActive);
      await ContentService.instance.updateLesson(updatedLesson);
      _loadData();

      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              lesson.isActive ? 'تم إلغاء تفعيل الدرس' : 'تم تفعيل الدرس',
            ),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: $e')));
      }
    }
  }

  Future<void> _deleteUnit(Unit unit) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف وحدة "${unit.name}"؟\nسيتم حذف جميع الدروس والأسئلة المرتبطة بها.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ContentService.instance.deleteUnit(unit.id);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الوحدة بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في الحذف: $e')));
        }
      }
    }
  }

  Future<void> _deleteLesson(Lesson lesson) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف درس "${lesson.name}"؟\nسيتم حذف جميع الأسئلة المرتبطة به.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ContentService.instance.deleteLesson(lesson.id);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الدرس بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في الحذف: $e')));
        }
      }
    }
  }
}
