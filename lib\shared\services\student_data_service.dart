import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/question_model.dart';

/// خدمة لحفظ بيانات الطالب محلياً
class StudentDataService {
  static final StudentDataService _instance = StudentDataService._internal();
  static StudentDataService get instance => _instance;
  StudentDataService._internal();

  static const String _favoriteQuestionsKey = 'favorite_questions_by_subject';
  static const String _wrongQuestionsKey = 'wrong_questions_by_subject';
  static const String _questionNotesKey = 'question_notes';
  static const String _questionResultsKey = 'question_results';
  static const String _userAnswersKey = 'user_answers';

  /// حفظ سؤال في المفضلة لمادة معينة
  Future<void> addToFavorites(String questionId, String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = await getFavoriteQuestions(subjectId);
    if (!favorites.contains(questionId)) {
      favorites.add(questionId);
      await prefs.setStringList(
        '${_favoriteQuestionsKey}_$subjectId',
        favorites,
      );
    }
  }

  /// إزالة سؤال من المفضلة لمادة معينة
  Future<void> removeFromFavorites(String questionId, String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = await getFavoriteQuestions(subjectId);
    favorites.remove(questionId);
    await prefs.setStringList('${_favoriteQuestionsKey}_$subjectId', favorites);
  }

  /// الحصول على قائمة الأسئلة المفضلة لمادة معينة
  Future<List<String>> getFavoriteQuestions(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList('${_favoriteQuestionsKey}_$subjectId') ?? [];
  }

  /// فحص ما إذا كان السؤال في المفضلة لمادة معينة
  Future<bool> isFavorite(String questionId, String subjectId) async {
    final favorites = await getFavoriteQuestions(subjectId);
    return favorites.contains(questionId);
  }

  /// الحصول على جميع الأسئلة المفضلة لجميع المواد (للتوافق مع الكود القديم)
  Future<List<String>> getAllFavoriteQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final allKeys = prefs.getKeys();
    final favoriteKeys = allKeys.where(
      (key) => key.startsWith(_favoriteQuestionsKey),
    );

    final allFavorites = <String>[];
    for (final key in favoriteKeys) {
      final favorites = prefs.getStringList(key) ?? [];
      allFavorites.addAll(favorites);
    }

    return allFavorites.toSet().toList(); // إزالة المكررات
  }

  /// حفظ سؤال في الأسئلة الخاطئة لمادة معينة
  Future<void> addToWrongQuestions(String questionId, String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    final wrongQuestions = await getWrongQuestions(subjectId);
    if (!wrongQuestions.contains(questionId)) {
      wrongQuestions.add(questionId);
      await prefs.setStringList(
        '${_wrongQuestionsKey}_$subjectId',
        wrongQuestions,
      );
    }
  }

  /// إزالة سؤال من الأسئلة الخاطئة لمادة معينة (عند الإجابة الصحيحة)
  Future<void> removeFromWrongQuestions(
    String questionId,
    String subjectId,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final wrongQuestions = await getWrongQuestions(subjectId);
    wrongQuestions.remove(questionId);
    await prefs.setStringList(
      '${_wrongQuestionsKey}_$subjectId',
      wrongQuestions,
    );
  }

  /// الحصول على قائمة الأسئلة الخاطئة لمادة معينة
  Future<List<String>> getWrongQuestions(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList('${_wrongQuestionsKey}_$subjectId') ?? [];
  }

  /// فحص ما إذا كان السؤال في الأسئلة الخاطئة لمادة معينة
  Future<bool> isWrongQuestion(String questionId, String subjectId) async {
    final wrongQuestions = await getWrongQuestions(subjectId);
    return wrongQuestions.contains(questionId);
  }

  /// الحصول على جميع الأسئلة الخاطئة لجميع المواد (للتوافق مع الكود القديم)
  Future<List<String>> getAllWrongQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final allKeys = prefs.getKeys();
    final wrongKeys = allKeys.where(
      (key) => key.startsWith(_wrongQuestionsKey),
    );

    final allWrong = <String>[];
    for (final key in wrongKeys) {
      final wrong = prefs.getStringList(key) ?? [];
      allWrong.addAll(wrong);
    }

    return allWrong.toSet().toList(); // إزالة المكررات
  }

  /// حفظ ملاحظة على سؤال
  Future<void> saveQuestionNote(String questionId, String note) async {
    final prefs = await SharedPreferences.getInstance();
    final notes = await getQuestionNotes();
    if (note.trim().isEmpty) {
      notes.remove(questionId);
    } else {
      notes[questionId] = note.trim();
    }
    await prefs.setString(_questionNotesKey, jsonEncode(notes));
  }

  /// الحصول على ملاحظة سؤال
  Future<String> getQuestionNote(String questionId) async {
    final notes = await getQuestionNotes();
    return notes[questionId] ?? '';
  }

  /// الحصول على جميع الملاحظات
  Future<Map<String, String>> getQuestionNotes() async {
    final prefs = await SharedPreferences.getInstance();
    final notesJson = prefs.getString(_questionNotesKey) ?? '{}';
    final notesMap = jsonDecode(notesJson) as Map<String, dynamic>;
    return notesMap.map((key, value) => MapEntry(key, value.toString()));
  }

  /// حفظ نتيجة سؤال (صحيح/خاطئ) مع ربطها بالمادة
  Future<void> saveQuestionResult(
    String questionId,
    bool isCorrect,
    String subjectId,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final results = await getQuestionResults();
    results[questionId] = isCorrect;
    await prefs.setString(_questionResultsKey, jsonEncode(results));

    // إضافة/إزالة من الأسئلة الخاطئة للمادة المحددة
    if (isCorrect) {
      await removeFromWrongQuestions(questionId, subjectId);
    } else {
      await addToWrongQuestions(questionId, subjectId);
    }
  }

  /// الحصول على نتيجة سؤال
  Future<bool?> getQuestionResult(String questionId) async {
    final results = await getQuestionResults();
    return results[questionId];
  }

  /// الحصول على جميع نتائج الأسئلة
  Future<Map<String, bool>> getQuestionResults() async {
    final prefs = await SharedPreferences.getInstance();
    final resultsJson = prefs.getString(_questionResultsKey) ?? '{}';
    final resultsMap = jsonDecode(resultsJson) as Map<String, dynamic>;
    return resultsMap.map((key, value) => MapEntry(key, value as bool));
  }

  /// حفظ إجابات المستخدم
  Future<void> saveUserAnswers(String questionId, List<String> answers) async {
    final prefs = await SharedPreferences.getInstance();
    final userAnswers = await getUserAnswers();
    userAnswers[questionId] = answers;
    await prefs.setString(_userAnswersKey, jsonEncode(userAnswers));
  }

  /// الحصول على إجابات المستخدم لسؤال
  Future<List<String>> getUserAnswersForQuestion(String questionId) async {
    final userAnswers = await getUserAnswers();
    return userAnswers[questionId] ?? [];
  }

  /// الحصول على جميع إجابات المستخدم
  Future<Map<String, List<String>>> getUserAnswers() async {
    final prefs = await SharedPreferences.getInstance();
    final answersJson = prefs.getString(_userAnswersKey) ?? '{}';
    final answersMap = jsonDecode(answersJson) as Map<String, dynamic>;
    return answersMap.map(
      (key, value) => MapEntry(key, List<String>.from(value as List)),
    );
  }

  /// مسح جميع البيانات
  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_favoriteQuestionsKey);
    await prefs.remove(_wrongQuestionsKey);
    await prefs.remove(_questionNotesKey);
    await prefs.remove(_questionResultsKey);
    await prefs.remove(_userAnswersKey);
  }

  /// الحصول على إحصائيات عامة لجميع المواد
  Future<Map<String, int>> getStatistics() async {
    final favorites = await getAllFavoriteQuestions();
    final wrongQuestions = await getAllWrongQuestions();
    final results = await getQuestionResults();

    final correctAnswers = results.values.where((result) => result).length;
    final totalAnswered = results.length;

    return {
      'favoriteCount': favorites.length,
      'wrongCount': wrongQuestions.length,
      'correctCount': correctAnswers,
      'totalAnswered': totalAnswered,
    };
  }

  /// الحصول على إحصائيات مادة معينة
  Future<Map<String, int>> getSubjectStatistics(String subjectId) async {
    final favorites = await getFavoriteQuestions(subjectId);
    final wrongQuestions = await getWrongQuestions(subjectId);
    final results = await getQuestionResults();

    final correctAnswers = results.values.where((result) => result).length;
    final totalAnswered = results.length;

    return {
      'favoriteCount': favorites.length,
      'wrongCount': wrongQuestions.length,
      'correctCount': correctAnswers,
      'totalAnswered': totalAnswered,
    };
  }

  /// تحميل الأسئلة المفضلة مع تفاصيلها
  Future<List<Question>> loadFavoriteQuestionsWithDetails() async {
    // هذه الدالة ستحتاج تطبيق مع ExamService
    // مؤقتاً نرجع قائمة فارغة
    return [];
  }

  /// تحميل الأسئلة الخاطئة مع تفاصيلها
  Future<List<Question>> loadWrongQuestionsWithDetails() async {
    // هذه الدالة ستحتاج تطبيق مع ExamService
    // مؤقتاً نرجع قائمة فارغة
    return [];
  }
}
