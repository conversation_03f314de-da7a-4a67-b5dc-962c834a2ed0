import 'package:flutter/foundation.dart';
import '../models/video_model.dart';
import 'local_videos_cache_service.dart';
import 'subject_videos_service.dart';

/// خدمة موحدة لإدارة الفيديوهات
/// تجمع بين التخزين المحلي والتحديث من Firebase
class UnifiedVideosService extends ChangeNotifier {
  static final UnifiedVideosService _instance = UnifiedVideosService._internal();
  static UnifiedVideosService get instance => _instance;
  UnifiedVideosService._internal();

  final LocalVideosCacheService _localCache = LocalVideosCacheService.instance;
  final SmartVideoUpdateService _updateService = SmartVideoUpdateService.instance;

  /// الحصول على فيديوهات مادة (من التخزين المحلي أولاً)
  Future<List<Video>> getSubjectVideos(String subjectId) async {
    try {
      // محاولة تحميل الفيديوهات من التخزين المحلي أولاً
      final localVideos = await _localCache.getSubjectVideos(subjectId);
      
      if (localVideos.isNotEmpty) {
        debugPrint('📚 تم تحميل ${localVideos.length} فيديو من التخزين المحلي للمادة $subjectId');
        return localVideos;
      }

      // إذا لم توجد فيديوهات محلياً، تحميل من Firebase
      debugPrint('📥 لا توجد فيديوهات محلية، تحميل من Firebase للمادة $subjectId');
      final success = await _updateService.downloadSubjectVideos(subjectId, 'المادة $subjectId');
      
      if (success) {
        return await _localCache.getSubjectVideos(subjectId);
      }

      return [];
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على فيديوهات المادة: $e');
      return [];
    }
  }

  /// فلترة فيديوهات الوحدة
  Future<List<Video>> getVideosByUnit(String subjectId, String unitId) async {
    final allVideos = await getSubjectVideos(subjectId);
    return _localCache.filterVideosByUnit(allVideos, unitId);
  }

  /// فلترة فيديوهات الدرس
  Future<List<Video>> getVideosByLesson(String subjectId, String lessonId) async {
    final allVideos = await getSubjectVideos(subjectId);
    return _localCache.filterVideosByLesson(allVideos, lessonId);
  }

  /// البحث في الفيديوهات
  Future<List<Video>> searchVideos(String subjectId, String query) async {
    final allVideos = await getSubjectVideos(subjectId);
    return _localCache.searchVideos(allVideos, query);
  }

  /// تحديث فيديوهات مادة واحدة
  Future<bool> updateSubjectVideos(String subjectId, String subjectName) async {
    final success = await _updateService.updateSubjectVideos(subjectId, subjectName);
    if (success) {
      notifyListeners();
    }
    return success;
  }

  /// تحديث جميع المواد المشترك بها (فيديوهات)
  Future<Map<String, bool>> updateAllSubscribedVideoSubjects() async {
    final results = await _updateService.updateAllSubscribedVideoSubjects();
    notifyListeners();
    return results;
  }

  /// تحميل فيديوهات مادة لأول مرة
  Future<bool> downloadSubjectVideos(String subjectId, String subjectName) async {
    final success = await _updateService.downloadSubjectVideos(subjectId, subjectName);
    if (success) {
      notifyListeners();
    }
    return success;
  }

  /// التحقق من الاشتراك في مادة (فيديوهات)
  Future<bool> isSubscribedToVideoSubject(String subjectId) async {
    return await _localCache.isSubscribedToVideoSubject(subjectId);
  }

  /// إلغاء الاشتراك في مادة (فيديوهات)
  Future<void> unsubscribeFromVideoSubject(String subjectId) async {
    await _localCache.removeSubscribedVideoSubject(subjectId);
    notifyListeners();
  }

  /// الحصول على المواد المشترك بها (فيديوهات)
  Future<List<String>> getSubscribedVideoSubjects() async {
    return await _localCache.getSubscribedVideoSubjects();
  }

  /// التحقق من وجود فيديوهات محفوظة للمادة
  Future<bool> hasSubjectVideos(String subjectId) async {
    return await _localCache.hasSubjectVideos(subjectId);
  }

  /// الحصول على حجم التخزين المؤقت للفيديوهات
  Future<Map<String, int>> getVideosCacheSize() async {
    return await _localCache.getVideosCacheSize();
  }

  /// مسح جميع البيانات المحلية للفيديوهات
  Future<void> clearAllVideoData() async {
    await _localCache.clearAllVideoData();
    notifyListeners();
  }

  /// الحصول على آخر وقت تحديث لمادة (فيديوهات)
  Future<DateTime?> getLastUpdateTime(String subjectId) async {
    return await _localCache.getLastUpdateTime(subjectId);
  }

  /// التحقق من وجود تحديثات للمادة (فيديوهات)
  Future<bool> hasUpdates(String subjectId) async {
    try {
      final lastUpdateTime = await getLastUpdateTime(subjectId);
      return await SubjectVideosService.instance.hasUpdates(
        subjectId,
        lastUpdateTime ?? DateTime(2000),
      );
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من تحديثات الفيديوهات: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الفيديوهات للمادة
  Future<Map<String, int>> getSubjectVideosStats(String subjectId) async {
    try {
      final allVideos = await getSubjectVideos(subjectId);

      return {
        'total': allVideos.length,
      };
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إحصائيات الفيديوهات: $e');
      return {'total': 0};
    }
  }

  /// الحصول على فيديو واحد بالمعرف
  Future<Video?> getVideoById(String subjectId, String videoId) async {
    try {
      final allVideos = await getSubjectVideos(subjectId);
      return allVideos.firstWhere(
        (video) => video.id == videoId,
        orElse: () => throw Exception('Video not found'),
      );
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الفيديو بالمعرف: $e');
      return null;
    }
  }

  /// الحصول على قائمة الوحدات التي تحتوي على فيديوهات
  Future<List<String>> getUnitsWithVideos(String subjectId) async {
    final allVideos = await getSubjectVideos(subjectId);
    final unitIds = allVideos
        .map((v) => v.unitId)
        .where((unitId) => unitId.isNotEmpty)
        .toSet()
        .toList();

    return unitIds;
  }

  /// الحصول على قائمة الدروس التي تحتوي على فيديوهات في وحدة معينة
  Future<List<String>> getLessonsWithVideos(String subjectId, String unitId) async {
    final allVideos = await getSubjectVideos(subjectId);
    final filteredVideos = allVideos
        .where((v) => v.unitId == unitId)
        .toList();

    final lessonIds = filteredVideos
        .map((v) => v.lessonId)
        .where((lessonId) => lessonId.isNotEmpty)
        .toSet()
        .toList();

    return lessonIds;
  }

  /// فلترة الفيديوهات حسب الوحدة والدرس معاً
  Future<List<Video>> getVideosByUnitAndLesson(
    String subjectId,
    String unitId,
    String lessonId,
  ) async {
    final allVideos = await getSubjectVideos(subjectId);
    return allVideos
        .where((v) => v.unitId == unitId && v.lessonId == lessonId)
        .toList();
  }
}
