@echo off
echo Fixing Firebase CMake version...
powershell -Command "(Get-Content 'build\windows\x64\extracted\firebase_cpp_sdk_windows\CMakeLists.txt') -replace 'cmake_minimum_required\(VERSION 3\.1\)', 'cmake_minimum_required(VERSION 3.10...4.1)' | Set-Content 'build\windows\x64\extracted\firebase_cpp_sdk_windows\CMakeLists.txt'"
echo Firebase CMake version fixed!
echo Now you can run: flutter run -d windows --dart-define=FLAVOR=student
