rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Public collections rules - allow authenticated users to write
    match /subjects/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /exams/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /units/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /lessons/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /questions/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /courses/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /videos/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Public settings
    match /settings/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /pricing_messages/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // Video sections rules
    match /video_sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // Test sections rules
    match /test_sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // Admin messages rules
    match /admin_messages/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // Announcements rules
    match /announcements/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // User data rules (for authenticated users only)
    match /user_statistics/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_favorites/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_wrong_answers/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_subscriptions/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_notes/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_video_progress/{deviceId} {
      allow read, write: if request.auth != null;
    }

    // Codes rules (read and write for authenticated users)
    match /subscription_codes/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    match /activation_codes/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // Rules for subcollections
    match /{path=**}/questions/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /{path=**}/videos/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // General rules for other collections
    match /app_config/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /version_info/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // Default rules for any other collections
    match /{document=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
