# تحديث قواعد أمان Firebase

## المشكلة
تطبيق الإدارة لا يستطيع إنشاء أو تعديل البيانات في Firebase بسبب قواعد الأمان الحالية.

## الحل
تم تحديث قواعد الأمان في ملف `firestore.rules` للسماح للمستخدمين المسجلين بالكتابة.

## خطوات التطبيق

### 1. افتح Firebase Console
- اذهب إلى [Firebase Console](https://console.firebase.google.com)
- اختر مشروع `smart-test-app`

### 2. انتقل إلى Firestore Database
- من القائمة الجانبية، اختر "Firestore Database"
- اختر تبويب "Rules"

### 3. استبدل القواعد الحالية بالقواعد الجديدة
انسخ والصق القواعد التالية:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // قواعد للمجموعات العامة - السماح للمستخدمين المسجلين بالكتابة
    // Public collections rules - allow authenticated users to write
    match /subjects/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /exams/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /units/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /lessons/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /questions/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /courses/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /videos/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // قواعد للإعدادات العامة
    // Public settings
    match /settings/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /pricing_messages/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد لأقسام الفيديوهات
    // Video sections rules
    match /video_sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد لأقسام الاختبارات
    // Test sections rules
    match /test_sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد للرسائل الإدارية
    // Admin messages rules
    match /admin_messages/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد للإعلانات
    // Announcements rules
    match /announcements/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // قواعد لبيانات المستخدمين (للمستخدمين المسجلين فقط)
    // User data rules (for authenticated users only)
    match /user_statistics/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_favorites/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_wrong_answers/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_subscriptions/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_notes/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_video_progress/{deviceId} {
      allow read, write: if request.auth != null;
    }

    // قواعد للأكواد (قراءة وكتابة للمستخدمين المسجلين)
    // Codes rules (read and write for authenticated users)
    match /subscription_codes/{document} {
      allow read: if request.auth != null; // للتحقق من صحة الكود
      allow write: if request.auth != null; // لتحديث حالة الكود عند الاستخدام
    }

    match /activation_codes/{document} {
      allow read: if request.auth != null; // للتحقق من صحة الكود
      allow write: if request.auth != null; // لتحديث حالة الكود عند الاستخدام
    }

    // قواعد للمجموعات الفرعية في الوثائق
    // Rules for subcollections
    match /{path=**}/questions/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /{path=**}/videos/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد عامة للمجموعات الأخرى
    // General rules for other collections
    match /app_config/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /version_info/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد افتراضية لأي مجموعات أخرى
    // Default rules for any other collections
    match /{document=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### 4. انشر القواعد
- اضغط على زر "Publish" لتطبيق القواعد الجديدة

## التغييرات المهمة
1. **السماح بالكتابة للمستخدمين المسجلين**: تم تغيير `allow write: if false` إلى `allow write: if request.auth != null`
2. **تهيئة Firebase Authentication**: تم إضافة تسجيل دخول مجهول لتطبيق الإدارة
3. **إصلاح تحميل المواد**: تم تصحيح طريقة تحميل المواد في أقسام الفيديوهات

## اختبار التطبيق
بعد تطبيق هذه التغييرات:
1. شغل تطبيق الإدارة وجرب إنشاء قسم أو مادة جديدة
2. شغل تطبيق الطالب وتحقق من ظهور المواد في أقسام الفيديوهات
