import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';

import 'subject_units_page.dart';
import 'subject_lessons_page.dart';
import 'favorite_questions_page.dart';
import 'wrong_questions_page.dart';
import 'course_questions_page.dart';
import 'subject_search_page.dart';

class SubjectDetailPage extends StatefulWidget {
  final Subject subject;
  final bool isFreeAccess; // هل هذا وصول مجاني للمادة؟

  const SubjectDetailPage({
    super.key,
    required this.subject,
    this.isFreeAccess = false,
  });

  @override
  State<SubjectDetailPage> createState() => _SubjectDetailPageState();
}

class _SubjectDetailPageState extends State<SubjectDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 240.h, // زيادة الارتفاع لتجنب التداخل
              floating: false,
              pinned: true,
              backgroundColor: _getSubjectColor(),
              actions: [
                // زر تحديث أسئلة المادة
                IconButton(
                  icon: Icon(Icons.refresh),
                  onPressed: () {
                    // يمكن إضافة منطق تحديث الأسئلة هنا لاحقاً
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('سيتم إضافة تحديث الأسئلة قريباً'),
                      ),
                    );
                  },
                  tooltip: 'تحديث الأسئلة',
                ),
                // زر البحث في الزاوية العلوية اليسرى
                IconButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            SubjectSearchPage(subject: widget.subject),
                      ),
                    );
                  },
                  icon: const Icon(Icons.search, color: Colors.white, size: 24),
                  tooltip: 'البحث في الأسئلة',
                ),
                SizedBox(width: 8.w),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        _getSubjectColor(),
                        _getSubjectColor().withValues(alpha: 0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(
                        20.w,
                        20.w,
                        20.w,
                        80.h,
                      ), // إضافة مساحة في الأسفل للتبويبات
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment:
                            MainAxisAlignment.center, // تغيير إلى center
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 60.w,
                                height: 60.h,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(16.r),
                                ),
                                child: Icon(
                                  _getSubjectIcon(),
                                  color: Colors.white,
                                  size: 30.sp,
                                ),
                              ),
                              SizedBox(width: 16.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.subject.name,
                                      style: TextStyle(
                                        fontSize: 24.sp,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                      maxLines: 2, // تحديد عدد الأسطر
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      widget.subject.description,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colors.white.withValues(
                                          alpha: 0.9,
                                        ),
                                      ),
                                      maxLines: 2, // تحديد عدد الأسطر
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              bottom: PreferredSize(
                preferredSize: Size.fromHeight(50.h),
                child: Container(
                  color: _getSubjectColor(),
                  child: TabBar(
                    controller: _tabController,
                    indicatorColor: Colors.white,
                    indicatorWeight: 3,
                    labelColor: Colors.white,
                    unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                    labelStyle: TextStyle(
                      fontSize: 11.sp, // تصغير حجم الخط
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: TextStyle(
                      fontSize: 11.sp,
                      fontWeight: FontWeight.normal,
                    ),
                    isScrollable: true, // جعل التبويبات قابلة للتمرير
                    tabs: const [
                      Tab(
                        icon: Icon(Icons.folder_outlined, size: 20),
                        text: 'الوحدات',
                      ),
                      Tab(
                        icon: Icon(Icons.play_lesson_outlined, size: 20),
                        text: 'الدروس',
                      ),
                      Tab(
                        icon: Icon(Icons.star_outline, size: 20),
                        text: 'المفضلة',
                      ),
                      Tab(
                        icon: Icon(Icons.error_outline, size: 20),
                        text: 'الخاطئة',
                      ),
                      Tab(
                        icon: Icon(Icons.school_outlined, size: 20),
                        text: 'الدورات',
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            // تبويب الوحدات
            SubjectUnitsPage(
              subject: widget.subject,
              isFreeAccess: widget.isFreeAccess,
            ),

            // تبويب الدروس
            SubjectLessonsPage(
              subject: widget.subject,
              isFreeAccess: widget.isFreeAccess,
            ),

            // تبويب الأسئلة المفضلة
            FavoriteQuestionsPage(
              subject: widget.subject,
              isFreeAccess: widget.isFreeAccess,
            ),

            // تبويب الأسئلة الخاطئة
            WrongQuestionsPage(
              subject: widget.subject,
              isFreeAccess: widget.isFreeAccess,
            ),

            // تبويب الدورات
            CourseQuestionsPage(
              subject: widget.subject,
              isFreeAccess: widget.isFreeAccess,
            ),
          ],
        ),
      ),
    );
  }

  Color _getSubjectColor() {
    final name = widget.subject.name.toLowerCase();
    if (name.contains('رياضيات') || name.contains('math')) {
      return AppTheme.primaryColor;
    } else if (name.contains('فيزياء') || name.contains('physics')) {
      return AppTheme.secondaryColor;
    } else if (name.contains('كيمياء') || name.contains('chemistry')) {
      return AppTheme.accentColor;
    } else if (name.contains('أحياء') || name.contains('biology')) {
      return AppTheme.successColor;
    } else if (name.contains('عربية') || name.contains('arabic')) {
      return AppTheme.warningColor;
    } else if (name.contains('إنجليزية') || name.contains('english')) {
      return AppTheme.errorColor;
    }
    return AppTheme.primaryColor;
  }

  IconData _getSubjectIcon() {
    final name = widget.subject.name.toLowerCase();
    if (name.contains('رياضيات') || name.contains('math')) {
      return Icons.calculate;
    } else if (name.contains('فيزياء') || name.contains('physics')) {
      return Icons.science;
    } else if (name.contains('كيمياء') || name.contains('chemistry')) {
      return Icons.biotech;
    } else if (name.contains('أحياء') || name.contains('biology')) {
      return Icons.eco;
    } else if (name.contains('عربية') || name.contains('arabic')) {
      return Icons.language;
    } else if (name.contains('إنجليزية') || name.contains('english')) {
      return Icons.translate;
    }
    return Icons.book;
  }
}
