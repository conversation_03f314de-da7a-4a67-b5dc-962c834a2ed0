import 'package:cloud_firestore/cloud_firestore.dart';

enum ResultStatus {
  inProgress,
  completed,
  submitted,
  graded,
}

class QuestionAnswer {
  final String questionId;
  final List<String> selectedAnswers;
  final bool isCorrect;
  final int pointsEarned;
  final DateTime answeredAt;

  const QuestionAnswer({
    required this.questionId,
    required this.selectedAnswers,
    required this.isCorrect,
    required this.pointsEarned,
    required this.answeredAt,
  });

  factory QuestionAnswer.fromMap(Map<String, dynamic> map) {
    return QuestionAnswer(
      questionId: map['questionId'] ?? '',
      selectedAnswers: List<String>.from(map['selectedAnswers'] ?? []),
      isCorrect: map['isCorrect'] ?? false,
      pointsEarned: map['pointsEarned'] ?? 0,
      answeredAt: (map['answeredAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'questionId': questionId,
      'selectedAnswers': selectedAnswers,
      'isCorrect': isCorrect,
      'pointsEarned': pointsEarned,
      'answeredAt': Timestamp.fromDate(answeredAt),
    };
  }
}

class ExamResult {
  final String id;
  final String examId;
  final String userId;
  final String deviceId;
  final List<QuestionAnswer> answers;
  final int totalPoints;
  final int earnedPoints;
  final double percentage;
  final bool isPassed;
  final ResultStatus status;
  final DateTime startedAt;
  final DateTime? completedAt;
  final DateTime? submittedAt;
  final int timeSpent; // بالثواني
  final int attemptNumber;
  final Map<String, dynamic> metadata;

  const ExamResult({
    required this.id,
    required this.examId,
    required this.userId,
    required this.deviceId,
    required this.answers,
    required this.totalPoints,
    required this.earnedPoints,
    required this.percentage,
    required this.isPassed,
    required this.status,
    required this.startedAt,
    this.completedAt,
    this.submittedAt,
    required this.timeSpent,
    required this.attemptNumber,
    required this.metadata,
  });

  factory ExamResult.fromMap(Map<String, dynamic> map) {
    return ExamResult(
      id: map['id'] ?? '',
      examId: map['examId'] ?? '',
      userId: map['userId'] ?? '',
      deviceId: map['deviceId'] ?? '',
      answers: (map['answers'] as List<dynamic>?)
          ?.map((answer) => QuestionAnswer.fromMap(answer))
          .toList() ?? [],
      totalPoints: map['totalPoints'] ?? 0,
      earnedPoints: map['earnedPoints'] ?? 0,
      percentage: (map['percentage'] ?? 0.0).toDouble(),
      isPassed: map['isPassed'] ?? false,
      status: ResultStatus.values.firstWhere(
        (e) => e.toString() == 'ResultStatus.${map['status']}',
        orElse: () => ResultStatus.inProgress,
      ),
      startedAt: (map['startedAt'] as Timestamp).toDate(),
      completedAt: map['completedAt'] != null 
          ? (map['completedAt'] as Timestamp).toDate() 
          : null,
      submittedAt: map['submittedAt'] != null 
          ? (map['submittedAt'] as Timestamp).toDate() 
          : null,
      timeSpent: map['timeSpent'] ?? 0,
      attemptNumber: map['attemptNumber'] ?? 1,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'examId': examId,
      'userId': userId,
      'deviceId': deviceId,
      'answers': answers.map((answer) => answer.toMap()).toList(),
      'totalPoints': totalPoints,
      'earnedPoints': earnedPoints,
      'percentage': percentage,
      'isPassed': isPassed,
      'status': status.toString().split('.').last,
      'startedAt': Timestamp.fromDate(startedAt),
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'submittedAt': submittedAt != null ? Timestamp.fromDate(submittedAt!) : null,
      'timeSpent': timeSpent,
      'attemptNumber': attemptNumber,
      'metadata': metadata,
    };
  }

  ExamResult copyWith({
    String? id,
    String? examId,
    String? userId,
    String? deviceId,
    List<QuestionAnswer>? answers,
    int? totalPoints,
    int? earnedPoints,
    double? percentage,
    bool? isPassed,
    ResultStatus? status,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? submittedAt,
    int? timeSpent,
    int? attemptNumber,
    Map<String, dynamic>? metadata,
  }) {
    return ExamResult(
      id: id ?? this.id,
      examId: examId ?? this.examId,
      userId: userId ?? this.userId,
      deviceId: deviceId ?? this.deviceId,
      answers: answers ?? this.answers,
      totalPoints: totalPoints ?? this.totalPoints,
      earnedPoints: earnedPoints ?? this.earnedPoints,
      percentage: percentage ?? this.percentage,
      isPassed: isPassed ?? this.isPassed,
      status: status ?? this.status,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      submittedAt: submittedAt ?? this.submittedAt,
      timeSpent: timeSpent ?? this.timeSpent,
      attemptNumber: attemptNumber ?? this.attemptNumber,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  String get statusDisplayName {
    switch (status) {
      case ResultStatus.inProgress:
        return 'قيد التقدم';
      case ResultStatus.completed:
        return 'مكتمل';
      case ResultStatus.submitted:
        return 'مُرسل';
      case ResultStatus.graded:
        return 'مُقيم';
    }
  }

  String get gradeDisplayName {
    if (percentage >= 90) return 'ممتاز';
    if (percentage >= 80) return 'جيد جداً';
    if (percentage >= 70) return 'جيد';
    if (percentage >= 60) return 'مقبول';
    return 'راسب';
  }

  int get correctAnswersCount {
    return answers.where((answer) => answer.isCorrect).length;
  }

  int get totalQuestionsCount => answers.length;

  String get timeSpentDisplayText {
    final hours = timeSpent ~/ 3600;
    final minutes = (timeSpent % 3600) ~/ 60;
    final seconds = timeSpent % 60;

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    }
  }

  Duration get duration {
    if (completedAt != null) {
      return completedAt!.difference(startedAt);
    }
    return Duration.zero;
  }
}
