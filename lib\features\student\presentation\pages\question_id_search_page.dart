import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/content_service.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';

class QuestionIdSearchPage extends StatefulWidget {
  const QuestionIdSearchPage({super.key});

  @override
  State<QuestionIdSearchPage> createState() => _QuestionIdSearchPageState();
}

class _QuestionIdSearchPageState extends State<QuestionIdSearchPage> {
  final TextEditingController _idController = TextEditingController();
  final FocusNode _idFocusNode = FocusNode();
  bool _isSearching = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // التركيز على حقل البحث عند فتح الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _idFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _idController.dispose();
    _idFocusNode.dispose();
    super.dispose();
  }

  Future<void> _searchById() async {
    final questionId = _idController.text.trim();

    if (questionId.isEmpty) {
      setState(() {
        _errorMessage = 'يرجى إدخال رقم السؤال';
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _errorMessage = '';
    });

    try {
      // البحث عن السؤال بالرقم
      final question = await ExamService.instance.getQuestionById(questionId);

      if (question != null) {
        // الحصول على معلومات المادة
        final subject = await ContentService.instance.getSubjectById(
          question.subjectId,
        );

        if (subject != null && mounted) {
          // الانتقال إلى عارض الأسئلة مع السؤال المحدد
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => QuestionsViewerPage(
                title: 'السؤال رقم $questionId',
                subject: subject,
                questionType: QuestionFilterType.single,
                singleQuestionId: questionId,
                isFreeAccess: true, // السماح بالوصول للسؤال المشارك
              ),
            ),
          );
        } else {
          setState(() {
            _errorMessage = 'خطأ في تحميل معلومات المادة';
          });
        }
      } else {
        setState(() {
          _errorMessage = 'لم يتم العثور على سؤال بهذا الرقم';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في البحث: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          'البحث برقم السؤال',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.getPrimaryGradient(context),
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              // المحتوى الرئيسي
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SizedBox(height: 40.h),

                      // أيقونة البحث
                      Icon(
                        Icons.search,
                        size: 60.w,
                        color: AppTheme.primaryColor,
                      ),

                      SizedBox(height: 20.h),

                      // عنوان
                      Text(
                        'ابحث عن سؤال محدد',
                        style: TextStyle(
                          fontSize: 22.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      SizedBox(height: 8.h),

                      // وصف
                      Text(
                        'أدخل رقم السؤال للعثور عليه مباشرة',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),

                      SizedBox(height: 30.h),

                      // حقل إدخال رقم السؤال
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: TextField(
                          controller: _idController,
                          focusNode: _idFocusNode,
                          keyboardType: TextInputType.text,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w600,
                          ),
                          decoration: InputDecoration(
                            hintText: 'أدخل رقم السؤال',
                            hintStyle: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 16.sp,
                            ),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 20.w,
                              vertical: 16.h,
                            ),
                          ),
                          onSubmitted: (_) => _searchById(),
                        ),
                      ),

                      SizedBox(height: 24.h),

                      // زر البحث
                      ElevatedButton(
                        onPressed: _isSearching ? null : _searchById,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16.r),
                          ),
                          elevation: 4,
                        ),
                        child: _isSearching
                            ? SizedBox(
                                height: 20.h,
                                width: 20.w,
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.search, size: 20.w),
                                  SizedBox(width: 8.w),
                                  Text(
                                    'بحث',
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                      ),

                      // رسالة الخطأ
                      if (_errorMessage.isNotEmpty) ...[
                        SizedBox(height: 16.h),
                        Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12.r),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 20.w,
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Text(
                                  _errorMessage,
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],

                      SizedBox(height: 32.h),

                      // نصائح
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.lightbulb_outline,
                                  color: AppTheme.primaryColor,
                                  size: 20.w,
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  'نصائح:',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              '• يمكنك الحصول على رقم السؤال من خلال مشاركة السؤال\n'
                              '• يمكنك نسخ الرقم ولصقه هنا',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppTheme.textPrimaryColor,
                                height: 1.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
