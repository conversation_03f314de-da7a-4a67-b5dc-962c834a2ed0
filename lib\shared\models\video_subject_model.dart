import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج مواد الفيديوهات (مثل الرياضيات، الفيزياء)
class VideoSubject {
  final String id;
  final String sectionId; // معرف القسم الذي تنتمي إليه المادة
  final String name;
  final String description;
  final String iconUrl;
  final String color;
  final int order;
  final bool isActive;
  final bool isFree; // هل المادة مجانية أم مدفوعة
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdByAdminId;

  const VideoSubject({
    required this.id,
    required this.sectionId,
    required this.name,
    required this.description,
    this.iconUrl = '',
    this.color = '#6C5CE7',
    this.order = 0,
    this.isActive = true,
    this.isFree = false, // افتراضياً مدفوعة
    required this.createdAt,
    required this.updatedAt,
    required this.createdByAdminId,
  });

  factory VideoSubject.fromMap(Map<String, dynamic> map) {
    return VideoSubject(
      id: map['id'] ?? '',
      sectionId: map['sectionId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      color: map['color'] ?? '#6C5CE7',
      order: map['order'] ?? 0,
      isActive: map['isActive'] ?? true,
      isFree: map['isFree'] ?? false,
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      createdByAdminId: map['createdByAdminId'] ?? '',
    );
  }

  /// تحويل إلى Map للتخزين المحلي الدائم
  Map<String, dynamic> toLocalMap() {
    return {
      'id': id,
      'sectionId': sectionId,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'order': order,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdByAdminId': createdByAdminId,
    };
  }

  /// إنشاء من Map للتخزين المحلي الدائم
  factory VideoSubject.fromLocalMap(Map<String, dynamic> map) {
    return VideoSubject(
      id: map['id'] ?? '',
      sectionId: map['sectionId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      color: map['color'] ?? '#6C5CE7',
      order: map['order'] ?? 0,
      isActive: map['isActive'] ?? true,
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      createdByAdminId: map['createdByAdminId'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sectionId': sectionId,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'order': order,
      'isActive': isActive,
      'isFree': isFree,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'createdByAdminId': createdByAdminId,
    };
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    if (value is String) return DateTime.tryParse(value) ?? DateTime.now();
    return DateTime.now();
  }

  VideoSubject copyWith({
    String? id,
    String? sectionId,
    String? name,
    String? description,
    String? iconUrl,
    String? color,
    int? order,
    bool? isActive,
    bool? isFree,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByAdminId,
  }) {
    return VideoSubject(
      id: id ?? this.id,
      sectionId: sectionId ?? this.sectionId,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      color: color ?? this.color,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      isFree: isFree ?? this.isFree,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
    );
  }

  // دوال Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'sectionId': sectionId,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'order': order,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'createdByAdminId': createdByAdminId,
    };
  }

  factory VideoSubject.fromFirestore(
    Map<String, dynamic> data,
    String documentId,
  ) {
    return VideoSubject(
      id: documentId,
      sectionId: data['sectionId'] ?? '',
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      iconUrl: data['iconUrl'] ?? '',
      color: data['color'] ?? '#6C5CE7',
      order: data['order'] ?? 0,
      isActive: data['isActive'] ?? true,
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
      createdByAdminId: data['createdByAdminId'] ?? '',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoSubject && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoSubject(id: $id, sectionId: $sectionId, name: $name, description: $description, isActive: $isActive)';
  }
}
