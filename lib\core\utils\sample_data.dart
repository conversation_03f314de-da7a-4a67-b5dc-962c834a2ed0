import 'package:cloud_firestore/cloud_firestore.dart';
import 'logger.dart';
import '../../shared/models/subject_model.dart';
import '../../shared/models/subscription_code_model.dart';
import '../../shared/models/question_model.dart';
import '../../shared/models/exam_model.dart';

class SampleData {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إضافة المواد التجريبية
  static Future<void> addSampleSubjects() async {
    try {
      final subjects = [
        Subject(
          id: 'math',
          name: 'الرياضيات',
          description: 'مادة الرياضيات للصف الثالث الثانوي',
          iconUrl: '',
          color: '#6C5CE7',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'physics',
          name: 'الفيزياء',
          description: 'مادة الفيزياء للصف الثالث الثانوي',
          iconUrl: '',
          color: '#00CEC9',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'chemistry',
          name: 'الكيمياء',
          description: 'مادة الكيمياء للصف الثالث الثانوي',
          iconUrl: '',
          color: '#FF7675',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'biology',
          name: 'الأحياء',
          description: 'مادة الأحياء للصف الثالث الثانوي',
          iconUrl: '',
          color: '#00B894',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'arabic',
          name: 'اللغة العربية',
          description: 'مادة اللغة العربية للصف الثالث الثانوي',
          iconUrl: '',
          color: '#FFBE0B',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'english',
          name: 'اللغة الإنجليزية',
          description: 'مادة اللغة الإنجليزية للصف الثالث الثانوي',
          iconUrl: '',
          color: '#E17055',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      for (final subject in subjects) {
        await _firestore
            .collection('subjects')
            .doc(subject.id)
            .set(subject.toMap());
        Logger.success('تم إضافة مادة: ${subject.name}');
      }

      Logger.success('✅ تم إضافة جميع المواد بنجاح');
    } catch (e) {
      Logger.error('❌ خطأ في إضافة المواد', e);
    }
  }

  /// إضافة أكواد الاشتراك التجريبية
  static Future<void> addSampleCodes() async {
    try {
      final codes = [
        SubscriptionCode(
          id: 'code1',
          code: 'MATH2024',
          subjectIds: ['math'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي لمادة الرياضيات',
        ),
        SubscriptionCode(
          id: 'code2',
          code: 'PHYSICS2024',
          subjectIds: ['physics'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي لمادة الفيزياء',
        ),
        SubscriptionCode(
          id: 'code3',
          code: 'SCIENCE2024',
          subjectIds: ['physics', 'chemistry', 'biology'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي للمواد العلمية',
        ),
        SubscriptionCode(
          id: 'code4',
          code: 'ALLIN2024',
          subjectIds: [
            'math',
            'physics',
            'chemistry',
            'biology',
            'arabic',
            'english',
          ],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي لجميع المواد',
        ),
        SubscriptionCode(
          id: 'code5',
          code: 'LANG2024',
          subjectIds: ['arabic', 'english'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي للغات',
        ),
      ];

      for (final code in codes) {
        await _firestore
            .collection('subscription_codes')
            .doc(code.id)
            .set(code.toMap());
        Logger.success(
          'تم إضافة كود: ${code.code} للمواد: ${code.subjectIds.join(', ')}',
        );
      }

      Logger.success('✅ تم إضافة جميع الأكواد بنجاح');
    } catch (e) {
      Logger.error('❌ خطأ في إضافة الأكواد', e);
    }
  }

  /// إضافة الأسئلة التجريبية
  static Future<void> addSampleQuestions() async {
    try {
      final questions = [
        // أسئلة الرياضيات
        Question(
          id: 'math_q1',
          subjectId: 'math',
          unitId: 'math_unit1',
          lessonId: 'math_lesson1',
          isCourseQuestion: false,
          questionText: 'ما هو ناتج 2 + 2؟',
          type: QuestionType.multipleChoice,
          difficulty: DifficultyLevel.easy,
          contentType: QuestionContentType.text,
          options: ['3', '4', '5', '6'],
          correctAnswers: ['4'],
          explanation: 'ناتج جمع 2 + 2 = 4',
          points: 1,
          imageUrl: '',
          metadata: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
          isActive: true,
        ),
        Question(
          id: 'math_q2',
          subjectId: 'math',
          unitId: 'math_unit1',
          lessonId: 'math_lesson2',
          isCourseQuestion: true,
          questionText: 'هل العدد 7 عدد أولي؟',
          type: QuestionType.trueFalse,
          difficulty: DifficultyLevel.medium,
          contentType: QuestionContentType.text,
          options: ['صح', 'خطأ'],
          correctAnswers: ['صح'],
          explanation:
              'العدد 7 هو عدد أولي لأنه لا يقبل القسمة إلا على 1 وعلى نفسه',
          points: 2,
          imageUrl: '',
          metadata: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
          isActive: true,
        ),
        // أسئلة الفيزياء
        Question(
          id: 'physics_q1',
          subjectId: 'physics',
          unitId: 'physics_unit1',
          lessonId: 'physics_lesson1',
          isCourseQuestion: false,
          questionText: 'ما هي وحدة قياس القوة في النظام الدولي؟',
          type: QuestionType.multipleChoice,
          difficulty: DifficultyLevel.easy,
          contentType: QuestionContentType.text,
          options: ['نيوتن', 'جول', 'واط', 'أمبير'],
          correctAnswers: ['نيوتن'],
          explanation: 'النيوتن هو وحدة قياس القوة في النظام الدولي',
          points: 1,
          imageUrl: '',
          metadata: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
          isActive: true,
        ),
        Question(
          id: 'physics_q2',
          subjectId: 'physics',
          unitId: 'physics_unit1',
          lessonId: 'physics_lesson2',
          isCourseQuestion: true,
          questionText: 'سرعة الضوء في الفراغ تساوي تقريباً:',
          type: QuestionType.multipleChoice,
          difficulty: DifficultyLevel.medium,
          contentType: QuestionContentType.text,
          options: [
            '300,000 كم/ث',
            '300,000,000 م/ث',
            '150,000 كم/ث',
            '500,000 م/ث',
          ],
          correctAnswers: ['300,000,000 م/ث'],
          explanation:
              'سرعة الضوء في الفراغ تساوي تقريباً 300,000,000 متر في الثانية',
          points: 2,
          imageUrl: '',
          metadata: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
          isActive: true,
        ),
      ];

      for (final question in questions) {
        await _firestore
            .collection('questions')
            .doc(question.id)
            .set(question.toMap());
        Logger.success(
          'تم إضافة سؤال: ${question.questionText.substring(0, 30)}...',
        );
      }

      Logger.success('✅ تم إضافة جميع الأسئلة بنجاح');
    } catch (e) {
      Logger.error('❌ خطأ في إضافة الأسئلة', e);
    }
  }

  /// إضافة الاختبارات التجريبية
  static Future<void> addSampleExams() async {
    try {
      final exams = [
        Exam(
          id: 'math_exam1',
          title: 'اختبار الرياضيات الأساسي',
          description: 'اختبار تجريبي في الرياضيات يغطي المفاهيم الأساسية',
          subjectId: 'math',
          questionIds: ['math_q1', 'math_q2'],
          duration: 30,
          totalPoints: 3,
          passingScore: 60.0,
          status: ExamStatus.published,
          startTime: DateTime.now(),
          endTime: DateTime.now().add(const Duration(days: 30)),
          shuffleQuestions: true,
          shuffleOptions: true,
          showResults: true,
          allowRetake: true,
          maxAttempts: 3,
          settings: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
        ),
        Exam(
          id: 'physics_exam1',
          title: 'اختبار الفيزياء الأساسي',
          description: 'اختبار تجريبي في الفيزياء يغطي المفاهيم الأساسية',
          subjectId: 'physics',
          questionIds: ['physics_q1', 'physics_q2'],
          duration: 45,
          totalPoints: 3,
          passingScore: 70.0,
          status: ExamStatus.published,
          startTime: DateTime.now(),
          endTime: DateTime.now().add(const Duration(days: 30)),
          shuffleQuestions: false,
          shuffleOptions: true,
          showResults: true,
          allowRetake: false,
          maxAttempts: 1,
          settings: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
        ),
      ];

      for (final exam in exams) {
        await _firestore.collection('exams').doc(exam.id).set(exam.toMap());
        Logger.success('تم إضافة اختبار: ${exam.title}');
      }

      Logger.success('✅ تم إضافة جميع الاختبارات بنجاح');
    } catch (e) {
      Logger.error('❌ خطأ في إضافة الاختبارات', e);
    }
  }

  /// إضافة جميع البيانات التجريبية
  static Future<void> addAllSampleData() async {
    Logger.start('🚀 بدء إضافة البيانات التجريبية...');

    await addSampleSubjects();
    await Future.delayed(const Duration(seconds: 2));

    await addSampleCodes();
    await Future.delayed(const Duration(seconds: 2));

    await addSampleQuestions();
    await Future.delayed(const Duration(seconds: 2));

    await addSampleExams();

    Logger.success('🎉 تم إضافة جميع البيانات التجريبية بنجاح!');
  }

  /// حذف جميع البيانات التجريبية
  static Future<void> clearAllData() async {
    try {
      Logger.start('🗑️ بدء حذف البيانات...');

      // حذف المواد
      final subjectsSnapshot = await _firestore.collection('subjects').get();
      for (final doc in subjectsSnapshot.docs) {
        await doc.reference.delete();
      }

      // حذف الأكواد
      final codesSnapshot = await _firestore
          .collection('subscription_codes')
          .get();
      for (final doc in codesSnapshot.docs) {
        await doc.reference.delete();
      }

      // حذف الاشتراكات
      final subscriptionsSnapshot = await _firestore
          .collection('user_subscriptions')
          .get();
      for (final doc in subscriptionsSnapshot.docs) {
        await doc.reference.delete();
      }

      Logger.success('✅ تم حذف جميع البيانات بنجاح');
    } catch (e) {
      Logger.error('❌ خطأ في حذف البيانات', e);
    }
  }

  /// عرض إحصائيات البيانات
  static Future<void> showDataStats() async {
    try {
      final subjectsCount =
          (await _firestore.collection('subjects').get()).docs.length;
      final codesCount =
          (await _firestore.collection('subscription_codes').get()).docs.length;
      final subscriptionsCount =
          (await _firestore.collection('user_subscriptions').get()).docs.length;

      Logger.info('📊 إحصائيات البيانات:');
      Logger.info('   المواد: $subjectsCount');
      Logger.info('   الأكواد: $codesCount');
      Logger.info('   الاشتراكات: $subscriptionsCount');
    } catch (e) {
      Logger.error('❌ خطأ في عرض الإحصائيات', e);
    }
  }
}
