# 🎓 نظام الاشتراكات والأكواد - Smart Test App

## 📋 نظرة عامة

تم تطوير نظام متقدم لإدارة الاشتراكات والأكواد في تطبيق Smart Test، يضمن الأمان والتحكم الكامل في الوصول للمواد التعليمية.

## 🔐 المميزات الرئيسية

### ✅ **نظام Device ID الفريد**
- كل جهاز له معرف مشفر فريد
- استخدام `device_info_plus` و `crypto` للتشفير الآمن
- حفظ محلي للمعرف باستخدام `shared_preferences`

### ✅ **نظام الأكواد المتقدم**
- **كود واحد = جهاز واحد**: كل كود يمكن استخدامه على جهاز واحد فقط
- **استخدام واحد**: الكود يُعطل تلقائياً بعد الاستخدام
- **تتبع شامل**: تسجيل تاريخ الاستخدام ومعرف الجهاز
- **انتهاء صلاحية**: أكواد مؤقتة مع تواريخ انتهاء

### ✅ **إدارة الاشتراكات**
- ربط المواد بالأجهزة
- تتبع حالة كل اشتراك
- إحصائيات مفصلة للمستخدم
- واجهة سهلة الاستخدام

## 🏗️ البنية التقنية

### **Models (النماذج)**
```
lib/shared/models/
├── subject_model.dart          # نموذج المواد الدراسية
├── subscription_code_model.dart # نموذج أكواد الاشتراك
└── user_subscription_model.dart # نموذج اشتراك المستخدم
```

### **Services (الخدمات)**
```
lib/shared/services/
├── device_service.dart         # خدمة معرف الجهاز
├── firebase_service.dart       # خدمة Firebase
└── subscription_service.dart   # خدمة إدارة الاشتراكات
```

### **UI Components (واجهات المستخدم)**
```
lib/features/student/presentation/
├── pages/
│   └── subscription_activation_page.dart  # صفحة تفعيل الاشتراك
└── widgets/
    ├── code_input_widget.dart             # widget إدخال الكود
    ├── subject_selection_widget.dart      # widget عرض المواد
    └── device_info_widget.dart            # widget معلومات الجهاز
```

## 🎨 واجهات المستخدم

### **تطبيق الطالب**
- **صفحة تفعيل الاشتراك** مع 3 تبويبات:
  1. **تفعيل الكود**: إدخال وتفعيل الأكواد
  2. **المواد المتاحة**: عرض المواد المفعلة وغير المفعلة
  3. **معلومات الجهاز**: عرض معرف الجهاز ومعلومات الأمان

### **تطبيق الأدمن**
- **صفحة إدارة البيانات التجريبية**:
  - إضافة المواد التجريبية
  - إضافة الأكواد التجريبية
  - عرض الإحصائيات
  - حذف البيانات

## 🔧 الإعداد والتشغيل

### **1. Firebase Setup**
```bash
# تأكد من إنشاء Firestore Index للمواد
Collection: subjects
Fields: isActive (Ascending), name (Ascending)
```

### **2. تشغيل التطبيقات**
```bash
# تطبيق الطالب
flutter run --flavor student -t lib/main_student.dart

# تطبيق الأدمن
flutter run --flavor admin -t lib/main_admin.dart
```

### **3. إضافة البيانات التجريبية**
1. افتح تطبيق الأدمن
2. اضغط على "البيانات التجريبية"
3. اضغط على "إضافة الكل"

## 🎯 الأكواد التجريبية

| الكود | المواد المشمولة |
|-------|-----------------|
| `MATH2024` | الرياضيات |
| `PHYSICS2024` | الفيزياء |
| `SCIENCE2024` | المواد العلمية (فيزياء، كيمياء، أحياء) |
| `ALLIN2024` | جميع المواد |
| `LANG2024` | اللغات (عربي، إنجليزي) |

## 🔒 الأمان والحماية

### **Device Binding**
- كل كود مرتبط بجهاز واحد فقط
- معرف الجهاز مشفر ومحمي
- لا يمكن نقل الاشتراك لجهاز آخر

### **Code Security**
- الأكواد تُعطل تلقائياً بعد الاستخدام
- تتبع شامل لجميع محاولات الاستخدام
- تواريخ انتهاء صلاحية

### **Firebase Security**
- استخدام Firebase Authentication
- قواعد Firestore Security Rules
- تشفير البيانات الحساسة

## 📊 قاعدة البيانات

### **Collections**
```
Firestore Collections:
├── subjects/              # المواد الدراسية
├── subscription_codes/    # أكواد الاشتراك
└── user_subscriptions/    # اشتراكات المستخدمين
```

### **Data Flow**
1. المستخدم يدخل الكود
2. التحقق من صحة الكود وحالته
3. ربط الكود بمعرف الجهاز
4. تفعيل المواد المرتبطة بالكود
5. تحديث حالة الكود إلى "مستخدم"

## 🚀 المميزات المتقدمة

### **Real-time Updates**
- تحديث فوري لحالة الاشتراكات
- مزامنة البيانات عبر الأجهزة
- إشعارات الحالة

### **Offline Support**
- حفظ محلي لمعرف الجهاز
- تخزين مؤقت للبيانات
- مزامنة عند الاتصال

### **Analytics & Monitoring**
- تتبع استخدام الأكواد
- إحصائيات الاشتراكات
- مراقبة الأداء

## 🎨 التصميم

### **Material Design 3**
- ألوان عصرية ومتدرجة
- تأثيرات بصرية جذابة
- واجهات responsive

### **Arabic RTL Support**
- دعم كامل للغة العربية
- تخطيط من اليمين لليسار
- خطوط عربية جميلة

## 🔄 التحديثات المستقبلية

### **المخطط لها**
- [ ] نظام الإشعارات Push Notifications
- [ ] تقارير مفصلة للأدمن
- [ ] نظام الدفع المتكامل
- [ ] دعم المزيد من أنواع الاشتراكات
- [ ] واجهة ويب للإدارة

## 📞 الدعم والمساعدة

### **للمطورين**
- كود مُوثق بالكامل
- بنية واضحة ومنظمة
- أمثلة شاملة

### **للمستخدمين**
- واجهات سهلة الاستخدام
- رسائل خطأ واضحة
- مساعدة مدمجة

---

## 🏆 الخلاصة

تم تطوير نظام شامل ومتقدم لإدارة الاشتراكات والأكواد يضمن:
- **الأمان الكامل** للبيانات والأكواد
- **سهولة الاستخدام** للطلاب والإدارة
- **المرونة والقابلية للتوسع** للمستقبل
- **التصميم العصري** والجذاب

النظام جاهز للاستخدام الفوري ويمكن توسيعه بسهولة حسب الاحتياجات المستقبلية.
