import 'package:flutter/material.dart';

/// دالة مساعدة لتحديد اتجاه النص بذكاء
/// تعمل مثل تطبيقات المراسلة (تلغرام/واتساب)
class TextDirectionHelper {
  /// تحديد اتجاه النص بناءً على أول حرف له معنى
  static TextDirection getTextDirection(String text) {
    if (text.isEmpty) return TextDirection.rtl;
    
    // البحث عن أول حرف له اتجاه محدد
    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      final code = char.codeUnitAt(0);
      
      // الأحرف العربية (0x0600 - 0x06FF)
      if ((code >= 0x0600 && code <= 0x06FF) ||
          // الأحرف العربية الممتدة (0x0750 - 0x077F)
          (code >= 0x0750 && code <= 0x077F) ||
          // الأحرف العربية التكميلية (0x08A0 - 0x08FF)
          (code >= 0x08A0 && code <= 0x08FF) ||
          // الأحرف العربية في Unicode (0xFB50 - 0xFDFF)
          (code >= 0xFB50 && code <= 0xFDFF) ||
          // الأحرف العربية في Unicode (0xFE70 - 0xFEFF)
          (code >= 0xFE70 && code <= 0xFEFF)) {
        return TextDirection.rtl;
      }
      
      // الأحرف اللاتينية (A-Z, a-z)
      if ((code >= 0x0041 && code <= 0x005A) ||
          (code >= 0x0061 && code <= 0x007A)) {
        return TextDirection.ltr;
      }
    }
    
    // إذا لم نجد أحرف محددة، نستخدم RTL كافتراضي
    return TextDirection.rtl;
  }

  /// إنشاء Text widget مع اتجاه نص ذكي
  static Widget buildSmartText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    double? height,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      textDirection: getTextDirection(text),
    );
  }

  /// إنشاء RichText widget مع اتجاه نص ذكي
  static Widget buildSmartRichText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    return RichText(
      text: TextSpan(
        text: text,
        style: style,
      ),
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.clip,
      textDirection: getTextDirection(text),
    );
  }
}
