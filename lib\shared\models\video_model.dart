import 'package:cloud_firestore/cloud_firestore.dart';

/// حالة تحميل الفيديو
enum VideoDownloadStatus {
  notDownloaded, // غير محمل
  downloading, // قيد التحميل
  downloaded, // محمل
  failed, // فشل التحميل
}

/// جودة الفيديو
enum VideoQuality {
  quality360, // جودة 360p
  quality480, // جودة 480p
  quality720, // جودة 720p
  quality1080, // جودة 1080p
  auto, // تلقائي حسب الشبكة
}

/// نموذج الفيديو مع التشفير والحماية
class Video {
  final String id;
  final String lessonId; // معرف الدرس
  final String unitId; // معرف الوحدة
  final String subjectId; // معرف المادة
  final String sectionId; // معرف القسم
  final String title; // عنوان الفيديو
  final String description; // وصف الفيديو
  final String? encryptedUrl360; // الرابط المشفر للفيديو بجودة 360p
  final String? encryptedUrl480; // الرابط المشفر للفيديو بجودة 480p
  final String? encryptedUrl720; // الرابط المشفر للفيديو بجودة 720p
  final String? encryptedUrl1080; // الرابط المشفر للفيديو بجودة 1080p
  final String thumbnailUrl; // صورة مصغرة للفيديو
  final int durationInSeconds; // مدة الفيديو بالثواني
  final int fileSizeInBytes; // حجم الملف بالبايت
  final int order; // ترتيب الفيديو في الدرس
  final bool isActive; // هل الفيديو نشط
  final bool isPreview; // هل هو فيديو معاينة مجاني
  final Map<String, dynamic> metadata; // بيانات إضافية
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdByAdminId;

  // بيانات التحميل المحلي
  final VideoDownloadStatus downloadStatus;
  final String? localEncryptedPath; // مسار الملف المشفر المحلي
  final DateTime? downloadedAt; // تاريخ التحميل
  final String? downloadHash; // hash للتحقق من سلامة الملف

  const Video({
    required this.id,
    required this.lessonId,
    required this.unitId,
    required this.subjectId,
    required this.sectionId,
    required this.title,
    required this.description,
    this.encryptedUrl360,
    this.encryptedUrl480,
    this.encryptedUrl720,
    this.encryptedUrl1080,
    this.thumbnailUrl = '',
    this.durationInSeconds = 0,
    this.fileSizeInBytes = 0,
    this.order = 0,
    this.isActive = true,
    this.isPreview = false,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    required this.createdByAdminId,
    this.downloadStatus = VideoDownloadStatus.notDownloaded,
    this.localEncryptedPath,
    this.downloadedAt,
    this.downloadHash,
  });

  factory Video.fromMap(Map<String, dynamic> map) {
    return Video(
      id: map['id'] ?? '',
      lessonId: map['lessonId'] ?? '',
      unitId: map['unitId'] ?? '',
      subjectId: map['subjectId'] ?? '',
      sectionId: map['sectionId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      encryptedUrl360: map['encryptedUrl360'],
      encryptedUrl480: map['encryptedUrl480'],
      encryptedUrl720: map['encryptedUrl720'],
      encryptedUrl1080: map['encryptedUrl1080'],
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      durationInSeconds: map['durationInSeconds'] ?? 0,
      fileSizeInBytes: map['fileSizeInBytes'] ?? 0,
      order: map['order'] ?? 0,
      isActive: map['isActive'] ?? true,
      isPreview: map['isPreview'] ?? false,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      createdByAdminId: map['createdByAdminId'] ?? '',
      downloadStatus: VideoDownloadStatus.values.firstWhere(
        (s) => s.name == map['downloadStatus'],
        orElse: () => VideoDownloadStatus.notDownloaded,
      ),
      localEncryptedPath: map['localEncryptedPath'],
      downloadedAt: map['downloadedAt'] != null
          ? _parseDateTime(map['downloadedAt'])
          : null,
      downloadHash: map['downloadHash'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'lessonId': lessonId,
      'unitId': unitId,
      'subjectId': subjectId,
      'sectionId': sectionId,
      'title': title,
      'description': description,
      'encryptedUrl360': encryptedUrl360,
      'encryptedUrl480': encryptedUrl480,
      'encryptedUrl720': encryptedUrl720,
      'encryptedUrl1080': encryptedUrl1080,
      'thumbnailUrl': thumbnailUrl,
      'durationInSeconds': durationInSeconds,
      'fileSizeInBytes': fileSizeInBytes,
      'order': order,
      'isActive': isActive,
      'isPreview': isPreview,
      'metadata': metadata,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'createdByAdminId': createdByAdminId,
      'downloadStatus': downloadStatus.name,
      'localEncryptedPath': localEncryptedPath,
      'downloadedAt': downloadedAt?.millisecondsSinceEpoch,
      'downloadHash': downloadHash,
    };
  }

  /// تحويل إلى Map للتخزين المحلي (JSON) - نفس toMap() لأن الفيديو لا يستخدم Timestamp
  Map<String, dynamic> toLocalMap() => toMap();

  /// إنشاء من Map للتخزين المحلي (JSON) - نفس fromMap() لأن الفيديو لا يستخدم Timestamp
  factory Video.fromLocalMap(Map<String, dynamic> map) => Video.fromMap(map);

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    if (value is String) return DateTime.tryParse(value) ?? DateTime.now();
    return DateTime.now();
  }

  Video copyWith({
    String? id,
    String? lessonId,
    String? unitId,
    String? subjectId,
    String? sectionId,
    String? title,
    String? description,
    String? encryptedUrl360,
    String? encryptedUrl480,
    String? encryptedUrl720,
    String? encryptedUrl1080,
    String? thumbnailUrl,
    int? durationInSeconds,
    int? fileSizeInBytes,
    int? order,
    bool? isActive,
    bool? isPreview,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByAdminId,
    VideoDownloadStatus? downloadStatus,
    String? localEncryptedPath,
    DateTime? downloadedAt,
    String? downloadHash,
  }) {
    return Video(
      id: id ?? this.id,
      lessonId: lessonId ?? this.lessonId,
      unitId: unitId ?? this.unitId,
      subjectId: subjectId ?? this.subjectId,
      sectionId: sectionId ?? this.sectionId,
      title: title ?? this.title,
      description: description ?? this.description,
      encryptedUrl360: encryptedUrl360 ?? this.encryptedUrl360,
      encryptedUrl480: encryptedUrl480 ?? this.encryptedUrl480,
      encryptedUrl720: encryptedUrl720 ?? this.encryptedUrl720,
      encryptedUrl1080: encryptedUrl1080 ?? this.encryptedUrl1080,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      durationInSeconds: durationInSeconds ?? this.durationInSeconds,
      fileSizeInBytes: fileSizeInBytes ?? this.fileSizeInBytes,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      isPreview: isPreview ?? this.isPreview,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
      downloadStatus: downloadStatus ?? this.downloadStatus,
      localEncryptedPath: localEncryptedPath ?? this.localEncryptedPath,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      downloadHash: downloadHash ?? this.downloadHash,
    );
  }

  // دوال Firestore (بدون بيانات التحميل المحلي)
  Map<String, dynamic> toFirestore() {
    return {
      'lessonId': lessonId,
      'unitId': unitId,
      'subjectId': subjectId,
      'sectionId': sectionId,
      'title': title,
      'description': description,
      'encryptedUrl360': encryptedUrl360,
      'encryptedUrl480': encryptedUrl480,
      'encryptedUrl720': encryptedUrl720,
      'encryptedUrl1080': encryptedUrl1080,
      'thumbnailUrl': thumbnailUrl,
      'durationInSeconds': durationInSeconds,
      'fileSizeInBytes': fileSizeInBytes,
      'order': order,
      'isActive': isActive,
      'isPreview': isPreview,
      'metadata': metadata,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'createdByAdminId': createdByAdminId,
    };
  }

  factory Video.fromFirestore(Map<String, dynamic> data, String documentId) {
    return Video(
      id: documentId,
      lessonId: data['lessonId'] ?? '',
      unitId: data['unitId'] ?? '',
      subjectId: data['subjectId'] ?? '',
      sectionId: data['sectionId'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      encryptedUrl360: data['encryptedUrl360'],
      encryptedUrl480: data['encryptedUrl480'],
      encryptedUrl720: data['encryptedUrl720'],
      encryptedUrl1080: data['encryptedUrl1080'],
      thumbnailUrl: data['thumbnailUrl'] ?? '',
      durationInSeconds: data['durationInSeconds'] ?? 0,
      fileSizeInBytes: data['fileSizeInBytes'] ?? 0,
      order: data['order'] ?? 0,
      isActive: data['isActive'] ?? true,
      isPreview: data['isPreview'] ?? false,
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
      createdByAdminId: data['createdByAdminId'] ?? '',
    );
  }

  /// تحويل مدة الفيديو إلى نص قابل للقراءة
  String get formattedDuration {
    final hours = durationInSeconds ~/ 3600;
    final minutes = (durationInSeconds % 3600) ~/ 60;
    final seconds = durationInSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// تحويل حجم الملف إلى نص قابل للقراءة
  String get formattedFileSize {
    if (fileSizeInBytes < 1024) {
      return '$fileSizeInBytes B';
    } else if (fileSizeInBytes < 1024 * 1024) {
      return '${(fileSizeInBytes / 1024).toStringAsFixed(1)} KB';
    } else if (fileSizeInBytes < 1024 * 1024 * 1024) {
      return '${(fileSizeInBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// هل الفيديو محمل محلياً
  bool get isDownloaded =>
      downloadStatus == VideoDownloadStatus.downloaded &&
      localEncryptedPath != null;

  /// هل الفيديو قيد التحميل
  bool get isDownloading => downloadStatus == VideoDownloadStatus.downloading;

  /// الحصول على الجودات المتاحة
  List<VideoQuality> get availableQualities {
    List<VideoQuality> qualities = [];
    if (encryptedUrl360 != null && encryptedUrl360!.isNotEmpty) {
      qualities.add(VideoQuality.quality360);
    }
    if (encryptedUrl480 != null && encryptedUrl480!.isNotEmpty) {
      qualities.add(VideoQuality.quality480);
    }
    if (encryptedUrl720 != null && encryptedUrl720!.isNotEmpty) {
      qualities.add(VideoQuality.quality720);
    }
    if (encryptedUrl1080 != null && encryptedUrl1080!.isNotEmpty) {
      qualities.add(VideoQuality.quality1080);
    }
    return qualities;
  }

  /// الحصول على أفضل جودة متاحة
  VideoQuality get bestAvailableQuality {
    final qualities = availableQualities;
    if (qualities.isEmpty) return VideoQuality.auto;

    if (qualities.contains(VideoQuality.quality1080)) {
      return VideoQuality.quality1080;
    }
    if (qualities.contains(VideoQuality.quality720)) {
      return VideoQuality.quality720;
    }
    if (qualities.contains(VideoQuality.quality480)) {
      return VideoQuality.quality480;
    }
    if (qualities.contains(VideoQuality.quality360)) {
      return VideoQuality.quality360;
    }

    return VideoQuality.auto;
  }

  /// الحصول على الرابط المشفر حسب الجودة
  String? getEncryptedUrlForQuality(VideoQuality quality) {
    switch (quality) {
      case VideoQuality.quality360:
        return encryptedUrl360;
      case VideoQuality.quality480:
        return encryptedUrl480;
      case VideoQuality.quality720:
        return encryptedUrl720;
      case VideoQuality.quality1080:
        return encryptedUrl1080;
      case VideoQuality.auto:
        // إرجاع أفضل جودة متاحة
        final bestQuality = bestAvailableQuality;
        return getEncryptedUrlForQuality(bestQuality);
    }
  }

  /// الحصول على نص الجودة للعرض
  String getQualityDisplayText(VideoQuality quality) {
    switch (quality) {
      case VideoQuality.quality360:
        return '360p';
      case VideoQuality.quality480:
        return '480p';
      case VideoQuality.quality720:
        return '720p';
      case VideoQuality.quality1080:
        return '1080p';
      case VideoQuality.auto:
        return 'تلقائي';
    }
  }

  /// هل يوجد فيديوهات متاحة
  bool get hasAnyVideo {
    return availableQualities.isNotEmpty;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Video && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Video(id: $id, title: $title, lessonId: $lessonId, isActive: $isActive, downloadStatus: $downloadStatus)';
  }
}
