name: smart_edu
description: "Smart Edu - Educational App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Firebase packages
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0
  cloud_firestore: ^5.6.9
  firebase_storage: ^12.4.7
  firebase_messaging: ^15.1.4

  # State management
  provider: ^6.1.2

  # UI packages
  flutter_screenutil: ^5.9.3
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1

  # Audio packages
  audioplayers: ^6.1.0

  # Video packages - الحل الصحيح لـ Windows
  # API الموحّدة لجميع المنصّات
  video_player: ^2.10.0      # أو آخر نسخة مستقرة
  # الطبقة الأصلية لويندوز (Media Foundation)
  video_player_win: ^3.2.0   # يَجلب الملف video_player_win.dll
  chewie: ^1.8.5             # للـ Android/iOS

  # Local storage and encryption
  sqflite: ^2.3.3+2
  path: ^1.9.0
  crypto: ^3.0.5
  encrypt: ^5.0.3

  # Device info
  device_info_plus: ^10.1.2

  # Utilities
  shared_preferences: ^2.3.2
  url_launcher: ^6.3.1
  # image_picker: ^1.1.2  # For admin app to add question images - DISABLED FOR WINDOWS BUILD
  permission_handler: ^11.3.1

  # Image processing
  image: ^4.1.7
  flutter_image_compress: ^2.1.0
  path_provider: ^2.1.4

  # Cloudinary for image hosting
  cloudinary_public: ^0.21.0

  # Networking
  http: ^1.2.2
  dio: ^5.7.0

  # Sharing
  share_plus: ^10.1.2

  # Background sync and notifications for Offline-First system
  flutter_local_notifications: ^17.2.3

  # Barcode generation and scanning
  barcode_widget: ^2.0.4
  qr_flutter: ^4.1.0
  mobile_scanner: ^5.2.3
  workmanager: ^0.8.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/image.png"
  min_sdk_android: 21
  adaptive_icon_background: "assets/images/image.png"
  adaptive_icon_foreground: "assets/images/image.png"
  web:
    generate: true
    image_path: "assets/images/image.png"
    background_color: "#667eea"
    theme_color: "#667eea"
  windows:
    generate: true
    image_path: "assets/images/image.png"
    icon_size: 48
