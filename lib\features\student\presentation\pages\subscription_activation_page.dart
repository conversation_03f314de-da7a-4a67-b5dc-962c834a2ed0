import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/subscription_service.dart';

import '../widgets/subject_selection_widget.dart';
import '../widgets/code_input_widget.dart';
import 'subscription_pricing_page.dart';
// تم إزالة استيراد device_info_widget.dart مع الحفاظ على الوظائف في الخلفية

class SubscriptionActivationPage extends StatefulWidget {
  const SubscriptionActivationPage({super.key});

  @override
  State<SubscriptionActivationPage> createState() =>
      _SubscriptionActivationPageState();
}

class _SubscriptionActivationPageState extends State<SubscriptionActivationPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _codeController = TextEditingController();
  bool _isActivating = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 3,
      vsync: this,
    ); // إضافة تبويبة أسعار الاشتراك

    // تهيئة خدمة الاشتراكات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeServices();
    });
  }

  /// تهيئة الخدمات وإعادة تحميل البيانات
  Future<void> _initializeServices() async {
    final subscriptionService = context.read<SubscriptionService>();

    // إعادة تحميل البيانات من المصادر المحلية والخادم
    await subscriptionService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          'تفعيل الاشتراك',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.getPrimaryGradient(context),
          ),
        ),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'تفعيل الكود'),
            Tab(text: 'المواد المتاحة'),
            Tab(text: 'أسعار الاشتراك ومراكز الدفع'),
          ],
        ),
      ),
      body: Consumer<SubscriptionService>(
        builder: (context, subscriptionService, child) {
          if (subscriptionService.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildCodeActivationTab(subscriptionService),
              _buildSubjectsTab(subscriptionService),
              _buildPricingTab(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCodeActivationTab(SubscriptionService subscriptionService) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة معلومات الاشتراك الحالي
          if (subscriptionService.currentSubscription != null)
            _buildCurrentSubscriptionCard(subscriptionService),

          SizedBox(height: 24.h),

          // بطاقة تفعيل الكود
          CodeInputWidget(
            controller: _codeController,
            isLoading: _isActivating,
            onActivate: () => _activateCode(subscriptionService),
          ),

          SizedBox(height: 24.h),

          // تعليمات الاستخدام
          _buildInstructionsCard(),
        ],
      ),
    );
  }

  Widget _buildCurrentSubscriptionCard(
    SubscriptionService subscriptionService,
  ) {
    final subscription = subscriptionService.currentSubscription!;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: AppTheme.secondaryGradient,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.verified_user, color: Colors.white, size: 24.sp),
                SizedBox(width: 8.w),
                Text(
                  'اشتراكك الحالي',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildSubscriptionStat(
              'المواد المفعلة من قسم الاختبارات',
              '${subscription.subscribedSubjectIds.length}',
              Icons.book,
            ),
            SizedBox(height: 8.h),
            _buildSubscriptionStat(
              'المواد المفعلة من قسم الفيديوهات',
              '${subscription.videoSubjectIds.length}',
              Icons.video_library,
            ),
            SizedBox(height: 8.h),
            _buildSubscriptionStat(
              'الأكواد المستخدمة',
              '${subscription.usedCodes.length}',
              Icons.vpn_key,
            ),
            SizedBox(height: 8.h),
            _buildSubscriptionStat(
              'تاريخ التسجيل',
              _formatDate(subscription.createdAt),
              Icons.calendar_today,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white70, size: 16.sp),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildSubjectsTab(SubscriptionService subscriptionService) {
    return SubjectSelectionWidget(
      allSubjects: subscriptionService.allSubjects,
      subscribedSubjects: subscriptionService.subscribedSubjects,
      subscriptionService: subscriptionService,
    );
  }

  // تم إزالة _buildDeviceInfoTab مع الحفاظ على جميع وظائف معلومات الجهاز في الخلفية
  // جميع وظائف Device ID وربط الأكواد بالأجهزة تعمل بشكل طبيعي

  Widget _buildInstructionsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 24.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  'تعليمات الاستخدام',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildInstructionItem(
              '1. احصل على كود تفعيل اشتراك من الإدارة (لمعرفة الأسعار اضغط على زر أسعار الاشتراك وستجد الاسعار و زر التواصل مع الإدارة عبر تلغرام )',
              Icons.person_outline,
            ),
            _buildInstructionItem(
              '2. أدخل الكود في الحقل أعلاه',
              Icons.edit_outlined,
            ),
            _buildInstructionItem(
              '3. اضغط على "تفعيل الاشتراك"',
              Icons.play_arrow_outlined,
            ),
            _buildInstructionItem(
              '4. ستصبح المواد متاحة فوراً',
              Icons.check_circle_outline,
            ),
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppTheme.warningColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppTheme.warningColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_outlined,
                    color: AppTheme.warningColor,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      'كل كود يمكن استخدامه مرة واحدة فقط على جهاز واحد',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.warningColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionItem(String text, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.textSecondaryColor, size: 16.sp),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(text, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Future<void> _activateCode(SubscriptionService subscriptionService) async {
    if (_codeController.text.trim().isEmpty) {
      _showMessage('يرجى إدخال الكود', isError: true);
      return;
    }

    setState(() {
      _isActivating = true;
    });

    try {
      final result = await subscriptionService.useSubscriptionCode(
        _codeController.text.trim(),
      );

      if (result) {
        _codeController.clear();
        _showMessage('تم تفعيل الاشتراك بنجاح', isError: false);
        // الانتقال إلى تبويب المواد لعرض النتيجة
        _tabController.animateTo(1);
      } else {
        _showMessage('فشل في تفعيل الكود', isError: true);
      }
    } finally {
      setState(() {
        _isActivating = false;
      });
    }
  }

  void _showMessage(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppTheme.errorColor : AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
        duration: Duration(seconds: isError ? 4 : 3),
      ),
    );
  }

  /// بناء تبويبة أسعار الاشتراك
  Widget _buildPricingTab() {
    return const SubscriptionPricingPage();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
