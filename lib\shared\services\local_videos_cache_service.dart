import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/video_model.dart';
import 'subject_videos_service.dart';

/// خدمة التخزين المحلي للفيديوهات
/// تدير تخزين فيديوهات المواد محلياً وتتبع التحديثات
class LocalVideosCacheService {
  static final LocalVideosCacheService _instance =
      LocalVideosCacheService._internal();
  static LocalVideosCacheService get instance => _instance;
  LocalVideosCacheService._internal();

  static const String _videosPrefix = 'subject_videos_';
  static const String _lastUpdatePrefix = 'videos_last_update_';
  static const String _subscribedVideoSubjectsKey = 'subscribed_video_subjects';

  /// حفظ فيديوهات مادة محلياً
  Future<void> saveSubjectVideos(String subjectId, List<Video> videos) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحويل الفيديوهات إلى JSON للتخزين المحلي
      final videosJson = videos.map((v) => v.toLocalMap()).toList();
      final jsonString = jsonEncode(videosJson);

      // حفظ الفيديوهات
      await prefs.setString('$_videosPrefix$subjectId', jsonString);

      // حفظ وقت التحديث
      await prefs.setString(
        '$_lastUpdatePrefix$subjectId',
        DateTime.now().toIso8601String(),
      );

      debugPrint('✅ تم حفظ ${videos.length} فيديو للمادة $subjectId محلياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ فيديوهات المادة محلياً: $e');
    }
  }

  /// تحميل فيديوهات مادة من التخزين المحلي
  Future<List<Video>> getSubjectVideos(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('$_videosPrefix$subjectId');

      if (jsonString == null) {
        debugPrint('📭 لا توجد فيديوهات محفوظة محلياً للمادة $subjectId');
        return [];
      }

      final videosJson = jsonDecode(jsonString) as List;
      final videos = videosJson
          .map((json) => Video.fromLocalMap(json as Map<String, dynamic>))
          .toList();

      debugPrint(
        '📚 تم تحميل ${videos.length} فيديو للمادة $subjectId من التخزين المحلي',
      );
      return videos;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل فيديوهات المادة من التخزين المحلي: $e');
      return [];
    }
  }

  /// الحصول على آخر وقت تحديث لمادة
  Future<DateTime?> getLastUpdateTime(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString('$_lastUpdatePrefix$subjectId');

      if (timeString == null) return null;

      return DateTime.parse(timeString);
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على وقت التحديث: $e');
      return null;
    }
  }

  /// التحقق من وجود فيديوهات محفوظة للمادة
  Future<bool> hasSubjectVideos(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey('$_videosPrefix$subjectId');
  }

  /// حذف فيديوهات مادة من التخزين المحلي
  Future<void> deleteSubjectVideos(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_videosPrefix$subjectId');
      await prefs.remove('$_lastUpdatePrefix$subjectId');

      debugPrint('🗑️ تم حذف فيديوهات المادة $subjectId من التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في حذف فيديوهات المادة: $e');
    }
  }

  /// إضافة مادة للمواد المشترك بها (فيديوهات)
  Future<void> addSubscribedVideoSubject(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final subscribedSubjects = await getSubscribedVideoSubjects();

      if (!subscribedSubjects.contains(subjectId)) {
        subscribedSubjects.add(subjectId);
        await prefs.setStringList(
          _subscribedVideoSubjectsKey,
          subscribedSubjects,
        );
        debugPrint(
          '✅ تم إضافة المادة $subjectId للمواد المشترك بها (فيديوهات)',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في إضافة المادة للاشتراك (فيديوهات): $e');
    }
  }

  /// إزالة مادة من المواد المشترك بها (فيديوهات)
  Future<void> removeSubscribedVideoSubject(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final subscribedSubjects = await getSubscribedVideoSubjects();

      subscribedSubjects.remove(subjectId);
      await prefs.setStringList(
        _subscribedVideoSubjectsKey,
        subscribedSubjects,
      );

      // حذف فيديوهات المادة أيضاً
      await deleteSubjectVideos(subjectId);

      debugPrint(
        '✅ تم إزالة المادة $subjectId من المواد المشترك بها (فيديوهات)',
      );
    } catch (e) {
      debugPrint('❌ خطأ في إزالة المادة من الاشتراك (فيديوهات): $e');
    }
  }

  /// الحصول على قائمة المواد المشترك بها (فيديوهات)
  Future<List<String>> getSubscribedVideoSubjects() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من وجود بيانات قديمة بصيغة خاطئة
      final rawData = prefs.get(_subscribedVideoSubjectsKey);
      if (rawData is String) {
        // إذا كانت البيانات محفوظة كـ String بدلاً من List، نظفها
        debugPrint('🧹 تنظيف بيانات المواد المشترك بها (فيديوهات) القديمة');
        await prefs.remove(_subscribedVideoSubjectsKey);
        return [];
      }

      return prefs.getStringList(_subscribedVideoSubjectsKey) ?? [];
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المواد المشترك بها (فيديوهات): $e');
      // في حالة الخطأ، نظف البيانات
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_subscribedVideoSubjectsKey);
      } catch (_) {}
      return [];
    }
  }

  /// التحقق من الاشتراك في مادة (فيديوهات)
  Future<bool> isSubscribedToVideoSubject(String subjectId) async {
    final subscribedSubjects = await getSubscribedVideoSubjects();
    return subscribedSubjects.contains(subjectId);
  }

  /// فلترة الفيديوهات محلياً حسب الوحدة
  List<Video> filterVideosByUnit(List<Video> videos, String unitId) {
    return videos.where((v) => v.unitId == unitId).toList();
  }

  /// فلترة الفيديوهات محلياً حسب الدرس
  List<Video> filterVideosByLesson(List<Video> videos, String lessonId) {
    return videos.where((v) => v.lessonId == lessonId).toList();
  }

  /// البحث في الفيديوهات محلياً
  List<Video> searchVideos(List<Video> videos, String query) {
    if (query.trim().isEmpty) return videos;

    final searchQuery = query.toLowerCase().trim();
    return videos.where((video) {
      // البحث في عنوان الفيديو
      final titleMatch = video.title.toLowerCase().contains(searchQuery);

      // البحث في الوصف
      final descriptionMatch = video.description.toLowerCase().contains(
        searchQuery,
      );

      return titleMatch || descriptionMatch;
    }).toList();
  }

  /// مسح جميع البيانات المحلية للفيديوهات
  Future<void> clearAllVideoData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      // حذف جميع مفاتيح الفيديوهات والتحديثات
      for (final key in keys) {
        if (key.startsWith(_videosPrefix) ||
            key.startsWith(_lastUpdatePrefix) ||
            key == _subscribedVideoSubjectsKey) {
          await prefs.remove(key);
        }
      }

      debugPrint('🧹 تم مسح جميع بيانات الفيديوهات المحلية');
    } catch (e) {
      debugPrint('❌ خطأ في مسح البيانات المحلية للفيديوهات: $e');
    }
  }

  /// الحصول على حجم البيانات المحفوظة محلياً للفيديوهات
  Future<Map<String, int>> getVideosCacheSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      int totalVideos = 0;
      int totalSubjects = 0;

      for (final key in keys) {
        if (key.startsWith(_videosPrefix)) {
          totalSubjects++;
          final jsonString = prefs.getString(key);
          if (jsonString != null) {
            final videosJson = jsonDecode(jsonString) as List;
            totalVideos += videosJson.length;
          }
        }
      }

      return {'subjects': totalSubjects, 'videos': totalVideos};
    } catch (e) {
      debugPrint('❌ خطأ في حساب حجم التخزين المؤقت للفيديوهات: $e');
      return {'subjects': 0, 'videos': 0};
    }
  }
}

/// خدمة التحديث الذكي للفيديوهات
class SmartVideoUpdateService {
  static final SmartVideoUpdateService _instance =
      SmartVideoUpdateService._internal();
  static SmartVideoUpdateService get instance => _instance;
  SmartVideoUpdateService._internal();

  /// تحديث فيديوهات مادة واحدة
  Future<bool> updateSubjectVideos(String subjectId, String subjectName) async {
    try {
      debugPrint('🔄 بدء تحديث فيديوهات المادة: $subjectName');

      // التحقق من آخر تحديث محلي
      final lastUpdateTime = await LocalVideosCacheService.instance
          .getLastUpdateTime(subjectId);

      // التحقق من وجود تحديثات
      final hasUpdates = await SubjectVideosService.instance.hasUpdates(
        subjectId,
        lastUpdateTime ?? DateTime(2000), // إذا لم يكن هناك تحديث سابق
      );

      if (!hasUpdates) {
        debugPrint('✅ لديك آخر تحديث لفيديوهات مادة $subjectName');
        return false; // لا توجد تحديثات
      }

      // تحميل الفيديوهات الجديدة من Firebase
      final newVideos = await SubjectVideosService.instance.getAllSubjectVideos(
        subjectId,
      );

      if (newVideos.isEmpty) {
        debugPrint('📭 لا توجد فيديوهات للمادة $subjectName');
        return false;
      }

      // حفظ الفيديوهات محلياً
      await LocalVideosCacheService.instance.saveSubjectVideos(
        subjectId,
        newVideos,
      );

      // إضافة المادة للمواد المشترك بها
      await LocalVideosCacheService.instance.addSubscribedVideoSubject(
        subjectId,
      );

      debugPrint('✅ تم تحديث ${newVideos.length} فيديو لمادة $subjectName');
      return true; // تم التحديث بنجاح
    } catch (e) {
      debugPrint('❌ خطأ في تحديث فيديوهات المادة $subjectName: $e');
      return false;
    }
  }

  /// تحديث جميع المواد المشترك بها (فيديوهات)
  Future<Map<String, bool>> updateAllSubscribedVideoSubjects() async {
    try {
      debugPrint('🔄 بدء تحديث جميع المواد المشترك بها (فيديوهات)');

      final subscribedSubjects = await LocalVideosCacheService.instance
          .getSubscribedVideoSubjects();
      final results = <String, bool>{};

      for (final subjectId in subscribedSubjects) {
        final success = await updateSubjectVideos(
          subjectId,
          'المادة $subjectId',
        );
        results[subjectId] = success;
      }

      final updatedCount = results.values.where((success) => success).length;
      debugPrint(
        '✅ تم تحديث $updatedCount من ${subscribedSubjects.length} مادة (فيديوهات)',
      );

      return results;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المواد المشترك بها (فيديوهات): $e');
      return {};
    }
  }

  /// تحميل فيديوهات مادة لأول مرة
  Future<bool> downloadSubjectVideos(
    String subjectId,
    String subjectName,
  ) async {
    try {
      debugPrint('📥 تحميل فيديوهات المادة لأول مرة: $subjectName');

      // تحميل الفيديوهات من Firebase
      final videos = await SubjectVideosService.instance.getAllSubjectVideos(
        subjectId,
      );

      if (videos.isEmpty) {
        debugPrint('📭 لا توجد فيديوهات للمادة $subjectName');
        return false;
      }

      // حفظ الفيديوهات محلياً
      await LocalVideosCacheService.instance.saveSubjectVideos(
        subjectId,
        videos,
      );

      // إضافة المادة للمواد المشترك بها
      await LocalVideosCacheService.instance.addSubscribedVideoSubject(
        subjectId,
      );

      debugPrint('✅ تم تحميل ${videos.length} فيديو لمادة $subjectName');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل فيديوهات المادة $subjectName: $e');
      return false;
    }
  }
}
