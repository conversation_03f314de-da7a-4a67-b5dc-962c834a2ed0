import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';
import '../../core/models/section.dart';
import '../models/subject_model.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import '../models/question_model.dart';
import 'single_read_data_service.dart';

/// خدمة فرز وتوزيع البيانات من التخزين المحلي
/// تأخذ البيانات من الوثائق المحفوظة محلياً وتوزعها على الصفحات المناسبة
class DataDistributionService {
  static final DataDistributionService _instance =
      DataDistributionService._internal();
  static DataDistributionService get instance => _instance;
  DataDistributionService._internal();

  // مفاتيح التخزين المحلي
  static const String _generalDataKey = 'general_data';
  static const String _subjectPrefix = 'subject_';
  static const String _freeSubjectPrefix = 'free_subject_';

  // كاش البيانات المفروزة
  List<Section>? _cachedSections;
  List<Subject>? _cachedSubjects;
  List<VideoSection>? _cachedVideoSections;
  List<VideoSubject>? _cachedVideoSubjects;

  Map<String, List<Unit>> _cachedUnits = {};
  Map<String, List<Lesson>> _cachedLessons = {};
  Map<String, List<Question>> _cachedQuestions = {};
  Map<String, List<VideoUnit>> _cachedVideoUnits = {};
  Map<String, List<VideoLesson>> _cachedVideoLessons = {};
  Map<String, List<Video>> _cachedVideos = {};

  // ═══════════════════════════════════════════════════════════════
  // 🔄 تحديث الكاش من التخزين المحلي
  // ═══════════════════════════════════════════════════════════════

  /// تحديث جميع البيانات من التخزين المحلي
  Future<void> refreshAllData() async {
    try {
      debugPrint(
        '🔄 [STUDENT MONITOR] تحديث جميع البيانات من التخزين المحلي...',
      );
      debugPrint('🕐 [STUDENT MONITOR] الوقت: ${DateTime.now()}');

      // استخدام البيانات من SingleReadDataService مباشرة
      final singleReadService = SingleReadDataService.instance;

      debugPrint('🔍 [DEBUG] البيانات في SingleReadDataService:');
      debugPrint('   - الأقسام: ${singleReadService.sections.length}');
      debugPrint('   - المواد: ${singleReadService.subjects.length}');
      debugPrint(
        '   - أقسام الفيديو: ${singleReadService.videoSections.length}',
      );
      debugPrint(
        '   - مواد الفيديو: ${singleReadService.videoSubjects.length}',
      );
      debugPrint('   - الوحدات: ${singleReadService.units.length}');
      debugPrint('   - الدروس: ${singleReadService.lessons.length}');
      debugPrint('   - الأسئلة: ${singleReadService.questions.length}');

      // نسخ البيانات العامة
      _cachedSections = List.from(singleReadService.sections);
      _cachedSubjects = List.from(singleReadService.subjects);
      _cachedVideoSections = List.from(singleReadService.videoSections);
      _cachedVideoSubjects = List.from(singleReadService.videoSubjects);

      // فرز البيانات المفصلة حسب المادة
      _cachedUnits.clear();
      _cachedLessons.clear();
      _cachedQuestions.clear();
      _cachedVideoUnits.clear();
      _cachedVideoLessons.clear();
      _cachedVideos.clear();

      // فرز الوحدات حسب المادة (مع فحص التكرار)
      debugPrint('🔍 [DEBUG] فرز ${singleReadService.units.length} وحدة...');
      debugPrint('🔍 [DEBUG] جميع الوحدات في SingleReadDataService:');
      for (int i = 0; i < singleReadService.units.length; i++) {
        final unit = singleReadService.units[i];
        debugPrint(
          '   [$i] ${unit.name} (subjectId: ${unit.subjectId}, id: ${unit.id})',
        );
      }

      for (final unit in singleReadService.units) {
        debugPrint('   - وحدة: ${unit.name} (subjectId: ${unit.subjectId})');

        if (!_cachedUnits.containsKey(unit.subjectId)) {
          _cachedUnits[unit.subjectId] = [];
          debugPrint('     🆕 إنشاء مفتاح جديد: ${unit.subjectId}');
        }

        // فحص التكرار بناءً على ID
        final existingUnits = _cachedUnits[unit.subjectId]!;
        final isDuplicate = existingUnits.any((u) => u.id == unit.id);

        if (!isDuplicate) {
          _cachedUnits[unit.subjectId]!.add(unit);
          debugPrint('     ✅ تم إضافة الوحدة للمفتاح: ${unit.subjectId}');
        } else {
          debugPrint('     ⚠️ وحدة مكررة تم تجاهلها');
        }
      }

      debugPrint('📊 [DEBUG] نتائج فرز الوحدات:');
      debugPrint('   - إجمالي المفاتيح: ${_cachedUnits.keys.length}');
      for (final key in _cachedUnits.keys) {
        debugPrint('   - المفتاح "$key": ${_cachedUnits[key]!.length} وحدة');
      }

      // فرز الدروس حسب المادة (مع فحص التكرار)
      debugPrint('🔍 [DEBUG] فرز ${singleReadService.lessons.length} درس...');
      for (final lesson in singleReadService.lessons) {
        debugPrint('   - درس: ${lesson.name} (subjectId: ${lesson.subjectId})');

        if (!_cachedLessons.containsKey(lesson.subjectId)) {
          _cachedLessons[lesson.subjectId] = [];
        }

        // فحص التكرار بناءً على ID
        final existingLessons = _cachedLessons[lesson.subjectId]!;
        final isDuplicate = existingLessons.any((l) => l.id == lesson.id);

        if (!isDuplicate) {
          _cachedLessons[lesson.subjectId]!.add(lesson);
          debugPrint('     ✅ تم إضافة الدرس للمفتاح: ${lesson.subjectId}');
        } else {
          debugPrint('     ⚠️ درس مكرر تم تجاهله');
        }
      }

      debugPrint('📊 [DEBUG] نتائج فرز الدروس:');
      debugPrint('   - إجمالي المفاتيح: ${_cachedLessons.keys.length}');
      for (final key in _cachedLessons.keys) {
        debugPrint('   - المفتاح "$key": ${_cachedLessons[key]!.length} درس');
      }

      // فرز الأسئلة حسب المادة (مع فحص التكرار)
      for (final question in singleReadService.questions) {
        if (!_cachedQuestions.containsKey(question.subjectId)) {
          _cachedQuestions[question.subjectId] = [];
        }

        // فحص التكرار بناءً على ID
        final existingQuestions = _cachedQuestions[question.subjectId]!;
        final isDuplicate = existingQuestions.any((q) => q.id == question.id);

        if (!isDuplicate) {
          _cachedQuestions[question.subjectId]!.add(question);
        }
      }

      // فرز وحدات الفيديو حسب المادة (مع فحص التكرار)
      for (final videoUnit in singleReadService.videoUnits) {
        if (!_cachedVideoUnits.containsKey(videoUnit.subjectId)) {
          _cachedVideoUnits[videoUnit.subjectId] = [];
        }

        // فحص التكرار بناءً على ID
        final existingUnits = _cachedVideoUnits[videoUnit.subjectId]!;
        final isDuplicate = existingUnits.any(
          (unit) => unit.id == videoUnit.id,
        );

        if (!isDuplicate) {
          _cachedVideoUnits[videoUnit.subjectId]!.add(videoUnit);
        }
      }

      // تصحيح خاصية isFree لمواد الفيديو بناءً على القسم
      _correctVideoSubjectsFreeStatus();

      // فرز دروس الفيديو حسب المادة (مع فحص التكرار)
      for (final videoLesson in singleReadService.videoLessons) {
        if (!_cachedVideoLessons.containsKey(videoLesson.subjectId)) {
          _cachedVideoLessons[videoLesson.subjectId] = [];
        }

        // فحص التكرار بناءً على ID
        final existingLessons = _cachedVideoLessons[videoLesson.subjectId]!;
        final isDuplicate = existingLessons.any(
          (lesson) => lesson.id == videoLesson.id,
        );

        if (!isDuplicate) {
          _cachedVideoLessons[videoLesson.subjectId]!.add(videoLesson);
        }
      }

      // فرز الفيديوهات حسب المادة (مع فحص التكرار)
      for (final video in singleReadService.videos) {
        if (!_cachedVideos.containsKey(video.subjectId)) {
          _cachedVideos[video.subjectId] = [];
        }

        // فحص التكرار بناءً على ID
        final existingVideos = _cachedVideos[video.subjectId]!;
        final isDuplicate = existingVideos.any((v) => v.id == video.id);

        if (!isDuplicate) {
          _cachedVideos[video.subjectId]!.add(video);
        }
      }

      // 🔍 طباعة إحصائيات مفصلة
      printDataStatistics();

      debugPrint('✅ [STUDENT MONITOR] تم تحديث جميع البيانات بنجاح');
      debugPrint('🕐 [STUDENT MONITOR] انتهى في: ${DateTime.now()}');
    } catch (e) {
      debugPrint('❌ [STUDENT MONITOR] خطأ في تحديث البيانات: $e');
      debugPrint('❌ [STUDENT MONITOR] تفاصيل الخطأ: ${e.toString()}');
      debugPrint('❌ [STUDENT MONITOR] استخدام الطريقة القديمة كـ fallback...');
      // fallback للطريقة القديمة
      await _loadGeneralData();
      await _loadSubjectData();
    }
  }

  /// تحميل البيانات العامة
  Future<void> _loadGeneralData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(_generalDataKey);

      debugPrint('🔍 [DEBUG] _loadGeneralData():');
      debugPrint('   - مفتاح البيانات: $_generalDataKey');
      debugPrint('   - هل توجد بيانات محفوظة: ${dataString != null}');
      if (dataString != null) {
        debugPrint('   - حجم البيانات: ${dataString.length} حرف');
      }

      if (dataString != null) {
        final data = jsonDecode(dataString);
        final generalData = data['data'];

        debugPrint('🔍 [DEBUG] محتوى البيانات المحفوظة:');
        debugPrint('   - البيانات الخارجية: ${data.keys.toList()}');
        debugPrint(
          '   - البيانات الداخلية: ${generalData?.keys?.toList() ?? "null"}',
        );

        // فرز الأقسام والمواد
        _cachedSections =
            (generalData['test_sections'] as List?)
                ?.map(
                  (item) =>
                      Section.fromLocalMap(Map<String, dynamic>.from(item)),
                )
                .toList() ??
            [];

        _cachedSubjects =
            (generalData['test_subjects'] as List?)
                ?.map(
                  (item) => Subject.fromMap(Map<String, dynamic>.from(item)),
                )
                .toList() ??
            [];

        _cachedVideoSections =
            (generalData['video_sections'] as List?)
                ?.map(
                  (item) =>
                      VideoSection.fromMap(Map<String, dynamic>.from(item)),
                )
                .toList() ??
            [];

        // تحميل مواد الفيديو مع تصحيح خاصية isFree
        final videoSubjectsData =
            (generalData['video_subjects'] as List?) ?? [];
        _cachedVideoSubjects = videoSubjectsData.map((item) {
          final subjectData = Map<String, dynamic>.from(item);
          final sectionId = subjectData['sectionId'] as String? ?? '';

          // البحث عن القسم المرتبط لتحديد isFree
          final relatedSection = (_cachedVideoSections ?? []).firstWhere(
            (section) => section.id == sectionId,
            orElse: () => VideoSection(
              id: '',
              name: '',
              description: '',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              createdByAdminId: '',
              isFree: false,
            ),
          );

          // تصحيح خاصية isFree بناءً على القسم
          final originalIsFree = subjectData['isFree'] ?? false;
          if (originalIsFree != relatedSection.isFree) {
            debugPrint(
              '🔧 تصحيح مادة فيديو: ${subjectData['name']} - isFree: $originalIsFree → ${relatedSection.isFree}',
            );
            subjectData['isFree'] = relatedSection.isFree;
          }

          return VideoSubject.fromMap(subjectData);
        }).toList();

        debugPrint('✅ تم تحميل البيانات العامة:');
        debugPrint('   - أقسام الاختبارات: ${_cachedSections?.length ?? 0}');
        debugPrint('   - مواد الاختبارات: ${_cachedSubjects?.length ?? 0}');
        debugPrint(
          '   - أقسام الفيديوهات: ${_cachedVideoSections?.length ?? 0}',
        );
        debugPrint(
          '   - مواد الفيديوهات: ${_cachedVideoSubjects?.length ?? 0}',
        );

        // 🔍 تشخيص مفصل للأقسام
        debugPrint('🔍 [DEBUG] تفاصيل الأقسام المحملة:');
        _cachedSections?.forEach((section) {
          debugPrint('   - ${section.name}: مجاني=${section.isFree}');
        });

        debugPrint('🔍 [DEBUG] تفاصيل أقسام الفيديو المحملة:');
        _cachedVideoSections?.forEach((section) {
          debugPrint('   - ${section.name}: مجاني=${section.isFree}');
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات العامة: $e');
    }
  }

  /// تحميل بيانات المواد
  Future<void> _loadSubjectData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      debugPrint('🔍 [DEBUG] _loadSubjectData():');
      debugPrint('   - إجمالي المفاتيح: ${keys.length}');

      final subjectKeys = keys
          .where(
            (key) =>
                key.startsWith(_subjectPrefix) ||
                key.startsWith(_freeSubjectPrefix),
          )
          .toList();

      debugPrint('   - مفاتيح المواد: ${subjectKeys.length}');
      for (final key in subjectKeys) {
        debugPrint('     - $key');
      }

      // تحميل بيانات المواد المدفوعة والمجانية
      for (final key in subjectKeys) {
        await _loadSingleSubjectData(key);
      }

      debugPrint('📊 [DEBUG] نتائج التحميل:');
      debugPrint('   - الوحدات: ${_cachedUnits.length} مادة');
      debugPrint('   - الدروس: ${_cachedLessons.length} مادة');
      debugPrint('   - الأسئلة: ${_cachedQuestions.length} مادة');
      debugPrint('   - وحدات الفيديو: ${_cachedVideoUnits.length} مادة');
      debugPrint('   - دروس الفيديو: ${_cachedVideoLessons.length} مادة');
      debugPrint('   - الفيديوهات: ${_cachedVideos.length} مادة');

      debugPrint('✅ تم تحميل بيانات ${_cachedUnits.length} مادة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات المواد: $e');
    }
  }

  /// تحميل بيانات مادة واحدة
  Future<void> _loadSingleSubjectData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(key);

      debugPrint('🔍 [DEBUG] _loadSingleSubjectData($key):');
      debugPrint('   - هل توجد بيانات: ${dataString != null}');

      if (dataString != null) {
        debugPrint('   - حجم البيانات: ${dataString.length} حرف');

        final data = jsonDecode(dataString);
        final subjectData = data['data'];
        final subjectInfo = data['subject_info'];
        final subjectId = subjectInfo['id'];
        final subjectName = subjectInfo['name'];

        debugPrint('   - معرف المادة: $subjectId');
        debugPrint('   - اسم المادة: $subjectName');
        debugPrint(
          '   - محتوى البيانات: ${subjectData?.keys?.toList() ?? "null"}',
        );

        // تحويل int إلى Timestamp عند القراءة
        final cleanSubjectData = _convertIntToTimestamp(subjectData);

        // فرز محتوى المادة
        if (cleanSubjectData['units'] != null) {
          final units = (cleanSubjectData['units'] as List)
              .map((item) => Unit.fromMap(Map<String, dynamic>.from(item)))
              .toList();
          _cachedUnits[subjectId] = units;
          debugPrint('   - تم تحميل ${units.length} وحدة');
        }

        if (cleanSubjectData['lessons'] != null) {
          final lessons = (cleanSubjectData['lessons'] as List)
              .map((item) => Lesson.fromMap(Map<String, dynamic>.from(item)))
              .toList();
          _cachedLessons[subjectId] = lessons;
          debugPrint('   - تم تحميل ${lessons.length} درس');
        }

        if (cleanSubjectData['questions'] != null) {
          final questions = (cleanSubjectData['questions'] as List)
              .map((item) => Question.fromMap(Map<String, dynamic>.from(item)))
              .toList();
          _cachedQuestions[subjectId] = questions;
          debugPrint('   - تم تحميل ${questions.length} سؤال');
        }

        if (cleanSubjectData['video_units'] != null) {
          final videoUnits = (cleanSubjectData['video_units'] as List)
              .map((item) => VideoUnit.fromMap(Map<String, dynamic>.from(item)))
              .toList();
          _cachedVideoUnits[subjectId] = videoUnits;
          debugPrint('   - تم تحميل ${videoUnits.length} وحدة فيديو');
        }

        if (cleanSubjectData['video_lessons'] != null) {
          final videoLessons = (cleanSubjectData['video_lessons'] as List)
              .map(
                (item) => VideoLesson.fromMap(Map<String, dynamic>.from(item)),
              )
              .toList();
          _cachedVideoLessons[subjectId] = videoLessons;
          debugPrint('   - تم تحميل ${videoLessons.length} درس فيديو');
        }

        if (cleanSubjectData['videos'] != null) {
          final videos = (cleanSubjectData['videos'] as List)
              .map((item) => Video.fromMap(Map<String, dynamic>.from(item)))
              .toList();
          _cachedVideos[subjectId] = videos;
          debugPrint('   - تم تحميل ${videos.length} فيديو');
        }

        debugPrint('✅ تم تحميل بيانات المادة: $subjectName ($subjectId)');
      } else {
        debugPrint('⚠️ لا توجد بيانات للمفتاح: $key');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات المادة $key: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // 📊 واجهات الحصول على البيانات
  // ═══════════════════════════════════════════════════════════════

  /// الحصول على جميع الأقسام
  List<Section> getAllSections() {
    return _cachedSections ?? [];
  }

  /// الحصول على الأقسام المجانية
  List<Section> getFreeSections() {
    return getAllSections().where((section) => section.isFree).toList();
  }

  /// الحصول على الأقسام المدفوعة
  List<Section> getPaidSections() {
    final allSections = getAllSections();
    final paidSections = allSections
        .where((section) => !section.isFree)
        .toList();

    // 🔍 تشخيص مفصل
    debugPrint('🔍 [DEBUG] getPaidSections():');
    debugPrint('   - إجمالي الأقسام: ${allSections.length}');
    debugPrint('   - الأقسام المدفوعة: ${paidSections.length}');
    for (var section in allSections) {
      debugPrint(
        '   - ${section.name}: مجاني=${section.isFree} ${section.isFree ? "(تم تجاهله)" : "(مدفوع)"}',
      );
    }

    return paidSections;
  }

  /// الحصول على جميع المواد
  List<Subject> getAllSubjects() {
    return _cachedSubjects ?? [];
  }

  /// الحصول على مواد قسم معين
  List<Subject> getSubjectsBySection(String sectionId) {
    return getAllSubjects()
        .where((subject) => subject.sectionId == sectionId)
        .toList();
  }

  /// الحصول على المواد المجانية لقسم معين
  List<Subject> getFreeSubjectsBySection(String sectionId) {
    return getSubjectsBySection(
      sectionId,
    ).where((subject) => subject.isFree).toList();
  }

  /// الحصول على المواد المدفوعة لقسم معين
  List<Subject> getPaidSubjectsBySection(String sectionId) {
    return getSubjectsBySection(
      sectionId,
    ).where((subject) => !subject.isFree).toList();
  }

  /// الحصول على جميع أقسام الفيديو
  List<VideoSection> getAllVideoSections() {
    return _cachedVideoSections ?? [];
  }

  /// الحصول على أقسام الفيديو المجانية
  List<VideoSection> getFreeVideoSections() {
    return getAllVideoSections().where((section) => section.isFree).toList();
  }

  /// الحصول على أقسام الفيديو المدفوعة
  List<VideoSection> getPaidVideoSections() {
    final allSections = getAllVideoSections();
    final paidSections = allSections
        .where((section) => !section.isFree)
        .toList();

    // 🔍 تشخيص مفصل
    debugPrint('🔍 [DEBUG] getPaidVideoSections():');
    debugPrint('   - إجمالي أقسام الفيديو: ${allSections.length}');
    debugPrint('   - أقسام الفيديو المدفوعة: ${paidSections.length}');
    for (var section in allSections) {
      debugPrint(
        '   - ${section.name}: مجاني=${section.isFree} ${section.isFree ? "(تم تجاهله)" : "(مدفوع)"}',
      );
    }

    return paidSections;
  }

  /// الحصول على جميع مواد الفيديو
  List<VideoSubject> getAllVideoSubjects() {
    return _cachedVideoSubjects ?? [];
  }

  /// الحصول على مواد الفيديو لقسم معين
  List<VideoSubject> getVideoSubjectsBySection(String sectionId) {
    final allVideoSubjects = getAllVideoSubjects();
    final sectionSubjects = allVideoSubjects
        .where((subject) => subject.sectionId == sectionId)
        .toList();

    // 🔍 تشخيص مفصل
    debugPrint('🔍 [DEBUG] getVideoSubjectsBySection($sectionId):');
    debugPrint('   - إجمالي مواد الفيديو: ${allVideoSubjects.length}');
    debugPrint('   - مواد الفيديو للقسم: ${sectionSubjects.length}');

    return sectionSubjects;
  }

  /// الحصول على مواد الفيديو المجانية لقسم معين
  List<VideoSubject> getFreeVideoSubjectsBySection(String sectionId) {
    final allSubjects = getVideoSubjectsBySection(sectionId);

    // البحث عن القسم المرتبط لتحديد إذا كان مجاني
    final relatedSection = getAllVideoSections().firstWhere(
      (section) => section.id == sectionId,
      orElse: () => VideoSection(
        id: '',
        name: '',
        description: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdByAdminId: '',
        isFree: false,
      ),
    );

    // إذا كان القسم مجاني، فجميع المواد فيه مجانية
    final freeSubjects = relatedSection.isFree
        ? allSubjects // جميع المواد في القسم المجاني
        : allSubjects
              .where((subject) => subject.isFree)
              .toList(); // فقط المواد المجانية

    // 🔍 تشخيص مفصل لمواد الفيديو المجانية
    debugPrint('🔍 [DEBUG] getFreeVideoSubjectsBySection($sectionId):');
    debugPrint('   - إجمالي مواد الفيديو للقسم: ${allSubjects.length}');
    debugPrint(
      '   - القسم المرتبط: ${relatedSection.name} (مجاني: ${relatedSection.isFree})',
    );
    debugPrint('   - مواد الفيديو المجانية: ${freeSubjects.length}');

    for (var subject in allSubjects) {
      final isActuallyFree = relatedSection.isFree || subject.isFree;
      debugPrint(
        '   - ${subject.name}: مجاني=${isActuallyFree} (subject.isFree=${subject.isFree}, section.isFree=${relatedSection.isFree}) (ID: ${subject.id})',
      );
    }

    return freeSubjects;
  }

  /// الحصول على مواد الفيديو المدفوعة لقسم معين
  List<VideoSubject> getPaidVideoSubjectsBySection(String sectionId) {
    return getVideoSubjectsBySection(
      sectionId,
    ).where((subject) => !subject.isFree).toList();
  }

  /// الحصول على وحدات مادة معينة
  List<Unit> getUnitsBySubject(String subjectId) {
    // البحث في المفاتيح المباشرة أولاً
    var units = _cachedUnits[subjectId] ?? [];

    // إذا لم توجد، ابحث في جميع الوحدات بناءً على subjectId
    if (units.isEmpty) {
      units = _cachedUnits.values
          .expand((unitList) => unitList)
          .where((unit) => unit.subjectId == subjectId)
          .toList();
    }

    debugPrint('🔍 [DEBUG] getUnitsBySubject($subjectId):');
    debugPrint('   - المفاتيح المتاحة: ${_cachedUnits.keys.toList()}');
    debugPrint('   - عدد الوحدات للمادة: ${units.length}');
    if (units.isNotEmpty) {
      debugPrint(
        '   - الوحدات الموجودة: ${units.map((u) => u.name).join(", ")}',
      );
    }

    return units;
  }

  /// الحصول على دروس مادة معينة
  List<Lesson> getLessonsBySubject(String subjectId) {
    // البحث في المفاتيح المباشرة أولاً
    var lessons = _cachedLessons[subjectId] ?? [];

    // إذا لم توجد، ابحث في جميع الدروس بناءً على subjectId
    if (lessons.isEmpty) {
      lessons = _cachedLessons.values
          .expand((lessonList) => lessonList)
          .where((lesson) => lesson.subjectId == subjectId)
          .toList();
    }

    debugPrint('🔍 [DEBUG] getLessonsBySubject($subjectId):');
    debugPrint('   - المفاتيح المتاحة: ${_cachedLessons.keys.toList()}');
    debugPrint('   - عدد الدروس للمادة: ${lessons.length}');
    if (lessons.isNotEmpty) {
      debugPrint(
        '   - الدروس الموجودة: ${lessons.map((l) => l.name).join(", ")}',
      );
    }

    return lessons;
  }

  /// الحصول على أسئلة مادة معينة
  List<Question> getQuestionsBySubject(String subjectId) {
    // البحث في المفاتيح المباشرة أولاً
    var questions = _cachedQuestions[subjectId] ?? [];

    // إذا لم توجد، ابحث في جميع الأسئلة بناءً على subjectId
    if (questions.isEmpty) {
      questions = _cachedQuestions.values
          .expand((questionList) => questionList)
          .where((question) => question.subjectId == subjectId)
          .toList();
    }

    return questions;
  }

  /// الحصول على وحدات فيديو لمادة معينة
  List<VideoUnit> getVideoUnitsBySubject(String subjectId) {
    return _cachedVideoUnits[subjectId] ?? [];
  }

  /// الحصول على دروس فيديو لمادة معينة
  List<VideoLesson> getVideoLessonsBySubject(String subjectId) {
    return _cachedVideoLessons[subjectId] ?? [];
  }

  /// الحصول على دروس فيديو لوحدة معينة
  List<VideoLesson> getVideoLessonsByUnit(String unitId) {
    final allLessons = <VideoLesson>[];

    // البحث في جميع المواد عن الدروس التي تنتمي للوحدة المحددة
    for (final lessons in _cachedVideoLessons.values) {
      final unitLessons = lessons
          .where((lesson) => lesson.unitId == unitId && lesson.isActive)
          .toList();
      allLessons.addAll(unitLessons);
    }

    // ترتيب الدروس حسب الترتيب
    allLessons.sort((a, b) => a.order.compareTo(b.order));

    return allLessons;
  }

  /// الحصول على فيديوهات مادة معينة
  List<Video> getVideosBySubject(String subjectId) {
    return _cachedVideos[subjectId] ?? [];
  }

  /// الحصول على فيديوهات لدرس معين
  List<Video> getVideosByLesson(String lessonId) {
    final allVideos = <Video>[];

    // البحث في جميع المواد عن الفيديوهات التي تنتمي للدرس المحدد
    for (final videos in _cachedVideos.values) {
      final lessonVideos = videos
          .where((video) => video.lessonId == lessonId && video.isActive)
          .toList();
      allVideos.addAll(lessonVideos);
    }

    // ترتيب الفيديوهات حسب الترتيب
    allVideos.sort((a, b) => a.order.compareTo(b.order));

    return allVideos;
  }

  // ═══════════════════════════════════════════════════════════════
  // 🧹 تنظيف الكاش
  // ═══════════════════════════════════════════════════════════════

  /// مسح جميع البيانات المخزنة في الكاش
  void clearCache() {
    _cachedSections = null;
    _cachedSubjects = null;
    _cachedVideoSections = null;
    _cachedVideoSubjects = null;
    _cachedUnits.clear();
    _cachedLessons.clear();
    _cachedQuestions.clear();
    _cachedVideoUnits.clear();
    _cachedVideoLessons.clear();
    _cachedVideos.clear();

    debugPrint('🧹 تم مسح جميع البيانات من الكاش');
  }

  /// التحقق من وجود بيانات في الكاش
  bool hasData() {
    return (_cachedSections?.isNotEmpty ?? false) ||
        (_cachedVideoSections?.isNotEmpty ?? false) ||
        _cachedUnits.isNotEmpty ||
        _cachedVideoUnits.isNotEmpty;
  }

  /// تصحيح خاصية isFree لمواد الفيديو بناءً على القسم
  void _correctVideoSubjectsFreeStatus() {
    debugPrint('🔧 تصحيح خاصية isFree لمواد الفيديو...');

    if (_cachedVideoSubjects == null || _cachedVideoSections == null) {
      debugPrint('⚠️ البيانات غير متوفرة للتصحيح');
      return;
    }

    for (final videoSubject in _cachedVideoSubjects!) {
      // البحث عن القسم المرتبط
      final relatedSection = _cachedVideoSections!.firstWhere(
        (section) => section.id == videoSubject.sectionId,
        orElse: () => VideoSection(
          id: '',
          name: '',
          description: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: '',
          isFree: false,
        ),
      );

      // تصحيح خاصية isFree بناءً على القسم
      final originalIsFree = videoSubject.isFree;
      final sectionIsFree = relatedSection.isFree;

      if (originalIsFree != sectionIsFree) {
        debugPrint(
          '🔧 تصحيح مادة فيديو: ${videoSubject.name} - isFree: $originalIsFree → $sectionIsFree (القسم: ${relatedSection.name})',
        );
        // لا يمكن تعديل isFree لأنه final، لكن يمكننا تسجيل المشكلة
        debugPrint('⚠️ يجب تصحيح هذا في البيانات الأصلية');
      }
    }

    debugPrint('✅ تم فحص خاصية isFree لجميع مواد الفيديو');
  }

  /// طباعة إحصائيات البيانات المحملة
  void printDataStatistics() {
    debugPrint('📊 [STUDENT MONITOR] ═══ إحصائيات البيانات المحملة ═══');
    debugPrint('🕐 [STUDENT MONITOR] الوقت: ${DateTime.now()}');

    // إحصائيات الاختبارات
    debugPrint('📝 [STUDENT MONITOR] بيانات الاختبارات:');
    debugPrint('   - الأقسام: ${_cachedSections?.length ?? 0}');
    debugPrint('   - المواد: ${_cachedSubjects?.length ?? 0}');
    debugPrint(
      '   - الوحدات: ${_cachedUnits.values.fold(0, (sum, units) => sum + units.length)}',
    );
    debugPrint(
      '   - الدروس: ${_cachedLessons.values.fold(0, (sum, lessons) => sum + lessons.length)}',
    );
    debugPrint(
      '   - الأسئلة: ${_cachedQuestions.values.fold(0, (sum, questions) => sum + questions.length)}',
    );

    // إحصائيات الفيديوهات
    debugPrint('🎥 [STUDENT MONITOR] بيانات الفيديوهات:');
    debugPrint('   - الأقسام: ${_cachedVideoSections?.length ?? 0}');
    debugPrint('   - المواد: ${_cachedVideoSubjects?.length ?? 0}');
    debugPrint(
      '   - الوحدات: ${_cachedVideoUnits.values.fold(0, (sum, units) => sum + units.length)}',
    );
    debugPrint(
      '   - الدروس: ${_cachedVideoLessons.values.fold(0, (sum, lessons) => sum + lessons.length)}',
    );
    debugPrint(
      '   - الفيديوهات: ${_cachedVideos.values.fold(0, (sum, videos) => sum + videos.length)}',
    );

    // تفاصيل الأقسام
    if (_cachedSections?.isNotEmpty ?? false) {
      debugPrint('📋 [STUDENT MONITOR] تفاصيل الأقسام:');
      for (var section in _cachedSections!) {
        final subjectCount =
            _cachedSubjects?.where((s) => s.sectionId == section.id).length ??
            0;
        debugPrint(
          '   - ${section.name}: $subjectCount مادة (مجاني: ${section.isFree})',
        );
      }
    }

    // تفاصيل أقسام الفيديو
    if (_cachedVideoSections?.isNotEmpty ?? false) {
      debugPrint('🎬 [STUDENT MONITOR] تفاصيل أقسام الفيديو:');
      for (var section in _cachedVideoSections!) {
        final subjectCount =
            _cachedVideoSubjects
                ?.where((s) => s.sectionId == section.id)
                .length ??
            0;
        debugPrint(
          '   - ${section.name}: $subjectCount مادة (مجاني: ${section.isFree})',
        );
      }
    }

    debugPrint('📊 [STUDENT MONITOR] ═══════════════════════════════════════');
  }

  /// تحويل int إلى Timestamp عند قراءة البيانات من SharedPreferences
  Map<String, dynamic> _convertIntToTimestamp(Map<String, dynamic> data) {
    final result = <String, dynamic>{};

    data.forEach((key, value) {
      if (value is int && _isTimestampField(key)) {
        // تحويل int إلى Timestamp
        result[key] = Timestamp.fromMillisecondsSinceEpoch(value);
      } else if (value is Map<String, dynamic>) {
        // تحويل المخططات المتداخلة
        result[key] = _convertIntToTimestamp(value);
      } else if (value is List) {
        // تحويل القوائم
        result[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _convertIntToTimestamp(item);
          } else if (item is int && _isTimestampField(key)) {
            return Timestamp.fromMillisecondsSinceEpoch(item);
          }
          return item;
        }).toList();
      } else {
        // نسخ القيم الأخرى كما هي
        result[key] = value;
      }
    });

    return result;
  }

  /// التحقق من أن الحقل هو حقل تاريخ
  bool _isTimestampField(String fieldName) {
    return fieldName == 'createdAt' ||
        fieldName == 'updatedAt' ||
        fieldName == 'lastUpdated' ||
        fieldName.endsWith('At') ||
        fieldName.endsWith('Date') ||
        fieldName.endsWith('Time');
  }
}
