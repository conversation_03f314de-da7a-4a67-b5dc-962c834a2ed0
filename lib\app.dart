import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';

import 'flavors.dart';
import 'core/theme/app_theme.dart';
import 'features/student/presentation/pages/simple_student_home_page.dart';
import 'features/admin/presentation/pages/admin_home_page.dart';
import 'shared/services/new_subscription_service.dart';
import 'shared/services/exam_service.dart';
import 'shared/services/content_service.dart';
import 'shared/services/pricing_service.dart';
import 'shared/services/firebase_service.dart';
import 'shared/services/theme_service.dart';
// النظام الجديد للقراءة الواحدة
import 'shared/services/single_read_data_service.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // تهيئة الخدمات عند بدء التطبيق
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeServices();
    });
  }

  /// تهيئة الخدمات حسب نوع التطبيق
  Future<void> _initializeServices() async {
    // تهيئة خدمة الثيمات أولاً
    try {
      await ThemeService.instance.initialize();
      debugPrint('🎨 تم تهيئة خدمة الثيمات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الثيمات: $e');
    }

    if (F.isStudent) {
      try {
        // تهيئة Firebase Authentication لتطبيق الطالب أولاً
        await _initializeStudentServices();
        debugPrint('🚀 بدء تهيئة النظام الجديد للقراءة الواحدة...');
        // ثم تهيئة النظام الجديد للقراءة الواحدة
        await SingleReadDataService.instance.initialize();
        debugPrint('✅ تم تهيئة النظام الجديد بنجاح');
      } catch (e) {
        debugPrint('❌ خطأ في تهيئة خدمات الطالب: $e');
        debugPrint('🔄 سيتم المتابعة بالنظام القديم');
      }
    } else {
      // تهيئة Firebase Authentication لتطبيق الإدارة
      await _initializeAdminServices();
    }
  }

  /// تهيئة خدمات تطبيق الطالب
  Future<void> _initializeStudentServices() async {
    try {
      debugPrint('🔐 بدء تسجيل دخول مجهول للطالب...');
      final user = await FirebaseService.instance.signInAnonymously();
      if (user != null) {
        debugPrint('✅ تم تسجيل دخول الطالب بنجاح - UID: ${user.uid}');
      } else {
        debugPrint('❌ فشل في تسجيل دخول الطالب');
        throw Exception('فشل في المصادقة المجهولة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمات الطالب: $e');
      rethrow; // إعادة رمي الخطأ لمنع تهيئة النظام الجديد
    }
  }

  /// تهيئة خدمات تطبيق الإدارة
  Future<void> _initializeAdminServices() async {
    try {
      // تسجيل دخول مجهول للإدارة للوصول إلى Firebase
      await FirebaseService.instance.signInAnonymously();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمات الإدارة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحديد حجم التصميم بناءً على نوع الجهاز
    Size designSize = const Size(375, 812); // افتراضي للهاتف

    // للكمبيوتر، نستخدم حجم أكبر
    if (kIsWeb ||
        (!kIsWeb &&
            (Platform.isWindows || Platform.isMacOS || Platform.isLinux))) {
      designSize = const Size(1200, 800); // للكمبيوتر
    }

    return ScreenUtilInit(
      designSize: designSize,
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            // خدمة الثيمات
            ChangeNotifierProvider.value(value: ThemeService.instance),
            // النظام الجديد للقراءة الواحدة
            ChangeNotifierProvider.value(value: SingleReadDataService.instance),
            ChangeNotifierProvider.value(value: SubscriptionService.instance),
            // الخدمات الحالية
            ChangeNotifierProvider(create: (_) => ExamService.instance),
            ChangeNotifierProvider(create: (_) => ContentService.instance),
            ChangeNotifierProvider(create: (_) => PricingService.instance),
          ],
          child: Consumer<ThemeService>(
            builder: (context, themeService, child) {
              return MaterialApp(
                title: F.title,
                debugShowCheckedModeBanner: false,
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: themeService.themeMode,
                home: F.isStudent
                    ? const SimpleStudentHomePage()
                    : const AdminHomePage(),
              );
            },
          ),
        );
      },
    );
  }
}
