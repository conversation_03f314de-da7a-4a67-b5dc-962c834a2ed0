import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/unit_model.dart';
import '../../../../shared/models/lesson_model.dart';
import '../../../../shared/services/content_service.dart';

class UnitsPage extends StatefulWidget {
  final Subject subject;

  const UnitsPage({super.key, required this.subject});

  @override
  State<UnitsPage> createState() => _UnitsPageState();
}

class _UnitsPageState extends State<UnitsPage> {
  List<Unit> _units = [];
  final Map<String, List<Lesson>> _unitLessons = {};
  bool _isLoading = false; // ابدأ بدون تحميل - سيتم تعيينه حسب الحاجة

  @override
  void initState() {
    super.initState();
    _loadUnits();
  }

  Future<void> _loadUnits() async {
    try {
      final units = await ContentService.instance.getSubjectUnits(
        widget.subject.id,
      );

      // تحميل الدروس لكل وحدة
      for (final unit in units) {
        final lessons = await ContentService.instance.getUnitLessons(unit.id);
        _unitLessons[unit.id] = lessons;
      }

      setState(() {
        _units = units;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_units.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadUnits,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _units.length,
        itemBuilder: (context, index) {
          final unit = _units[index];
          final lessons = _unitLessons[unit.id] ?? [];
          return _buildUnitCard(unit, lessons);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد وحدات متاحة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الوحدات قريباً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitCard(Unit unit, List<Lesson> lessons) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: ExpansionTile(
        tilePadding: EdgeInsets.all(16.w),
        childrenPadding: EdgeInsets.symmetric(horizontal: 16.w),
        leading: Container(
          width: 50.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: Color(
              int.parse(unit.color.replaceFirst('#', '0xFF')),
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Icon(
            Icons.folder_outlined,
            color: Color(int.parse(unit.color.replaceFirst('#', '0xFF'))),
            size: 24.sp,
          ),
        ),
        title: Text(
          unit.name,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4.h),
            Text(
              unit.description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Icon(
                  Icons.play_lesson,
                  size: 16.sp,
                  color: AppTheme.primaryColor,
                ),
                SizedBox(width: 4.w),
                Text(
                  '${lessons.length} درس',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(width: 16.w),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: unit.isActive
                        ? AppTheme.successColor.withValues(alpha: 0.1)
                        : AppTheme.textSecondaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    unit.isActive ? 'متاح' : 'مقفل',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: unit.isActive
                          ? AppTheme.successColor
                          : AppTheme.textSecondaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        children: lessons.map((lesson) => _buildLessonItem(lesson)).toList(),
      ),
    );
  }

  Widget _buildLessonItem(Lesson lesson) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppTheme.dividerColor),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.play_circle_outline,
              color: AppTheme.primaryColor,
              size: 20.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  lesson.name,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
                SizedBox(height: 2.h),
                Text(
                  lesson.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: lesson.isActive
                  ? AppTheme.successColor.withValues(alpha: 0.1)
                  : AppTheme.textSecondaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              lesson.isActive ? 'متاح' : 'مقفل',
              style: TextStyle(
                fontSize: 10.sp,
                color: lesson.isActive
                    ? AppTheme.successColor
                    : AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
