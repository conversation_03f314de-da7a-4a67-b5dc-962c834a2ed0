class PricingMessage {
  final String id;
  final String message;
  final String telegramUsername;
  final String whatsappNumber;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  const PricingMessage({
    required this.id,
    required this.message,
    this.telegramUsername = '',
    this.whatsappNumber = '',
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  /// إنشاء من Map (Firebase)
  factory PricingMessage.fromMap(Map<String, dynamic> map) {
    return PricingMessage(
      id: map['id'] ?? '',
      message: map['message'] ?? '',
      telegramUsername: map['telegramUsername'] ?? '',
      whatsappNumber: map['whatsappNumber'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
      isActive: map['isActive'] ?? true,
    );
  }

  /// تحويل إلى Map (Firebase)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'message': message,
      'telegramUsername': telegramUsername,
      'whatsappNumber': whatsappNumber,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'isActive': isActive,
    };
  }

  /// إنشاء نسخة محدثة
  PricingMessage copyWith({
    String? id,
    String? message,
    String? telegramUsername,
    String? whatsappNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return PricingMessage(
      id: id ?? this.id,
      message: message ?? this.message,
      telegramUsername: telegramUsername ?? this.telegramUsername,
      whatsappNumber: whatsappNumber ?? this.whatsappNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  /// تحويل إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'message': message,
      'telegramUsername': telegramUsername,
      'whatsappNumber': whatsappNumber,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isActive': isActive,
    };
  }

  /// إنشاء من Firestore
  factory PricingMessage.fromFirestore(
    Map<String, dynamic> data,
    String documentId,
  ) {
    return PricingMessage(
      id: documentId,
      message: data['message'] ?? '',
      telegramUsername: data['telegramUsername'] ?? '',
      whatsappNumber: data['whatsappNumber'] ?? '',
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
      isActive: data['isActive'] ?? true,
    );
  }

  /// تحليل التاريخ من Firestore
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is DateTime) return value;
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    return DateTime.now();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PricingMessage &&
        other.id == id &&
        other.message == message &&
        other.telegramUsername == telegramUsername &&
        other.whatsappNumber == whatsappNumber &&
        other.isActive == isActive;
  }

  @override
  int get hashCode =>
      Object.hash(id, message, telegramUsername, whatsappNumber, isActive);

  @override
  String toString() {
    return 'PricingMessage(id: $id, message: $message, telegramUsername: $telegramUsername, whatsappNumber: $whatsappNumber, isActive: $isActive)';
  }
}
