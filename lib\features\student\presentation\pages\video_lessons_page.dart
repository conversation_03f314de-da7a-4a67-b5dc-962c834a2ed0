import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_lesson_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/data_distribution_service.dart';
// النظام الجديد Offline-First
import 'video_list_page.dart';

/// صفحة دروس الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoLessonsPage extends StatefulWidget {
  final String unitId;
  final bool hasSubscription;

  const VideoLessonsPage({
    super.key,
    required this.unitId,
    required this.hasSubscription,
  });

  @override
  State<VideoLessonsPage> createState() => _VideoLessonsPageState();
}

class _VideoLessonsPageState extends State<VideoLessonsPage> {
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;
  final DataDistributionService _dataService = DataDistributionService.instance;

  List<VideoLesson> _lessons = [];

  @override
  void initState() {
    super.initState();
    _loadVideoLessonsImmediately();
  }

  /// تحميل فوري لدروس الفيديو من النظام الجديد
  Future<void> _loadVideoLessonsImmediately() async {
    try {
      // استخدام خدمة فرز البيانات الجديدة
      final lessons = _dataService.getVideoLessonsByUnit(widget.unitId);

      if (mounted) {
        setState(() {
          _lessons = lessons;
        });
        debugPrint(
          '⚡ تم تحميل ${lessons.length} درس فيديو فوراً من خدمة فرز البيانات',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل دروس الفيديو: $e');
      // fallback للنظام القديم
      await _loadSavedDataFirst();
    }
  }

  /// تحميل البيانات المحفوظة أولاً
  Future<void> _loadSavedDataFirst() async {
    try {
      // فحص البيانات المحلية من الكاش أولاً (بدون await)
      final cachedLessons = _videoService.getCachedVideoLessons(widget.unitId);
      final activeCachedLessons = cachedLessons
          .where((lesson) => lesson.isActive)
          .toList();

      if (activeCachedLessons.isNotEmpty) {
        // البيانات متوفرة في الكاش - عرضها فوراً
        setState(() {
          _lessons = activeCachedLessons;
        });
        debugPrint(
          '⚡ إرجاع ${activeCachedLessons.length} درس فيديو من الكاش للوحدة ${widget.unitId}',
        );
        debugPrint(
          '🚀 تم عرض ${activeCachedLessons.length} درس فيديو من البيانات المحفوظة',
        );

        // تحميل بيانات الاشتراك في الخلفية
        _loadSubscriptionInBackground();

        // بدء التحديث في الخلفية
        _updateFromNetworkSilently();
        return;
      }

      // إذا لم توجد بيانات في الكاش، حاول تحميل البيانات من Firebase
      debugPrint('📱 لا توجد دروس في الكاش، محاولة تحميل من Firebase...');

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();

      // محاولة تحميل البيانات من Firebase
      try {
        final savedLessons = await _videoService.getVideoLessons(widget.unitId);
        final activeSavedLessons = savedLessons
            .where((lesson) => lesson.isActive)
            .toList();

        setState(() {
          _lessons = activeSavedLessons;
        });

        if (activeSavedLessons.isNotEmpty) {
          debugPrint(
            '✅ تم تحميل ${activeSavedLessons.length} درس فيديو من Firebase',
          );
        } else {
          debugPrint('📱 لا توجد دروس نشطة للوحدة ${widget.unitId}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في تحميل البيانات من Firebase: $e');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();
    }
  }

  /// تحميل بيانات الاشتراك في الخلفية
  Future<void> _loadSubscriptionInBackground() async {
    try {
      await _subscriptionService.loadUserSubscription();
      // تحديث صامت من الشبكة في الخلفية
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك: $e');
    }
  }

  /// تحديث صامت من الشبكة في الخلفية
  Future<void> _updateFromNetworkSilently() async {
    try {
      final hasInternet = await _checkInternetConnection();
      if (!hasInternet) {
        debugPrint('📱 لا توجد شبكة - الاعتماد على البيانات المحلية فقط');
        return;
      }

      debugPrint('🌐 تحديث دروس الفيديوهات من الشبكة في الخلفية...');
      // تحديث الدروس من Firebase باستخدام الدالة الجديدة
      final updatedLessons = await _videoService
          .refreshVideoLessonsFromFirebase(widget.unitId);

      if (mounted && updatedLessons.isNotEmpty) {
        final activeUpdatedLessons = updatedLessons
            .where((lesson) => lesson.isActive)
            .toList();
        setState(() {
          _lessons = activeUpdatedLessons;
        });
        debugPrint(
          '🔄 تم تحديث ${activeUpdatedLessons.length} درس فيديو من الشبكة',
        );
      }
    } catch (e) {
      debugPrint(
        '⚠️ فشل التحديث من الشبكة (لا مشكلة - البيانات المحلية متوفرة): $e',
      );
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // تم إزالة وظيفة التحديث الأولى - التحديث يتم من الصفحة الرئيسية فقط

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'الدروس',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.getPrimaryGradient(context),
        ),
      ),
      foregroundColor: Colors.white,
      // تم إزالة زر التحديث - التحديث يتم من الصفحة الرئيسية فقط
    );
  }

  Widget _buildBody() {
    // النظام الجديد: عرض البيانات مباشرة بدون تحديث
    return _lessons.isNotEmpty ? _buildLessonsContent() : _buildEmptyState();
  }

  // تم إزالة وظيفة التحديث الثانية - التحديث يتم من الصفحة الرئيسية فقط

  Widget _buildLessonsContent() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: 20.h),
          Expanded(child: _buildLessonsList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.play_lesson, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'دروس الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر الدرس لمشاهدة الفيديوهات التعليمية',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLessonsList() {
    return ListView.builder(
      itemCount: _lessons.length,
      itemBuilder: (context, index) {
        final lesson = _lessons[index];
        return _buildLessonCard(lesson);
      },
    );
  }

  Widget _buildLessonCard(VideoLesson lesson) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _navigateToVideos(lesson),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                _buildLessonIcon(lesson),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        lesson.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      if (lesson.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          lesson.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textSecondaryColor,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLessonIcon(VideoLesson lesson) {
    return Container(
      width: 50.w,
      height: 50.w,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Icon(Icons.play_lesson, color: Colors.white, size: 24.sp),
    );
  }

  void _navigateToVideos(VideoLesson lesson) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoListPage(
          lessonId: lesson.id,
          lesson: lesson,
          hasSubscription: widget.hasSubscription,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_lesson_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد دروس فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الدروس قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
