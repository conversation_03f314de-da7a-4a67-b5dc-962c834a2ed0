import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/services/exam_service.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';
import 'course_units_selection_page.dart';
import 'course_lessons_selection_page.dart';

class CourseQuestionsPage extends StatefulWidget {
  final Subject subject;
  final bool isFreeAccess;

  const CourseQuestionsPage({
    super.key,
    required this.subject,
    this.isFreeAccess = false,
  });

  @override
  State<CourseQuestionsPage> createState() => _CourseQuestionsPageState();
}

class _CourseQuestionsPageState extends State<CourseQuestionsPage> {
  int _totalCourseQuestions = 0;
  int _courseQuestionsByUnits = 0;
  int _courseQuestionsByLessons = 0;

  Map<String, String> _unitsWithCourses = {}; // unitId -> unitName
  Map<String, String> _lessonsWithCourses = {}; // lessonId -> lessonName

  @override
  void initState() {
    super.initState();
    _loadCourseQuestionsCounts();
  }

  Future<void> _loadCourseQuestionsCounts() async {
    try {
      debugPrint(
        '🔍 بدء تحميل أسئلة الدورات للمادة: ${widget.subject.name} (ID: ${widget.subject.id})',
      );

      // تحميل جميع أسئلة الدورات للمادة
      final allQuestions = await ExamService.instance
          .getQuestionsBySubject(widget.subject.id)
          .first;

      debugPrint(
        '📊 تم تحميل ${allQuestions.length} سؤال إجمالي للمادة ${widget.subject.name}',
      );

      var courseQuestions = allQuestions
          .where((q) => q.isCourseQuestion)
          .toList();

      debugPrint(
        '🎓 تم العثور على ${courseQuestions.length} سؤال دورة من أصل ${allQuestions.length}',
      );

      // طباعة تفاصيل الأسئلة للتشخيص
      debugPrint('📝 تفاصيل جميع الأسئلة:');
      for (int i = 0; i < allQuestions.length; i++) {
        final q = allQuestions[i];
        debugPrint(
          '  السؤال ${i + 1}: ${q.questionText.substring(0, math.min(50, q.questionText.length))}... - isCourseQuestion: ${q.isCourseQuestion} - subjectId: ${q.subjectId}',
        );
      }

      // طباعة تفاصيل أسئلة الدورات فقط
      if (courseQuestions.isNotEmpty) {
        debugPrint('🎓 تفاصيل أسئلة الدورات:');
        for (int i = 0; i < courseQuestions.length; i++) {
          final q = courseQuestions[i];
          debugPrint(
            '  دورة ${i + 1}: ${q.questionText.substring(0, math.min(50, q.questionText.length))}... - unitId: ${q.unitId} - lessonId: ${q.lessonId}',
          );
        }
      }

      if (courseQuestions.isEmpty) {
        debugPrint('❌ لا توجد أسئلة دورات للمادة ${widget.subject.name}');
        debugPrint('🔄 محاولة تحميل جميع أسئلة الدورات من جميع المواد...');

        // محاولة تحميل جميع أسئلة الدورات
        final allCourseQuestions = await ExamService.instance
            .getAllCourseQuestions()
            .first;
        debugPrint(
          '🌍 تم تحميل ${allCourseQuestions.length} سؤال دورة من جميع المواد',
        );

        // طباعة تفاصيل جميع أسئلة الدورات
        for (int i = 0; i < allCourseQuestions.length; i++) {
          final q = allCourseQuestions[i];
          debugPrint(
            '  دورة عامة ${i + 1}: ${q.questionText.substring(0, math.min(50, q.questionText.length))}... - subjectId: ${q.subjectId}',
          );
        }

        // تصفية أسئلة الدورات للمادة الحالية
        final filteredCourseQuestions = allCourseQuestions
            .where((q) => q.subjectId == widget.subject.id)
            .toList();

        debugPrint(
          '🎯 تم العثور على ${filteredCourseQuestions.length} سؤال دورة للمادة الحالية بعد التصفية',
        );

        if (filteredCourseQuestions.isEmpty) {
          debugPrint(
            '❌ لا توجد أسئلة دورات للمادة ${widget.subject.name} حتى بعد البحث الشامل',
          );
          setState(() {
            _totalCourseQuestions = 0;
            _courseQuestionsByUnits = 0;
            _courseQuestionsByLessons = 0;
            _unitsWithCourses.clear();
            _lessonsWithCourses.clear();
          });
          return;
        } else {
          // استخدام الأسئلة المصفاة
          courseQuestions = filteredCourseQuestions;
          debugPrint('✅ تم العثور على أسئلة دورات بعد البحث الشامل!');
        }
      }

      // تجميع الوحدات والدروس
      final unitsMap = <String, String>{};
      final lessonsMap = <String, String>{};

      for (final question in courseQuestions) {
        if (question.unitId.isNotEmpty) {
          unitsMap[question.unitId] =
              'الوحدة ${question.unitId}'; // سنحسن هذا لاحقاً
        }
        if (question.lessonId.isNotEmpty) {
          lessonsMap[question.lessonId] =
              'الدرس ${question.lessonId}'; // سنحسن هذا لاحقاً
        }
      }

      setState(() {
        _totalCourseQuestions = courseQuestions.length;
        _courseQuestionsByUnits = unitsMap.length;
        _courseQuestionsByLessons = lessonsMap.length;
        _unitsWithCourses = unitsMap;
        _lessonsWithCourses = lessonsMap;
      });

      debugPrint(
        'تم تحميل ${courseQuestions.length} سؤال دورة للمادة ${widget.subject.name}',
      );
    } catch (e) {
      debugPrint('خطأ في تحميل عدد أسئلة الدورات: $e');
      setState(() {
        _totalCourseQuestions = 0;
        _courseQuestionsByUnits = 0;
        _courseQuestionsByLessons = 0;
        _unitsWithCourses.clear();
        _lessonsWithCourses.clear();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadCourseQuestionsCounts,
      child: ListView(
        padding: EdgeInsets.all(16.w),
        children: [
          // رسالة توضيحية
          _buildInfoCard(),

          SizedBox(height: 16.h),

          // جميع أسئلة الدورات
          _buildOptionCard(
            title: 'جميع أسئلة الدورات',
            subtitle: 'جميع الأسئلة المخصصة للدورات التدريبية',
            count: _totalCourseQuestions,
            icon: Icons.school,
            color: AppTheme.accentColor,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => QuestionsViewerPage(
                    title: 'أسئلة الدورات',
                    subject: widget.subject,
                    questionType: QuestionFilterType.course,
                    isFreeAccess: widget.isFreeAccess,
                  ),
                ),
              );
            },
          ),

          SizedBox(height: 16.h),

          // أسئلة الدورات حسب الوحدات
          _buildOptionCard(
            title: 'الدورات حسب الوحدات',
            subtitle: 'تصفح أسئلة الدورات مقسمة حسب الوحدات',
            count: _courseQuestionsByUnits,
            icon: Icons.folder_special,
            color: AppTheme.primaryColor,
            onTap: () {
              _showUnitsSelection();
            },
          ),

          SizedBox(height: 16.h),

          // أسئلة الدورات حسب الدروس
          _buildOptionCard(
            title: 'الدورات حسب الدروس',
            subtitle: 'تصفح أسئلة الدورات مقسمة حسب الدروس',
            count: _courseQuestionsByLessons,
            icon: Icons.play_lesson,
            color: AppTheme.secondaryColor,
            onTap: () {
              _showLessonsSelection();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          gradient: LinearGradient(
            colors: [
              AppTheme.accentColor.withValues(alpha: 0.1),
              AppTheme.accentColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: AppTheme.accentColor, size: 24.sp),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                'أسئلة الدورات هي أسئلة مخصصة للتدريب المكثف والمراجعة الشاملة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard({
    required String title,
    required String subtitle,
    required int count,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              // أيقونة
              Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Icon(icon, color: Colors.white, size: 30.sp),
              ),

              SizedBox(width: 16.w),

              // المحتوى
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(Icons.quiz_outlined, size: 16.sp, color: color),
                        SizedBox(width: 4.w),
                        Text(
                          '$count سؤال',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // سهم
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textSecondaryColor,
                size: 16.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUnitsSelection() {
    // الانتقال إلى صفحة اختيار الوحدات التي تحتوي على أسئلة دورات
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CourseUnitsSelectionPage(subject: widget.subject),
      ),
    );
  }

  void _showLessonsSelection() {
    // الانتقال إلى صفحة اختيار الدروس التي تحتوي على أسئلة دورات
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            CourseLessonsSelectionPage(subject: widget.subject),
      ),
    );
  }
}
