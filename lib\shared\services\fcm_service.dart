import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'background_sync_service.dart';
import 'local_notifications_service.dart';
import 'offline_first_service.dart';

/// خدمة Firebase Cloud Messaging
/// تستقبل الإشعارات من Firebase وتحفز المزامنة عند الحاجة
class FCMService {
  static final FCMService _instance = FCMService._internal();
  static FCMService get instance => _instance;
  FCMService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  bool _isInitialized = false;
  String? _fcmToken;

  /// تهيئة خدمة FCM
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      debugPrint('🔔 تهيئة خدمة Firebase Cloud Messaging...');

      // طلب الإذن للإشعارات
      await _requestPermission();

      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // الحصول على FCM token
      await _getFCMToken();

      // إعداد معالجات الرسائل
      _setupMessageHandlers();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة FCM بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة FCM: $e');
    }
  }

  /// طلب الإذن للإشعارات
  Future<void> _requestPermission() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      debugPrint('🔔 حالة إذن الإشعارات: ${settings.authorizationStatus}');
    } catch (e) {
      debugPrint('❌ خطأ في طلب إذن الإشعارات: $e');
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    try {
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      debugPrint('✅ تم تهيئة الإشعارات المحلية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الإشعارات المحلية: $e');
    }
  }

  /// الحصول على FCM token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      debugPrint('🔑 FCM Token: $_fcmToken');
      
      // يمكن إرسال الـ token للخادم هنا إذا لزم الأمر
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على FCM token: $e');
    }
  }

  /// إعداد معالجات الرسائل
  void _setupMessageHandlers() {
    // معالج الرسائل عندما يكون التطبيق في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // معالج الرسائل عندما يكون التطبيق في الخلفية ويتم النقر على الإشعار
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

    // معالج الرسائل عندما يكون التطبيق مغلق ويتم فتحه من الإشعار
    _handleAppLaunchedFromNotification();

    debugPrint('✅ تم إعداد معالجات رسائل FCM');
  }

  /// معالجة الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      debugPrint('📨 تم استقبال رسالة FCM في المقدمة: ${message.messageId}');
      debugPrint('📨 العنوان: ${message.notification?.title}');
      debugPrint('📨 المحتوى: ${message.notification?.body}');
      debugPrint('📨 البيانات: ${message.data}');

      // معالجة البيانات
      await _processMessageData(message.data);

      // عرض إشعار محلي
      await _showLocalNotification(message);
    } catch (e) {
      debugPrint('❌ خطأ في معالجة رسالة المقدمة: $e');
    }
  }

  /// معالجة الرسائل في الخلفية
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    try {
      debugPrint('📨 تم فتح التطبيق من إشعار FCM: ${message.messageId}');
      
      // معالجة البيانات
      await _processMessageData(message.data);
    } catch (e) {
      debugPrint('❌ خطأ في معالجة رسالة الخلفية: $e');
    }
  }

  /// معالجة فتح التطبيق من إشعار
  Future<void> _handleAppLaunchedFromNotification() async {
    try {
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        debugPrint('📨 تم فتح التطبيق من إشعار FCM: ${initialMessage.messageId}');
        await _processMessageData(initialMessage.data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة فتح التطبيق من إشعار: $e');
    }
  }

  /// معالجة بيانات الرسالة
  Future<void> _processMessageData(Map<String, dynamic> data) async {
    try {
      final type = data['type'] as String?;
      
      switch (type) {
        case 'content_update':
          await _handleContentUpdate(data);
          break;
        case 'sync_request':
          await _handleSyncRequest(data);
          break;
        case 'announcement':
          await _handleAnnouncement(data);
          break;
        default:
          debugPrint('❓ نوع رسالة غير معروف: $type');
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة بيانات الرسالة: $e');
    }
  }

  /// معالجة تحديث المحتوى
  Future<void> _handleContentUpdate(Map<String, dynamic> data) async {
    try {
      debugPrint('🔄 تحديث محتوى مطلوب من FCM');
      
      final subjectId = data['subject_id'] as String?;
      final updateType = data['update_type'] as String?;
      
      // تحفيز المزامنة الفورية
      await BackgroundSyncService.instance.syncNow();
      
      // إنشاء إشعار محلي
      await LocalNotificationsService.instance.createContentUpdateNotification(
        subjectId: subjectId ?? 'unknown',
        subjectName: data['subject_name'] as String? ?? 'مادة غير معروفة',
        oldContent: {},
        newContent: {'update_type': updateType},
      );
      
      debugPrint('✅ تم معالجة تحديث المحتوى');
    } catch (e) {
      debugPrint('❌ خطأ في معالجة تحديث المحتوى: $e');
    }
  }

  /// معالجة طلب المزامنة
  Future<void> _handleSyncRequest(Map<String, dynamic> data) async {
    try {
      debugPrint('🔄 طلب مزامنة من FCM');
      
      // تحفيز المزامنة الفورية
      await BackgroundSyncService.instance.syncNow();
      
      debugPrint('✅ تم معالجة طلب المزامنة');
    } catch (e) {
      debugPrint('❌ خطأ في معالجة طلب المزامنة: $e');
    }
  }

  /// معالجة الإعلانات
  Future<void> _handleAnnouncement(Map<String, dynamic> data) async {
    try {
      debugPrint('📢 إعلان جديد من FCM');
      
      final title = data['title'] as String? ?? 'إعلان جديد';
      final message = data['message'] as String? ?? '';
      
      // يمكن إضافة منطق حفظ الإعلان محلياً هنا
      
      debugPrint('✅ تم معالجة الإعلان: $title');
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الإعلان: $e');
    }
  }

  /// عرض إشعار محلي
  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'smart_edu_channel',
        'Smart Edu Notifications',
        channelDescription: 'إشعارات تطبيق Smart Edu',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        message.hashCode,
        message.notification?.title ?? 'Smart Edu',
        message.notification?.body ?? 'رسالة جديدة',
        notificationDetails,
        payload: jsonEncode(message.data),
      );
    } catch (e) {
      debugPrint('❌ خطأ في عرض الإشعار المحلي: $e');
    }
  }

  /// معالجة النقر على الإشعار
  void _onNotificationTapped(NotificationResponse response) {
    try {
      debugPrint('👆 تم النقر على إشعار محلي: ${response.id}');
      
      if (response.payload != null) {
        final data = jsonDecode(response.payload!) as Map<String, dynamic>;
        _processMessageData(data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة النقر على الإشعار: $e');
    }
  }

  /// الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      debugPrint('✅ تم الاشتراك في الموضوع: $topic');
    } catch (e) {
      debugPrint('❌ خطأ في الاشتراك في الموضوع: $e');
    }
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      debugPrint('✅ تم إلغاء الاشتراك من الموضوع: $topic');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الاشتراك من الموضوع: $e');
    }
  }

  /// الحصول على FCM token
  String? get fcmToken => _fcmToken;

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;
}

/// معالج الرسائل في الخلفية (خارج الكلاس)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    debugPrint('📨 تم استقبال رسالة FCM في الخلفية: ${message.messageId}');
    
    // يمكن إضافة منطق معالجة الرسائل في الخلفية هنا
    // مثل تحفيز المزامنة أو حفظ البيانات محلياً
    
  } catch (e) {
    debugPrint('❌ خطأ في معالجة رسالة الخلفية: $e');
  }
}
