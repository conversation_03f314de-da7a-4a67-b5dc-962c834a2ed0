import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/foundation.dart' hide Key;
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// خدمة التشفير والحماية المتقدمة للفيديوهات والروابط
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  static EncryptionService get instance => _instance;
  EncryptionService._internal();

  // مفتاح التشفير الأساسي (يجب أن يكون آمناً في الإنتاج)
  static const String _baseEncryptionKey = 'SmartTestVideoSecureKey2024!@#';

  // مفاتيح إضافية للحماية المتقدمة
  static const String _deviceSalt = 'SmartEduDeviceSalt2024';
  static const String _videoSalt = 'SmartEduVideoSalt2024';
  static const String _sessionSalt = 'SmartEduSessionSalt2024';

  late final Encrypter _encrypter;
  late final IV _iv;
  late final Encrypter _advancedEncrypter;
  late final IV _advancedIv;
  bool _isInitialized = false;

  // نظام الحماية من التسجيل
  bool _isRecordingDetected = false;
  DateTime? _lastSecurityCheck;

  // معرف الجلسة الفريد
  late final String _sessionId;

  /// تهيئة خدمة التشفير المتقدمة
  void initialize() {
    if (_isInitialized) {
      return; // تم التهيئة مسبقاً
    }

    // إنشاء معرف جلسة فريد
    _sessionId = _generateSessionId();

    // إنشاء مفتاح التشفير الأساسي
    final keyBytes = sha256.convert(utf8.encode(_baseEncryptionKey)).bytes;
    final key = Key(Uint8List.fromList(keyBytes));
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);

    // إنشاء مفتاح التشفير المتقدم
    final advancedKeyBytes = sha256
        .convert(utf8.encode('$_baseEncryptionKey$_sessionSalt$_sessionId'))
        .bytes;
    final advancedKey = Key(Uint8List.fromList(advancedKeyBytes));
    _advancedEncrypter = Encrypter(AES(advancedKey));
    _advancedIv = IV.fromSecureRandom(16);

    _isInitialized = true;
    debugPrint('🔐 تم تهيئة نظام التشفير المتقدم - معرف الجلسة: $_sessionId');
  }

  /// إنشاء معرف جلسة فريد
  String _generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + (timestamp % 1000)).toString();
    final combined = '$timestamp$random$_sessionSalt';
    return sha256.convert(utf8.encode(combined)).toString().substring(0, 16);
  }

  // ===== نظام الحماية المتقدم =====

  /// فحص أمان البيئة قبل فك التشفير
  Future<bool> performSecurityCheck() async {
    try {
      // فحص التوقيت لمنع الهجمات المتكررة
      final now = DateTime.now();
      if (_lastSecurityCheck != null) {
        final timeDiff = now.difference(_lastSecurityCheck!).inMilliseconds;
        if (timeDiff < 100) {
          // منع الطلبات السريعة المتتالية
          debugPrint('⚠️ تم رفض الطلب - طلبات سريعة متتالية');
          return false;
        }
      }
      _lastSecurityCheck = now;

      // فحص منع تسجيل الشاشة (للأندرويد)
      if (!kIsWeb && Platform.isAndroid) {
        await _checkScreenRecording();
      }

      // فحص البيئة للويندوز
      if (!kIsWeb && Platform.isWindows) {
        await _checkWindowsEnvironment();
      }

      return !_isRecordingDetected;
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأمان: $e');
      return false;
    }
  }

  /// فحص تسجيل الشاشة المتقدم (للأندرويد)
  Future<void> _checkScreenRecording() async {
    try {
      // فحص التطبيقات المشبوهة المثبتة
      final suspiciousApps = [
        'com.mobizen.mirroring.uibc', // Mobizen
        'com.teamviewer.teamviewer.market.mobile', // TeamViewer
        'com.vysor.vysor', // Vysor
        'com.koushikdutta.vysor', // Vysor
        'com.apowersoft.mirror', // ApowerMirror
        'com.lonelycatgames.Xplore', // X-plore File Manager
        'com.speedsoftware.rootexplorer', // Root Explorer
      ];

      // فحص وضع المطور
      try {
        // يمكن إضافة فحص وضع المطور هنا
        debugPrint('🔍 فحص وضع المطور...');
      } catch (e) {
        debugPrint('خطأ في فحص وضع المطور: $e');
      }

      // فحص الـ Root
      try {
        // يمكن إضافة فحص الـ Root هنا
        debugPrint('🔍 فحص صلاحيات الـ Root...');
      } catch (e) {
        debugPrint('خطأ في فحص الـ Root: $e');
      }

      _isRecordingDetected = false;
      debugPrint('✅ فحص الأندرويد مكتمل - لا توجد تهديدات');
    } catch (e) {
      debugPrint('خطأ في فحص تسجيل الشاشة: $e');
      // في حالة الخطأ، نفترض وجود تهديد للأمان
      _isRecordingDetected = true;
    }
  }

  /// فحص بيئة الويندوز المتقدم
  Future<void> _checkWindowsEnvironment() async {
    try {
      // فحص العمليات المشبوهة
      final suspiciousProcesses = [
        'obs64.exe', 'obs32.exe', // OBS Studio
        'bandicam.exe', // Bandicam
        'fraps.exe', // Fraps
        'camtasia.exe', // Camtasia
        'ffmpeg.exe', // FFmpeg
        'vlc.exe', // VLC Media Player
        'potplayer.exe', // PotPlayer
        'kmplayer.exe', // KMPlayer
        'wireshark.exe', // Wireshark
        'fiddler.exe', // Fiddler
        'cheatengine.exe', // Cheat Engine
        'processhacker.exe', // Process Hacker
        'x64dbg.exe', 'x32dbg.exe', // x64dbg
      ];

      // فحص متغيرات البيئة المشبوهة
      final suspiciousEnvVars = [
        'DEBUGGING',
        'DEBUG_MODE',
        'DEVELOPMENT_MODE',
        'REVERSE_ENGINEERING',
      ];

      // فحص متغيرات البيئة
      for (final envVar in suspiciousEnvVars) {
        final value = Platform.environment[envVar];
        if (value != null && value.toLowerCase() == 'true') {
          debugPrint('⚠️ تم اكتشاف متغير بيئة مشبوه: $envVar');
          _isRecordingDetected = true;
          return;
        }
      }

      // فحص وجود ملفات مشبوهة في المجلد الحالي
      final currentDir = Directory.current;
      final suspiciousFiles = [
        'debug.log',
        'capture.exe',
        'recorder.exe',
        'dump.dmp',
      ];

      for (final fileName in suspiciousFiles) {
        final file = File('${currentDir.path}/$fileName');
        if (await file.exists()) {
          debugPrint('⚠️ تم اكتشاف ملف مشبوه: $fileName');
          _isRecordingDetected = true;
          return;
        }
      }

      _isRecordingDetected = false;
      debugPrint('✅ فحص بيئة الويندوز مكتمل - لا توجد تهديدات');
    } catch (e) {
      debugPrint('خطأ في فحص بيئة الويندوز: $e');
      // في حالة الخطأ، نفترض وجود تهديد للأمان
      _isRecordingDetected = true;
    }
  }

  /// تشفير نص (للروابط)
  String encryptText(String plainText) {
    try {
      final encrypted = _encrypter.encrypt(plainText, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      throw Exception('فشل في تشفير النص: $e');
    }
  }

  /// التحقق من ما إذا كان النص مشفراً
  bool isEncrypted(String text) {
    try {
      // محاولة فك التشفير للتحقق
      Encrypted.fromBase64(text);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تحويل رابط Google Drive إلى رابط تحميل مباشر
  String convertGoogleDriveUrl(String url) {
    try {
      // التحقق من أن الرابط من Google Drive
      if (url.contains('drive.google.com/file/d/')) {
        // استخراج معرف الملف من الرابط
        final regex = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
        final match = regex.firstMatch(url);

        if (match != null) {
          final fileId = match.group(1);
          // تحويل إلى رابط تحميل مباشر
          return 'https://drive.google.com/uc?export=download&id=$fileId';
        }
      }

      // إذا لم يكن رابط Google Drive أو لم نتمكن من تحويله، إرجاع الرابط الأصلي
      return url;
    } catch (e) {
      debugPrint('خطأ في تحويل رابط Google Drive: $e');
      return url;
    }
  }

  /// فك تشفير نص (للروابط) مع فحص الأمان المتقدم
  Future<String> decryptText(String text) async {
    try {
      // فحص الأمان قبل فك التشفير
      final isSecure = await performSecurityCheck();
      if (!isSecure) {
        throw Exception('فشل في فحص الأمان - تم رفض فك تشفير الرابط');
      }

      // إذا لم يكن النص مشفراً، إرجاعه كما هو مع إضافة الحماية
      if (!isEncrypted(text)) {
        final secureUrl = _addSecurityToken(text);
        return convertGoogleDriveUrl(secureUrl);
      }

      final encrypted = Encrypted.fromBase64(text);
      final decryptedText = _encrypter.decrypt(encrypted, iv: _iv);

      // إضافة token أمان للرابط المفكوك التشفير
      final secureUrl = _addSecurityToken(decryptedText);

      // تحويل رابط Google Drive إذا لزم الأمر
      return convertGoogleDriveUrl(secureUrl);
    } catch (e) {
      // تسجيل الخطأ للمطورين فقط مع معلومات إضافية للـ Windows
      if (!kIsWeb && Platform.isWindows) {
        debugPrint('خطأ في فك التشفير على Windows: $e');
        debugPrint('نوع النص: ${text.runtimeType}');
        debugPrint('طول النص: ${text.length}');
        debugPrint(
          'أول 50 حرف: ${text.length > 50 ? text.substring(0, 50) : text}',
        );
      } else {
        debugPrint('خطأ في فك التشفير: $e');
      }
      throw Exception('فشل في فك التشفير');
    }
  }

  /// إضافة token أمان مؤقت للرابط
  String _addSecurityToken(String url) {
    try {
      final uri = Uri.parse(url);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final token = _generateSecurityToken(url, timestamp);

      final newQueryParams = Map<String, String>.from(uri.queryParameters);
      newQueryParams['st'] = token; // security token
      newQueryParams['ts'] = timestamp.toString(); // timestamp
      newQueryParams['sid'] = _sessionId; // session id

      final newUri = uri.replace(queryParameters: newQueryParams);
      return newUri.toString();
    } catch (e) {
      debugPrint('خطأ في إضافة token الأمان: $e');
      return url; // إرجاع الرابط الأصلي في حالة الخطأ
    }
  }

  /// إنشاء token أمان للرابط
  String _generateSecurityToken(String url, int timestamp) {
    final combined = '$url$timestamp$_sessionId$_videoSalt';
    return sha256.convert(utf8.encode(combined)).toString().substring(0, 16);
  }

  /// تشفير بيانات الفيديو المتقدم (للملفات المحملة)
  Uint8List encryptVideoData(Uint8List videoData) {
    try {
      // التأكد من تهيئة المشفر
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مهيأة');
      }

      debugPrint(
        '🔐 بدء التشفير المتقدم للفيديو - الحجم: ${videoData.length} bytes',
      );

      // إضافة header مع معلومات الحماية
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final header = {
        'version': '2.0',
        'timestamp': timestamp,
        'sessionId': _sessionId,
        'checksum': _calculateChecksum(videoData),
      };

      final headerBytes = utf8.encode(json.encode(header));
      final headerLength = headerBytes.length;

      // تشفير البيانات بالمشفر المتقدم
      final encrypted = _advancedEncrypter.encryptBytes(
        videoData,
        iv: _advancedIv,
      );

      // دمج الـ header مع البيانات المشفرة
      final result = Uint8List(4 + headerLength + encrypted.bytes.length);
      result.setRange(0, 4, _intToBytes(headerLength));
      result.setRange(4, 4 + headerLength, headerBytes);
      result.setRange(4 + headerLength, result.length, encrypted.bytes);

      debugPrint(
        '✅ تم التشفير المتقدم للفيديو - الحجم النهائي: ${result.length} bytes',
      );
      return result;
    } catch (e) {
      debugPrint('❌ فشل في التشفير المتقدم للفيديو: $e');
      throw Exception('فشل في التشفير المتقدم للفيديو: $e');
    }
  }

  /// حساب checksum للبيانات
  String _calculateChecksum(Uint8List data) {
    return sha256.convert(data).toString().substring(0, 16);
  }

  /// تحويل int إلى bytes
  List<int> _intToBytes(int value) {
    return [
      (value >> 24) & 0xFF,
      (value >> 16) & 0xFF,
      (value >> 8) & 0xFF,
      value & 0xFF,
    ];
  }

  /// تحويل bytes إلى int
  int _bytesToInt(List<int> bytes) {
    return (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3];
  }

  /// فك تشفير بيانات الفيديو المتقدم مع فحص الأمان
  Future<Uint8List> decryptVideoData(Uint8List encryptedData) async {
    try {
      // التأكد من تهيئة المشفر
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مهيأة');
      }

      // فحص الأمان قبل فك التشفير
      final isSecure = await performSecurityCheck();
      if (!isSecure) {
        throw Exception('فشل في فحص الأمان - تم رفض فك التشفير');
      }

      debugPrint(
        '🔓 بدء فك التشفير المتقدم للفيديو - الحجم: ${encryptedData.length} bytes',
      );

      // محاولة فك التشفير بالنظام الجديد أولاً
      try {
        return await _decryptAdvancedVideoData(encryptedData);
      } catch (e) {
        debugPrint('⚠️ فشل النظام الجديد، محاولة النظام القديم: $e');
        return _decryptLegacyVideoData(encryptedData);
      }
    } catch (e) {
      // تسجيل الخطأ مع معلومات إضافية للـ Windows
      if (!kIsWeb && Platform.isWindows) {
        debugPrint('❌ فشل في فك تشفير بيانات الفيديو على Windows: $e');
        debugPrint('حجم البيانات المشفرة: ${encryptedData.length} bytes');
        debugPrint('حالة التهيئة: $_isInitialized');
      } else {
        debugPrint('❌ فشل في فك تشفير بيانات الفيديو: $e');
      }
      throw Exception('فشل في فك تشفير بيانات الفيديو: $e');
    }
  }

  /// فك التشفير المتقدم للنظام الجديد
  Future<Uint8List> _decryptAdvancedVideoData(Uint8List encryptedData) async {
    // قراءة طول الـ header
    if (encryptedData.length < 4) {
      throw Exception('بيانات مشفرة غير صالحة');
    }

    final headerLength = _bytesToInt(encryptedData.sublist(0, 4));
    if (headerLength <= 0 || headerLength > encryptedData.length - 4) {
      throw Exception('طول header غير صالح');
    }

    // قراءة الـ header
    final headerBytes = encryptedData.sublist(4, 4 + headerLength);
    final headerJson = utf8.decode(headerBytes);
    final header = json.decode(headerJson) as Map<String, dynamic>;

    // التحقق من الإصدار
    if (header['version'] != '2.0') {
      throw Exception('إصدار غير مدعوم');
    }

    // قراءة البيانات المشفرة
    final encryptedVideoData = encryptedData.sublist(4 + headerLength);
    final encrypted = Encrypted(encryptedVideoData);

    // فك التشفير
    final decryptedBytes = _advancedEncrypter.decryptBytes(
      encrypted,
      iv: _advancedIv,
    );
    final result = Uint8List.fromList(decryptedBytes);

    // التحقق من checksum
    final expectedChecksum = header['checksum'] as String;
    final actualChecksum = _calculateChecksum(result);
    if (expectedChecksum != actualChecksum) {
      throw Exception('فشل في التحقق من سلامة البيانات');
    }

    debugPrint('✅ تم فك التشفير المتقدم بنجاح - الحجم: ${result.length} bytes');
    return result;
  }

  /// فك التشفير للنظام القديم (للتوافق)
  Uint8List _decryptLegacyVideoData(Uint8List encryptedData) {
    final encrypted = Encrypted(encryptedData);
    final decryptedBytes = _encrypter.decryptBytes(encrypted, iv: _iv);
    final result = Uint8List.fromList(decryptedBytes);

    debugPrint('✅ تم فك التشفير القديم - الحجم: ${result.length} bytes');
    return result;
  }

  /// إنشاء hash للتحقق من سلامة البيانات
  String generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من hash البيانات
  bool verifyHash(String data, String expectedHash) {
    final actualHash = generateHash(data);
    return actualHash == expectedHash;
  }

  /// إنشاء مفتاح فريد للجهاز مع حماية متقدمة
  String generateDeviceKey(String deviceId) {
    final combined = '$deviceId$_baseEncryptionKey$_deviceSalt';
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 32);
  }

  /// تشفير رابط الفيديو مع معلومات إضافية
  Map<String, String> encryptVideoUrl(
    String url,
    String videoId,
    String deviceId,
  ) {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final deviceKey = generateDeviceKey(deviceId);

      // إنشاء payload مع معلومات الحماية
      final payload = {
        'url': url,
        'videoId': videoId,
        'deviceId': deviceId,
        'timestamp': timestamp,
        'deviceKey': deviceKey,
      };

      final jsonPayload = json.encode(payload);
      final encryptedPayload = encryptText(jsonPayload);
      final hash = generateHash(jsonPayload);

      return {
        'encryptedData': encryptedPayload,
        'hash': hash,
        'timestamp': timestamp,
      };
    } catch (e) {
      throw Exception('فشل في تشفير رابط الفيديو: $e');
    }
  }

  /// فك تشفير رابط الفيديو مع التحقق من الصحة المتقدم
  Future<Map<String, dynamic>?> decryptVideoUrl(
    Map<String, String> encryptedData,
    String deviceId,
  ) async {
    try {
      final encryptedPayload = encryptedData['encryptedData'];
      final expectedHash = encryptedData['hash'];
      final timestamp = encryptedData['timestamp'];

      if (encryptedPayload == null ||
          expectedHash == null ||
          timestamp == null) {
        return null;
      }

      // فك التشفير مع الحماية المتقدمة
      final jsonPayload = await decryptText(encryptedPayload);

      // التحقق من hash
      if (!verifyHash(jsonPayload, expectedHash)) {
        return null;
      }

      final payload = json.decode(jsonPayload) as Map<String, dynamic>;

      // التحقق من معرف الجهاز
      if (payload['deviceId'] != deviceId) {
        return null;
      }

      // التحقق من مفتاح الجهاز
      final expectedDeviceKey = generateDeviceKey(deviceId);
      if (payload['deviceKey'] != expectedDeviceKey) {
        return null;
      }

      // التحقق من انتهاء الصلاحية (24 ساعة)
      final payloadTimestamp = int.parse(payload['timestamp']);
      final currentTimestamp = DateTime.now().millisecondsSinceEpoch;
      const validityPeriod = 24 * 60 * 60 * 1000; // 24 ساعة بالميلي ثانية

      if (currentTimestamp - payloadTimestamp > validityPeriod) {
        return null; // انتهت الصلاحية
      }

      return payload;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء اسم ملف مشفر للفيديو المحمل
  String generateEncryptedFileName(String videoId, String deviceId) {
    final combined =
        '$videoId$deviceId${DateTime.now().millisecondsSinceEpoch}';
    final hash = generateHash(combined);
    return '${hash.substring(0, 16)}.enc'; // ملف مشفر بامتداد .enc
  }
}
