import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/pricing_message_service.dart';

class PricingManagementPage extends StatefulWidget {
  const PricingManagementPage({super.key});

  @override
  State<PricingManagementPage> createState() => _PricingManagementPageState();
}

class _PricingManagementPageState extends State<PricingManagementPage> {
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _telegramController = TextEditingController();
  final TextEditingController _whatsappController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // تهيئة خدمة رسالة الأسعار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PricingMessageService.instance.initialize().then((_) {
        if (PricingMessageService.instance.hasMessage) {
          final message = PricingMessageService.instance.currentMessage!;
          _messageController.text = message.message;
          _telegramController.text = message.telegramUsername;
          _whatsappController.text = message.whatsappNumber;
        }
      });
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _telegramController.dispose();
    _whatsappController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: PricingMessageService.instance,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إدارة رسالة الأسعار'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          actions: [
            IconButton(
              onPressed: _saveMessage,
              icon: const Icon(Icons.save),
              tooltip: 'حفظ الرسالة',
            ),
          ],
        ),
        body: Consumer<PricingMessageService>(
          builder: (context, service, child) {
            if (service.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            return RefreshIndicator(
              onRefresh: () => service.refresh(),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInstructionsCard(),
                    SizedBox(height: 24.h),
                    _buildMessageEditor(service),
                    SizedBox(height: 24.h),
                    _buildPreviewCard(service),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildInstructionsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              AppTheme.secondaryColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  'تعليمات كتابة رسالة الأسعار',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Text(
              'اكتب قائمة الأسعار بالتنسيق التالي:\n\n'
              '📚 الرياضيات ب 15000ل.س\n'
              '📚 الفيزياء ب 10000ل.س\n'
              '📚 الكيمياء ب 12000ل.س\n\n'
              'ستظهر هذه الرسالة للطلاب في صفحة أسعار الاشتراك',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageEditor(PricingMessageService service) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'رسالة الأسعار',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: _messageController,
              maxLines: 10,
              textDirection: TextDirection.rtl,
              decoration: InputDecoration(
                hintText:
                    'اكتب رسالة الأسعار هنا...\n\nمثال:\n📚 الرياضيات ب 15000ل.س\n📚 الفيزياء ب 10000ل.س',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(color: AppTheme.primaryColor),
                ),
              ),
            ),
            SizedBox(height: 24.h),

            // حقل معرف التلغرام
            Text(
              'معرف التلغرام',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8.h),
            TextField(
              controller: _telegramController,
              textDirection: TextDirection.ltr,
              decoration: InputDecoration(
                hintText: 'Smart_Test1',
                prefixText: '@',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(color: AppTheme.primaryColor),
                ),
              ),
            ),
            SizedBox(height: 16.h),

            // حقل رقم الواتساب
            Text(
              'رقم الواتساب',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8.h),
            TextField(
              controller: _whatsappController,
              textDirection: TextDirection.ltr,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                hintText: '963123456789',
                prefixText: '+',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(color: AppTheme.primaryColor),
                ),
              ),
            ),
            SizedBox(height: 16.h),
            if (service.error != null)
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: AppTheme.errorColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: AppTheme.errorColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: AppTheme.errorColor,
                      size: 20.sp,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        service.error!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: service.isLoading ? null : _saveMessage,
                    icon: service.isLoading
                        ? SizedBox(
                            width: 16.w,
                            height: 16.h,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          )
                        : const Icon(Icons.save),
                    label: Text(
                      service.isLoading ? 'جاري الحفظ...' : 'حفظ الرسالة',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ),
                if (service.hasMessage) ...[
                  SizedBox(width: 12.w),
                  ElevatedButton.icon(
                    onPressed: service.isLoading ? null : _deleteMessage,
                    icon: const Icon(Icons.delete),
                    label: const Text('حذف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.errorColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewCard(PricingMessageService service) {
    if (!service.hasMessage && _messageController.text.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    final previewText = _messageController.text.trim().isNotEmpty
        ? _messageController.text.trim()
        : service.currentMessage?.message ?? '';

    if (previewText.isEmpty) return const SizedBox.shrink();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.preview, color: AppTheme.primaryColor, size: 24.sp),
                SizedBox(width: 12.w),
                Text(
                  'معاينة الرسالة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                previewText,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(height: 1.5),
                textDirection: TextDirection.rtl,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال رسالة الأسعار'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final telegramUsername = _telegramController.text.trim();
    final whatsappNumber = _whatsappController.text.trim();

    final success = await PricingMessageService.instance.saveMessage(
      message,
      telegramUsername: telegramUsername,
      whatsappNumber: whatsappNumber,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ رسالة الأسعار بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _deleteMessage() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف رسالة الأسعار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await PricingMessageService.instance.deleteMessage();
      if (success && mounted) {
        _messageController.clear();
        _telegramController.clear();
        _whatsappController.clear();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف رسالة الأسعار بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }
}
