import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Animated<PERSON>ogo extends StatefulWidget {
  final double size;
  final bool animate;

  const AnimatedLogo({super.key, this.size = 100, this.animate = true});

  @override
  State<AnimatedLogo> createState() => _AnimatedLogoState();
}

class _AnimatedLogoState extends State<AnimatedLogo>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotateController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotateAnimation;

  @override
  void initState() {
    super.initState();
    if (widget.animate) {
      _initializeAnimations();
    }
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _rotateController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _rotateAnimation = Tween<double>(begin: 0, end: 0.1).animate(
      CurvedAnimation(parent: _rotateController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
    _rotateController.repeat(reverse: true);
  }

  @override
  void dispose() {
    if (widget.animate) {
      _pulseController.dispose();
      _rotateController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.animate) {
      return _buildLogo();
    }

    return AnimatedBuilder(
      animation: Listenable.merge([_pulseController, _rotateController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Transform.rotate(
            angle: _rotateAnimation.value,
            child: _buildLogo(),
          ),
        );
      },
    );
  }

  Widget _buildLogo() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4facfe), Color(0xFF00f2fe)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: const Color(0xFF4facfe).withValues(alpha: 0.3),
            blurRadius: 30,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Inner circle
          Container(
            width: widget.size * 0.8,
            height: widget.size * 0.8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.2),
            ),
          ),

          // Book icon (larger)
          Container(
            width: widget.size * 0.7,
            height: widget.size * 0.55,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Book lines
                Positioned(
                  top: widget.size * 0.08,
                  left: widget.size * 0.08,
                  right: widget.size * 0.08,
                  child: Column(
                    children: [
                      Container(
                        height: 2,
                        decoration: BoxDecoration(
                          color: const Color(0xFF667eea),
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                      SizedBox(height: widget.size * 0.04),
                      Container(
                        height: 2,
                        width: widget.size * 0.25,
                        decoration: BoxDecoration(
                          color: const Color(0xFF764ba2),
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                      SizedBox(height: widget.size * 0.04),
                      Container(
                        height: 2,
                        width: widget.size * 0.3,
                        decoration: BoxDecoration(
                          color: const Color(0xFF667eea),
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                    ],
                  ),
                ),

                // Checkmark (larger)
                Positioned(
                  top: widget.size * 0.08,
                  right: widget.size * 0.08,
                  child: Container(
                    width: widget.size * 0.18,
                    height: widget.size * 0.18,
                    decoration: const BoxDecoration(
                      color: Color(0xFF4facfe),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      color: Colors.white,
                      size: widget.size * 0.12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Brain/Smart indicator
          Positioned(
            top: widget.size * 0.15,
            left: widget.size * 0.15,
            child: Container(
              width: widget.size * 0.15,
              height: widget.size * 0.15,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                Icons.psychology,
                color: const Color(0xFF4facfe),
                size: widget.size * 0.1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
