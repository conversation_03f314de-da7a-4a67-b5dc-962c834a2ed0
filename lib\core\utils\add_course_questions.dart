import 'package:cloud_firestore/cloud_firestore.dart';
import '../../shared/models/question_model.dart';
import '../../shared/services/exam_service.dart';
import 'logger.dart';

/// دالة لإضافة أسئلة دورات تجريبية للمادة الحالية
class CourseQuestionsHelper {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إضافة أسئلة دورات للمادة المحددة
  static Future<void> addCourseQuestionsForSubject(String subjectId) async {
    try {
      Logger.start('🎓 بدء إضافة أسئلة الدورات للمادة: $subjectId');

      final courseQuestions = [
        Question(
          id: '${subjectId}_course_q1',
          subjectId: subjectId,
          unitId: '1750742919890', // نفس الوحدة الموجودة
          lessonId: '1750742932595', // نفس الدرس الموجود
          isCourseQuestion: true, // هذا هو المهم!
          questionText: 'سؤال دورة تجريبي رقم 1 - ما هو أفضل طريقة للتعلم؟',
          type: QuestionType.multipleChoice,
          difficulty: DifficultyLevel.medium,
          contentType: QuestionContentType.text,
          options: [
            'القراءة فقط',
            'الممارسة والتطبيق',
            'المشاهدة فقط',
            'الحفظ فقط',
          ],
          correctAnswers: ['الممارسة والتطبيق'],
          explanation:
              'أفضل طريقة للتعلم هي الممارسة والتطبيق العملي للمعلومات',
          points: 2,
          imageUrl: '',
          metadata: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
          isActive: true,
        ),
        Question(
          id: '${subjectId}_course_q2',
          subjectId: subjectId,
          unitId: '1750742919890',
          lessonId: '1750742932595',
          isCourseQuestion: true,
          questionText:
              'سؤال دورة تجريبي رقم 2 - كم عدد ساعات الدراسة المثلى يومياً؟',
          type: QuestionType.multipleChoice,
          difficulty: DifficultyLevel.easy,
          contentType: QuestionContentType.text,
          options: ['ساعة واحدة', '2-4 ساعات', '8 ساعات', '12 ساعة'],
          correctAnswers: ['2-4 ساعات'],
          explanation:
              'الدراسة لمدة 2-4 ساعات يومياً مع فترات راحة هي الأمثل للتركيز',
          points: 1,
          imageUrl: '',
          metadata: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
          isActive: true,
        ),
        Question(
          id: '${subjectId}_course_q3',
          subjectId: subjectId,
          unitId: '1750742919890',
          lessonId: '1750742932595',
          isCourseQuestion: true,
          questionText: 'سؤال دورة تجريبي رقم 3 - ما هي أهمية المراجعة؟',
          type: QuestionType.trueFalse,
          difficulty: DifficultyLevel.easy,
          contentType: QuestionContentType.text,
          options: ['صح', 'خطأ'],
          correctAnswers: ['صح'],
          explanation:
              'المراجعة مهمة جداً لتثبيت المعلومات في الذاكرة طويلة المدى',
          points: 1,
          imageUrl: '',
          metadata: {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdByAdminId: 'admin1',
          isActive: true,
        ),
      ];

      // إضافة الأسئلة إلى Firebase
      for (final question in courseQuestions) {
        await ExamService.instance.createQuestion(question);
        Logger.success(
          'تم إضافة سؤال دورة: ${question.questionText.substring(0, 30)}...',
        );
      }

      Logger.success('✅ تم إضافة ${courseQuestions.length} سؤال دورة بنجاح!');
    } catch (e) {
      Logger.error('❌ خطأ في إضافة أسئلة الدورات', e);
    }
  }

  /// إضافة أسئلة دورات لجميع المواد الموجودة
  static Future<void> addCourseQuestionsForAllSubjects() async {
    try {
      Logger.start('🎓 بدء إضافة أسئلة الدورات لجميع المواد');

      // الحصول على جميع المواد
      final subjectsSnapshot = await _firestore.collection('subjects').get();

      for (final doc in subjectsSnapshot.docs) {
        final subjectId = doc.id;
        final subjectName = doc.data()['name'] ?? 'غير محدد';

        Logger.info('إضافة أسئلة دورات للمادة: $subjectName');
        await addCourseQuestionsForSubject(subjectId);

        // انتظار قصير بين المواد
        await Future.delayed(const Duration(milliseconds: 500));
      }

      Logger.success('🎉 تم إضافة أسئلة الدورات لجميع المواد بنجاح!');
    } catch (e) {
      Logger.error('❌ خطأ في إضافة أسئلة الدورات لجميع المواد', e);
    }
  }

  /// إضافة أسئلة دورات للمادة الحالية مباشرة (للاختبار)
  static Future<void> addCourseQuestionsForCurrentSubject() async {
    try {
      Logger.start('🎓 بدء إضافة أسئلة الدورات للمادة الحالية');

      // البحث عن المادة "عيون العيون"
      final subjectsSnapshot = await _firestore
          .collection('subjects')
          .where('name', isEqualTo: 'عيون العيون')
          .get();

      if (subjectsSnapshot.docs.isEmpty) {
        Logger.error('❌ لم يتم العثور على المادة "عيون العيون"', null);
        return;
      }

      final subjectId = subjectsSnapshot.docs.first.id;
      Logger.info('تم العثور على المادة: $subjectId');

      // إضافة أسئلة الدورات
      await addCourseQuestionsForSubject(subjectId);

      Logger.success('✅ تم إضافة أسئلة الدورات للمادة الحالية بنجاح!');
    } catch (e) {
      Logger.error('❌ خطأ في إضافة أسئلة الدورات للمادة الحالية', e);
    }
  }

  /// تصحيح subjectId لأسئلة الدورات الموجودة
  static Future<void> fixCourseQuestionsSubjectId() async {
    try {
      Logger.start('🔧 بدء تصحيح معرفات المواد لأسئلة الدورات');

      // البحث عن المادة "عيون العيون"
      final subjectsSnapshot = await _firestore
          .collection('subjects')
          .where('name', isEqualTo: 'عيون العيون')
          .get();

      if (subjectsSnapshot.docs.isEmpty) {
        Logger.error('❌ لم يتم العثور على المادة "عيون العيون"', null);
        return;
      }

      final correctSubjectId = subjectsSnapshot.docs.first.id;
      Logger.info('معرف المادة الصحيح: $correctSubjectId');

      // البحث عن جميع أسئلة الدورات
      final courseQuestionsSnapshot = await _firestore
          .collection('questions')
          .where('isCourseQuestion', isEqualTo: true)
          .get();

      Logger.info(
        'تم العثور على ${courseQuestionsSnapshot.docs.length} سؤال دورة',
      );

      int updatedCount = 0;
      for (final doc in courseQuestionsSnapshot.docs) {
        final data = doc.data();
        final currentSubjectId = data['subjectId'] as String?;

        Logger.info('السؤال ${doc.id}: subjectId حالي = $currentSubjectId');

        // تحديث subjectId إذا كان مختلفاً
        if (currentSubjectId != correctSubjectId) {
          await doc.reference.update({'subjectId': correctSubjectId});
          updatedCount++;
          Logger.info('✅ تم تحديث السؤال ${doc.id}');
        }
      }

      Logger.success('🎉 تم تصحيح $updatedCount سؤال دورة بنجاح!');
    } catch (e) {
      Logger.error('❌ خطأ في تصحيح معرفات المواد', e);
    }
  }
}
