/// إعدادات Cloudinary
class CloudinaryConfig {
  // إعدادات Cloudinary - يجب تحديثها بالقيم الحقيقية
  static const String cloudName = 'dpkbtagqp'; // من Cloudinary Dashboard
  static const String uploadPreset =
      'smart_test_preset'; // سيتم إنشاؤه في Cloudinary

  // التحقق من صحة الإعدادات
  static bool get isConfigured =>
      cloudName.isNotEmpty &&
      cloudName != 'YOUR_CLOUD_NAME' &&
      uploadPreset.isNotEmpty &&
      uploadPreset != 'YOUR_UPLOAD_PRESET';

  // رسالة الخطأ في حالة عدم الإعداد
  static String get configurationError =>
      '''
إعدادات Cloudinary غير مكتملة!

Cloud Name: $cloudName
Upload Preset: $uploadPreset

تأكد من أن Upload Preset تم إنشاؤه في Cloudinary Dashboard:
1. اذهب إلى Settings > Upload
2. أنشئ Upload Preset جديد باسم: smart_test_preset
3. اختر Signing Mode: Unsigned
''';
}
