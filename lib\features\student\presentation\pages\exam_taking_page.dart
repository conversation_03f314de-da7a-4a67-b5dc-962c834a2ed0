import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/exam_model.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/models/exam_result_model.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/device_service.dart';
import 'package:firebase_auth/firebase_auth.dart';

class ExamTakingPage extends StatefulWidget {
  final Exam exam;

  const ExamTakingPage({super.key, required this.exam});

  @override
  State<ExamTakingPage> createState() => _ExamTakingPageState();
}

class _ExamTakingPageState extends State<ExamTakingPage> {
  List<Question> _questions = [];
  int _currentQuestionIndex = 0;
  final Map<String, List<String>> _answers = {};
  Timer? _timer;
  int _remainingSeconds = 0;
  bool _isLoading = true;
  bool _isSubmitting = false;
  String? _examResultId;

  @override
  void initState() {
    super.initState();
    _initializeExam();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _initializeExam() async {
    try {
      // Load questions
      final questions = await ExamService.instance.getExamQuestions(
        widget.exam,
      );

      if (questions.isEmpty) {
        _showErrorAndExit('لا توجد أسئلة في هذا الاختبار');
        return;
      }

      // Shuffle questions if needed
      if (widget.exam.shuffleQuestions) {
        questions.shuffle();
      }

      // Start exam session
      final user = FirebaseAuth.instance.currentUser;
      final deviceId = await DeviceService.instance.getDeviceId();

      if (user == null) {
        _showErrorAndExit('يجب تسجيل الدخول أولاً');
        return;
      }

      final resultId = await ExamService.instance.startExam(
        widget.exam.id,
        user.uid,
        deviceId,
      );

      setState(() {
        _questions = questions;
        _examResultId = resultId;
        _remainingSeconds = widget.exam.duration * 60;
        _isLoading = false;
      });

      _startTimer();
    } catch (e) {
      _showErrorAndExit('حدث خطأ في تحميل الاختبار: $e');
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      } else {
        _submitExam();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppTheme.getBackgroundColor(context),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldPop = await _showExitConfirmation();
          if (shouldPop && mounted) {
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          }
        }
      },
      child: Scaffold(
        backgroundColor: AppTheme.getBackgroundColor(context),
        appBar: AppBar(
          title: Text(
            widget.exam.title,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: AppTheme.getPrimaryGradient(context),
            ),
          ),
          foregroundColor: Colors.white,
          automaticallyImplyLeading: false,
          actions: [
            _buildTimerWidget(),
            SizedBox(width: 16.w),
          ],
        ),
        body: Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(),

            // Question content
            Expanded(child: _buildQuestionContent()),

            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildTimerWidget() {
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;
    final isLowTime = _remainingSeconds < 300; // Less than 5 minutes

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: isLowTime
            ? AppTheme.errorColor
            : Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.timer, size: 16.sp, color: Colors.white),
          SizedBox(width: 4.w),
          Text(
            '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final progress = (_currentQuestionIndex + 1) / _questions.length;

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السؤال ${_currentQuestionIndex + 1} من ${_questions.length}',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              Text(
                '${(_answers.length / _questions.length * 100).toInt()}% مكتمل',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppTheme.dividerColor,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionContent() {
    final question = _questions[_currentQuestionIndex];

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Question header
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor(
                        question.difficulty,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      _getQuestionTypeIcon(question.type),
                      color: _getDifficultyColor(question.difficulty),
                      size: 20.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          question.typeDisplayName,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                        ),
                        Text(
                          '${question.points} نقطة',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor(
                        question.difficulty,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      question.difficultyDisplayName,
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w600,
                        color: _getDifficultyColor(question.difficulty),
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 20.h),

              // Question text
              Text(
                question.questionText,
                textDirection: TextDirection.rtl, // من اليمين لليسار
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  height: 1.5,
                ),
              ),

              SizedBox(height: 24.h),

              // Answer options
              _buildAnswerOptions(question),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnswerOptions(Question question) {
    final options = widget.exam.shuffleOptions
        ? (List<String>.from(question.options)..shuffle())
        : question.options;

    switch (question.type) {
      case QuestionType.multipleChoice:
      case QuestionType.trueFalse:
        return _buildSingleChoiceOptions(question, options);
      default:
        return Text('نوع السؤال غير مدعوم حالياً');
    }
  }

  Widget _buildSingleChoiceOptions(Question question, List<String> options) {
    final selectedAnswer = _answers[question.id]?.first;

    return Column(
      children: options.map((option) {
        final isSelected = selectedAnswer == option;

        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: InkWell(
            onTap: () {
              setState(() {
                _answers[question.id] = [option];
              });
            },
            borderRadius: BorderRadius.circular(12.r),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.primaryColor.withValues(alpha: 0.1)
                    : Colors.transparent,
                border: Border.all(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : AppTheme.dividerColor,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                children: [
                  Container(
                    width: 20.w,
                    height: 20.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected
                            ? AppTheme.primaryColor
                            : AppTheme.dividerColor,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? Icon(Icons.check, size: 12.sp, color: Colors.white)
                        : null,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      option,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: isSelected
                            ? AppTheme.primaryColor
                            : AppTheme.textPrimaryColor,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          // Previous button
          if (_currentQuestionIndex > 0)
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    _currentQuestionIndex--;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.textSecondaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: Size(0, 48.h),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.arrow_back, size: 16.sp),
                    SizedBox(width: 8.w),
                    Text('السابق'),
                  ],
                ),
              ),
            ),

          if (_currentQuestionIndex > 0) SizedBox(width: 16.w),

          // Next/Submit button
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isSubmitting
                  ? null
                  : () {
                      if (_currentQuestionIndex < _questions.length - 1) {
                        setState(() {
                          _currentQuestionIndex++;
                        });
                      } else {
                        _submitExam();
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                minimumSize: Size(0, 48.h),
              ),
              child: _isSubmitting
                  ? SizedBox(
                      width: 20.w,
                      height: 20.h,
                      child: const CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _currentQuestionIndex < _questions.length - 1
                              ? 'التالي'
                              : 'إنهاء الاختبار',
                        ),
                        SizedBox(width: 8.w),
                        Icon(
                          _currentQuestionIndex < _questions.length - 1
                              ? Icons.arrow_forward
                              : Icons.check,
                          size: 16.sp,
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return AppTheme.successColor;
      case DifficultyLevel.medium:
        return AppTheme.warningColor;
      case DifficultyLevel.hard:
        return AppTheme.errorColor;
    }
  }

  IconData _getQuestionTypeIcon(QuestionType type) {
    switch (type) {
      case QuestionType.multipleChoice:
        return Icons.radio_button_checked;
      case QuestionType.trueFalse:
        return Icons.check_box;
      case QuestionType.shortAnswer:
        return Icons.short_text;
      case QuestionType.essay:
        return Icons.article;
      case QuestionType.matching:
        return Icons.compare_arrows;
      case QuestionType.fillInTheBlank:
        return Icons.edit;
    }
  }

  Future<bool> _showExitConfirmation() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الخروج'),
        content: const Text('هل أنت متأكد من الخروج؟ سيتم فقدان إجاباتك.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('خروج'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  Future<void> _submitExam() async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      _timer?.cancel();

      // Calculate results
      final questionAnswers = <QuestionAnswer>[];
      int earnedPoints = 0;

      for (final question in _questions) {
        final userAnswers = _answers[question.id] ?? [];
        final isCorrect = question.isCorrectAnswers(userAnswers);
        final points = isCorrect ? question.points : 0;

        questionAnswers.add(
          QuestionAnswer(
            questionId: question.id,
            selectedAnswers: userAnswers,
            isCorrect: isCorrect,
            pointsEarned: points,
            answeredAt: DateTime.now(),
          ),
        );

        earnedPoints += points;
      }

      final percentage = (earnedPoints / widget.exam.totalPoints) * 100;
      final isPassed = percentage >= widget.exam.passingScore;

      final result = ExamResult(
        id: _examResultId!,
        examId: widget.exam.id,
        userId: FirebaseAuth.instance.currentUser!.uid,
        deviceId: await DeviceService.instance.getDeviceId(),
        answers: questionAnswers,
        totalPoints: widget.exam.totalPoints,
        earnedPoints: earnedPoints,
        percentage: percentage,
        isPassed: isPassed,
        status: ResultStatus.completed,
        startedAt: DateTime.now().subtract(
          Duration(seconds: widget.exam.duration * 60 - _remainingSeconds),
        ),
        completedAt: DateTime.now(),
        submittedAt: DateTime.now(),
        timeSpent: widget.exam.duration * 60 - _remainingSeconds,
        attemptNumber: 1,
        metadata: {},
      );

      await ExamService.instance.submitExamResult(result);

      // Navigate to results
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) =>
                ExamResultPage(result: result, exam: widget.exam),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في إرسال النتائج: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  void _showErrorAndExit(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}

// Placeholder for ExamResultPage
class ExamResultPage extends StatelessWidget {
  final ExamResult result;
  final Exam exam;

  const ExamResultPage({super.key, required this.result, required this.exam});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('نتيجة الاختبار')),
      body: Center(child: Text('النتيجة: ${result.percentage.toInt()}%')),
    );
  }
}
