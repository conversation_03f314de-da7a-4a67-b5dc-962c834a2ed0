# نظام الثيمات (الوضع الليلي والنهاري) - Smart Edu

## 📋 نظرة عامة

تم إضافة نظام شامل للثيمات يدعم الوضع الليلي والنهاري مع إمكانية التبديل التلقائي حسب إعدادات النظام.

## 🎨 المميزات

### ✅ الأوضاع المتاحة
- **الوضع التلقائي**: يتبع إعدادات النظام تلقائياً
- **الوضع الفاتح**: مناسب للاستخدام في النهار
- **الوضع المظلم**: مناسب للاستخدام في الليل ويوفر البطارية

### ✅ الحفظ التلقائي
- يتم حفظ اختيار المستخدم في `SharedPreferences`
- يتم استرجاع الثيم المحفوظ عند إعادة تشغيل التطبيق

### ✅ التطبيق الشامل
- يعمل في تطبيق الطالب وتطبيق الإدارة
- يؤثر على جميع الصفحات والمكونات

## 🛠️ الملفات المضافة

### خدمة الثيمات
- `lib/shared/services/theme_service.dart` - الخدمة الرئيسية لإدارة الثيمات

### ويدجت الثيمات
- `lib/shared/widgets/theme_toggle_widget.dart` - مكونات تبديل الثيم
- `lib/shared/pages/theme_settings_page.dart` - صفحة إعدادات الثيم

### الثيمات
- `lib/core/theme/app_theme.dart` - تم تحديثه لإضافة الثيم المظلم

## 🎯 كيفية الاستخدام

### 1. تبديل الثيم السريع
```dart
// في أي مكان في التطبيق
const AppBarThemeToggle()
```

### 2. عرض إعدادات الثيم
```dart
// الانتقال لصفحة الإعدادات
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const ThemeSettingsPage(),
  ),
);
```

### 3. التحكم البرمجي
```dart
// الحصول على خدمة الثيمات
final themeService = Provider.of<ThemeService>(context, listen: false);

// تبديل الثيم
await themeService.toggleTheme();

// تعيين ثيم محدد
await themeService.setThemeMode(ThemeMode.dark);

// التحقق من الوضع الحالي
bool isDark = themeService.isDarkMode;
```

## 🎨 الألوان المستخدمة

### الوضع الفاتح
- **اللون الأساسي**: `#6C5CE7` (بنفسجي)
- **اللون الثانوي**: `#00CEC9` (تركوازي)
- **الخلفية**: `#F8F9FA` (رمادي فاتح)
- **السطح**: `#FFFFFF` (أبيض)
- **النص الأساسي**: `#2D3436` (رمادي داكن)

### الوضع المظلم
- **اللون الأساسي**: `#8B7CF6` (بنفسجي فاتح)
- **اللون الثانوي**: `#34D399` (تركوازي فاتح)
- **الخلفية**: `#0F172A` (أزرق داكن جداً)
- **السطح**: `#1E293B` (أزرق رمادي داكن)
- **النص الأساسي**: `#F1F5F9` (رمادي فاتح جداً)

## 🔧 التخصيص

### إضافة ألوان جديدة
```dart
// في app_theme.dart
static const Color customColor = Color(0xFF123456);
static const Color darkCustomColor = Color(0xFF654321);
```

### إنشاء ويدجت مخصص للثيم
```dart
class CustomThemeWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return Container(
          color: themeService.isDarkMode 
              ? Colors.black 
              : Colors.white,
          child: Text('مرحبا'),
        );
      },
    );
  }
}
```

## 📱 الواجهات المحدثة

### تطبيق الطالب
- **الصفحة الرئيسية**: أزرار تبديل الثيم والإعدادات في AppBar
- **جميع الصفحات**: تطبيق الثيم تلقائياً

### تطبيق الإدارة
- **الصفحة الرئيسية**: أزرار تبديل الثيم والإعدادات في AppBar
- **جميع الصفحات**: تطبيق الثيم تلقائياً

## 🚀 المميزات المتقدمة

### 1. التدرجات الديناميكية
- تدرجات مختلفة للوضع الفاتح والمظلم
- تطبيق تلقائي حسب الثيم الحالي

### 2. الأيقونات التفاعلية
- أيقونات مختلفة لكل وضع ثيم
- تغيير تلقائي عند التبديل

### 3. معاينة الألوان
- عرض الألوان الحالية في صفحة الإعدادات
- معاينة فورية للتغييرات

## 🔍 استكشاف الأخطاء

### المشكلة: الثيم لا يتغير
**الحل**: تأكد من أن `ThemeService` مضاف في `MultiProvider`

### المشكلة: الألوان لا تظهر بشكل صحيح
**الحل**: تأكد من استخدام `Theme.of(context)` بدلاً من الألوان المباشرة

### المشكلة: الثيم لا يُحفظ
**الحل**: تأكد من أن `SharedPreferences` يعمل بشكل صحيح

## 📝 ملاحظات مهمة

1. **الأداء**: خدمة الثيمات محسنة للأداء ولا تؤثر على سرعة التطبيق
2. **التوافق**: يعمل على جميع المنصات (Android, iOS, Windows)
3. **الذاكرة**: يتم حفظ الثيم في الذاكرة المحلية فقط
4. **الأمان**: لا يتم إرسال بيانات الثيم للخادم

## 🎯 الخطوات التالية

- [ ] إضافة المزيد من الثيمات (مثل الثيم الأزرق، الأخضر، إلخ)
- [ ] إضافة إعدادات متقدمة للألوان
- [ ] دعم الثيمات المخصصة من المستخدم
- [ ] إضافة انتقالات متحركة عند تغيير الثيم

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2025-01-20  
**الإصدار**: 1.0.0
