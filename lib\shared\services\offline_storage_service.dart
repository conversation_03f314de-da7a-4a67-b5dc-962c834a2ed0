import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../core/utils/logger.dart';
import '../models/question_model.dart';
import '../models/subject_model.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';
import '../models/user_subscription_model.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import '../../core/models/section.dart';

/// خدمة التخزين المحلي للأسئلة والمحتوى
class OfflineStorageService {
  static final OfflineStorageService _instance =
      OfflineStorageService._internal();
  static OfflineStorageService get instance => _instance;
  OfflineStorageService._internal();

  static const String _questionsKey = 'offline_questions';
  static const String _subjectsKey = 'offline_subjects';
  static const String _unitsKey = 'offline_units';
  static const String _lessonsKey = 'offline_lessons';
  static const String _videoSectionsKey = 'offline_video_sections';
  static const String _videoSubjectsKey = 'offline_video_subjects';
  static const String _videoUnitsKey = 'offline_video_units';
  static const String _videoLessonsKey = 'offline_video_lessons';
  static const String _videosKey = 'offline_videos';
  static const String _downloadedSubjectsKey = 'downloaded_subjects';
  static const String _lastSyncKey = 'last_sync_time';
  static const String _userSubscriptionKey = 'user_subscription';
  static const String _subscribedSubjectsKey = 'subscribed_subjects';
  static const String _sectionsKey = 'offline_sections';

  /// حفظ الأسئلة محلياً
  Future<void> saveQuestions(List<Question> questions) async {
    final prefs = await SharedPreferences.getInstance();
    final questionsJson = questions
        .map(
          (q) => {
            'id': q.id,
            'questionText': q.questionText,
            'options': q.options,
            'correctAnswers': q.correctAnswers,
            'explanation': q.explanation,
            'subjectId': q.subjectId,
            'unitId': q.unitId,
            'lessonId': q.lessonId,
            'difficulty': q.difficulty.toString().split('.').last,
            'type': q.type.toString().split('.').last,
            'contentType': q.contentType.toString().split('.').last,
            'points': q.points,
            'imageUrl': q.imageUrl,
            'localImagePath': q.localImagePath,
            'metadata': q.metadata,
            'createdByAdminId': q.createdByAdminId,
            'isActive': q.isActive,
            'isCourseQuestion': q.isCourseQuestion,
            'createdAt': q.createdAt.toIso8601String(),
            'updatedAt': q.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_questionsKey, jsonEncode(questionsJson));
  }

  /// تحميل الأسئلة المحفوظة محلياً
  Future<List<Question>> getOfflineQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final questionsJson = prefs.getString(_questionsKey);
    if (questionsJson == null) return [];

    try {
      final questionsList = jsonDecode(questionsJson) as List;
      return questionsList
          .map(
            (q) => Question(
              id: q['id'],
              questionText: q['questionText'],
              options: List<String>.from(q['options']),
              correctAnswers: List<String>.from(q['correctAnswers']),
              explanation: q['explanation'],
              subjectId: q['subjectId'],
              unitId: q['unitId'],
              lessonId: q['lessonId'],
              difficulty: DifficultyLevel.values.firstWhere(
                (d) => d.toString().split('.').last == q['difficulty'],
                orElse: () => DifficultyLevel.medium,
              ),
              type: QuestionType.values.firstWhere(
                (t) => t.toString().split('.').last == q['type'],
                orElse: () => QuestionType.multipleChoice,
              ),
              contentType: QuestionContentType.values.firstWhere(
                (c) => c.toString().split('.').last == q['contentType'],
                orElse: () => QuestionContentType.text,
              ),
              points: q['points'] ?? 1,
              imageUrl: q['imageUrl'] ?? '',
              localImagePath: q['localImagePath'],
              metadata: Map<String, dynamic>.from(q['metadata'] ?? {}),
              createdByAdminId: q['createdByAdminId'] ?? '',
              isActive: q['isActive'] ?? true,
              isCourseQuestion: q['isCourseQuestion'] ?? false,
              createdAt: DateTime.parse(q['createdAt']),
              updatedAt: DateTime.parse(q['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      Logger.error('خطأ في تحميل الأسئلة المحفوظة', e);
      return [];
    }
  }

  /// حفظ المواد محلياً
  Future<void> saveSubjects(List<Subject> subjects) async {
    final prefs = await SharedPreferences.getInstance();
    final subjectsJson = subjects
        .map(
          (s) => {
            'id': s.id,
            'name': s.name,
            'description': s.description,
            'iconUrl': s.iconUrl,
            'color': s.color,
            'isActive': s.isActive,
            'createdAt': s.createdAt.toIso8601String(),
            'updatedAt': s.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_subjectsKey, jsonEncode(subjectsJson));
  }

  /// تحميل المواد المحفوظة محلياً
  Future<List<Subject>> getOfflineSubjects() async {
    final prefs = await SharedPreferences.getInstance();
    final subjectsJson = prefs.getString(_subjectsKey);
    if (subjectsJson == null) return [];

    try {
      final subjectsList = jsonDecode(subjectsJson) as List;
      return subjectsList
          .map(
            (s) => Subject(
              id: s['id'],
              name: s['name'],
              description: s['description'],
              iconUrl: s['iconUrl'] ?? '',
              color: s['color'],
              isActive: s['isActive'] ?? true,
              createdAt: DateTime.parse(s['createdAt']),
              updatedAt: DateTime.parse(s['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// حفظ الوحدات محلياً
  Future<void> saveUnits(List<Unit> units) async {
    final prefs = await SharedPreferences.getInstance();
    final unitsJson = units
        .map(
          (u) => {
            'id': u.id,
            'name': u.name,
            'description': u.description,
            'subjectId': u.subjectId,
            'order': u.order,
            'iconUrl': u.iconUrl,
            'color': u.color,
            'isActive': u.isActive,
            'createdByAdminId': u.createdByAdminId,
            'createdAt': u.createdAt.toIso8601String(),
            'updatedAt': u.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_unitsKey, jsonEncode(unitsJson));
  }

  /// تحميل الوحدات المحفوظة محلياً
  Future<List<Unit>> getOfflineUnits() async {
    final prefs = await SharedPreferences.getInstance();
    final unitsJson = prefs.getString(_unitsKey);
    if (unitsJson == null) return [];

    try {
      final unitsList = jsonDecode(unitsJson) as List;
      return unitsList
          .map(
            (u) => Unit(
              id: u['id'],
              name: u['name'],
              description: u['description'],
              subjectId: u['subjectId'],
              order: u['order'] ?? 0,
              iconUrl: u['iconUrl'] ?? '',
              color: u['color'] ?? '',
              isActive: u['isActive'] ?? true,
              createdByAdminId: u['createdByAdminId'] ?? '',
              createdAt: DateTime.parse(u['createdAt']),
              updatedAt: DateTime.parse(u['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// حفظ الدروس محلياً
  Future<void> saveLessons(List<Lesson> lessons) async {
    final prefs = await SharedPreferences.getInstance();
    final lessonsJson = lessons
        .map(
          (l) => {
            'id': l.id,
            'unitId': l.unitId,
            'subjectId': l.subjectId,
            'name': l.name,
            'description': l.description,
            'order': l.order,
            'videoUrl': l.videoUrl,
            'contentUrl': l.contentUrl,
            'iconUrl': l.iconUrl,
            'isActive': l.isActive,
            'createdByAdminId': l.createdByAdminId,
            'createdAt': l.createdAt.toIso8601String(),
            'updatedAt': l.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_lessonsKey, jsonEncode(lessonsJson));
  }

  /// تحميل الدروس المحفوظة محلياً
  Future<List<Lesson>> getOfflineLessons() async {
    final prefs = await SharedPreferences.getInstance();
    final lessonsJson = prefs.getString(_lessonsKey);
    if (lessonsJson == null) return [];

    try {
      final lessonsList = jsonDecode(lessonsJson) as List;
      return lessonsList
          .map(
            (l) => Lesson(
              id: l['id'],
              unitId: l['unitId'],
              subjectId: l['subjectId'],
              name: l['name'],
              description: l['description'],
              order: l['order'] ?? 0,
              videoUrl: l['videoUrl'] ?? '',
              contentUrl: l['contentUrl'] ?? '',
              iconUrl: l['iconUrl'] ?? '',
              isActive: l['isActive'] ?? true,
              createdByAdminId: l['createdByAdminId'] ?? '',
              createdAt: DateTime.parse(l['createdAt']),
              updatedAt: DateTime.parse(l['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// إضافة مادة للمواد المحملة
  Future<void> addDownloadedSubject(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    final downloadedSubjects = await getDownloadedSubjects();
    if (!downloadedSubjects.contains(subjectId)) {
      downloadedSubjects.add(subjectId);
      await prefs.setStringList(_downloadedSubjectsKey, downloadedSubjects);
    }
  }

  /// إزالة مادة من المواد المحملة
  Future<void> removeDownloadedSubject(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    final downloadedSubjects = await getDownloadedSubjects();
    downloadedSubjects.remove(subjectId);
    await prefs.setStringList(_downloadedSubjectsKey, downloadedSubjects);
  }

  /// الحصول على قائمة المواد المحملة
  Future<List<String>> getDownloadedSubjects() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_downloadedSubjectsKey) ?? [];
  }

  /// فحص ما إذا كانت المادة محملة
  Future<bool> isSubjectDownloaded(String subjectId) async {
    final downloadedSubjects = await getDownloadedSubjects();
    return downloadedSubjects.contains(subjectId);
  }

  /// تحديث وقت آخر مزامنة
  Future<void> updateLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// الحصول على وقت آخر مزامنة
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt(_lastSyncKey);
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  /// حفظ اشتراك المستخدم محلياً
  Future<void> saveUserSubscription(UserSubscription subscription) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      _userSubscriptionKey,
      jsonEncode(subscription.toMap()),
    );
  }

  /// تحميل اشتراك المستخدم المحفوظ محلياً
  Future<UserSubscription?> getOfflineUserSubscription() async {
    final prefs = await SharedPreferences.getInstance();
    final subscriptionJson = prefs.getString(_userSubscriptionKey);
    if (subscriptionJson == null) return null;

    try {
      final subscriptionMap =
          jsonDecode(subscriptionJson) as Map<String, dynamic>;
      return UserSubscription.fromMap(subscriptionMap);
    } catch (e) {
      return null;
    }
  }

  /// حفظ المواد المشترك بها محلياً
  Future<void> saveSubscribedSubjects(List<Subject> subjects) async {
    final prefs = await SharedPreferences.getInstance();
    final subjectsJson = subjects
        .map(
          (s) => {
            'id': s.id,
            'name': s.name,
            'description': s.description,
            'iconUrl': s.iconUrl,
            'color': s.color,
            'isActive': s.isActive,
            'createdAt': s.createdAt.toIso8601String(),
            'updatedAt': s.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_subscribedSubjectsKey, jsonEncode(subjectsJson));
  }

  /// تحميل المواد المشترك بها المحفوظة محلياً
  Future<List<Subject>> getOfflineSubscribedSubjects() async {
    final prefs = await SharedPreferences.getInstance();
    final subjectsJson = prefs.getString(_subscribedSubjectsKey);
    if (subjectsJson == null) return [];

    try {
      final subjectsList = jsonDecode(subjectsJson) as List;
      return subjectsList
          .map(
            (s) => Subject(
              id: s['id'],
              name: s['name'],
              description: s['description'],
              iconUrl: s['iconUrl'] ?? '',
              color: s['color'],
              isActive: s['isActive'] ?? true,
              createdAt: DateTime.parse(s['createdAt']),
              updatedAt: DateTime.parse(s['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// مسح بيانات الاشتراك المحلية
  Future<void> clearSubscriptionData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userSubscriptionKey);
    await prefs.remove(_subscribedSubjectsKey);
  }

  /// تحميل أسئلة مادة معينة محلياً
  Future<List<Question>> getOfflineQuestionsBySubject(String subjectId) async {
    final allQuestions = await getOfflineQuestions();
    return allQuestions.where((q) => q.subjectId == subjectId).toList();
  }

  /// تحميل أسئلة وحدة معينة محلياً
  Future<List<Question>> getOfflineQuestionsByUnit(String unitId) async {
    final allQuestions = await getOfflineQuestions();
    return allQuestions.where((q) => q.unitId == unitId).toList();
  }

  /// تحميل أسئلة درس معين محلياً
  Future<List<Question>> getOfflineQuestionsByLesson(String lessonId) async {
    final allQuestions = await getOfflineQuestions();
    return allQuestions.where((q) => q.lessonId == lessonId).toList();
  }

  /// تحميل وحدات مادة معينة محلياً
  Future<List<Unit>> getOfflineUnitsBySubject(String subjectId) async {
    final allUnits = await getOfflineUnits();
    return allUnits.where((u) => u.subjectId == subjectId).toList();
  }

  /// تحميل دروس وحدة معينة محلياً
  Future<List<Lesson>> getOfflineLessonsByUnit(String unitId) async {
    final allLessons = await getOfflineLessons();
    return allLessons.where((l) => l.unitId == unitId).toList();
  }

  // ===== إدارة أقسام الفيديوهات =====

  /// حفظ أقسام الفيديوهات محلياً
  Future<void> saveVideoSections(List<VideoSection> sections) async {
    final prefs = await SharedPreferences.getInstance();
    final sectionsJson = sections
        .map(
          (s) => {
            'id': s.id,
            'name': s.name,
            'description': s.description,
            'order': s.order,
            'iconUrl': s.iconUrl,
            'color': s.color,
            'isFree': s.isFree,
            'isActive': s.isActive,
            'createdByAdminId': s.createdByAdminId,
            'createdAt': s.createdAt.toIso8601String(),
            'updatedAt': s.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_videoSectionsKey, jsonEncode(sectionsJson));
    debugPrint('💾 تم حفظ ${sections.length} قسم فيديو محلياً');
  }

  /// الحصول على أقسام الفيديوهات المحفوظة محلياً
  Future<List<VideoSection>> getOfflineVideoSections() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sectionsString = prefs.getString(_videoSectionsKey);
      if (sectionsString == null) return [];

      final sectionsJson = jsonDecode(sectionsString) as List;
      return sectionsJson
          .map(
            (json) => VideoSection(
              id: json['id'],
              name: json['name'],
              description: json['description'],
              order: json['order'],
              iconUrl: json['iconUrl'],
              color: json['color'],
              isFree: json['isFree'],
              isActive: json['isActive'],
              createdByAdminId: json['createdByAdminId'],
              createdAt: DateTime.parse(json['createdAt']),
              updatedAt: DateTime.parse(json['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل أقسام الفيديوهات المحلية: $e');
      return [];
    }
  }

  // ===== إدارة مواد الفيديوهات =====

  /// حفظ مواد الفيديوهات محلياً
  Future<void> saveVideoSubjects(List<VideoSubject> subjects) async {
    final prefs = await SharedPreferences.getInstance();
    final subjectsJson = subjects
        .map(
          (s) => {
            'id': s.id,
            'name': s.name,
            'description': s.description,
            'sectionId': s.sectionId,
            'order': s.order,
            'iconUrl': s.iconUrl,
            'color': s.color,
            'isActive': s.isActive,
            'createdByAdminId': s.createdByAdminId,
            'createdAt': s.createdAt.toIso8601String(),
            'updatedAt': s.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_videoSubjectsKey, jsonEncode(subjectsJson));
    debugPrint('💾 تم حفظ ${subjects.length} مادة فيديو محلياً');
  }

  /// الحصول على مواد الفيديوهات المحفوظة محلياً
  Future<List<VideoSubject>> getOfflineVideoSubjects() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final subjectsString = prefs.getString(_videoSubjectsKey);
      if (subjectsString == null) return [];

      final subjectsJson = jsonDecode(subjectsString) as List;
      return subjectsJson
          .map(
            (json) => VideoSubject(
              id: json['id'],
              name: json['name'],
              description: json['description'],
              sectionId: json['sectionId'],
              order: json['order'],
              iconUrl: json['iconUrl'],
              color: json['color'],
              isActive: json['isActive'],
              createdByAdminId: json['createdByAdminId'],
              createdAt: DateTime.parse(json['createdAt']),
              updatedAt: DateTime.parse(json['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل مواد الفيديوهات المحلية: $e');
      return [];
    }
  }

  // ===== إدارة وحدات الفيديوهات =====

  /// حفظ وحدات الفيديوهات محلياً
  Future<void> saveVideoUnits(List<VideoUnit> units) async {
    final prefs = await SharedPreferences.getInstance();
    final unitsJson = units
        .map(
          (u) => {
            'id': u.id,
            'name': u.name,
            'description': u.description,
            'subjectId': u.subjectId,
            'sectionId': u.sectionId,
            'order': u.order,
            'iconUrl': u.iconUrl,
            'color': u.color,
            'isActive': u.isActive,
            'createdByAdminId': u.createdByAdminId,
            'createdAt': u.createdAt.toIso8601String(),
            'updatedAt': u.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_videoUnitsKey, jsonEncode(unitsJson));
  }

  /// تحميل وحدات الفيديوهات المحفوظة محلياً
  Future<List<VideoUnit>> getOfflineVideoUnits() async {
    final prefs = await SharedPreferences.getInstance();
    final unitsJson = prefs.getString(_videoUnitsKey);
    if (unitsJson == null) return [];

    try {
      final unitsList = jsonDecode(unitsJson) as List;
      return unitsList
          .map(
            (u) => VideoUnit(
              id: u['id'],
              name: u['name'],
              description: u['description'],
              subjectId: u['subjectId'],
              sectionId: u['sectionId'] ?? '',
              order: u['order'] ?? 0,
              iconUrl: u['iconUrl'] ?? '',
              color: u['color'] ?? '',
              isActive: u['isActive'] ?? true,
              createdByAdminId: u['createdByAdminId'] ?? '',
              createdAt: DateTime.parse(u['createdAt']),
              updatedAt: DateTime.parse(u['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  // ===== إدارة دروس الفيديوهات =====

  /// حفظ دروس الفيديوهات محلياً
  Future<void> saveVideoLessons(List<VideoLesson> lessons) async {
    final prefs = await SharedPreferences.getInstance();
    final lessonsJson = lessons
        .map(
          (l) => {
            'id': l.id,
            'unitId': l.unitId,
            'subjectId': l.subjectId,
            'sectionId': l.sectionId,
            'name': l.name,
            'description': l.description,
            'order': l.order,
            'iconUrl': l.iconUrl,
            'isActive': l.isActive,
            'createdByAdminId': l.createdByAdminId,
            'createdAt': l.createdAt.toIso8601String(),
            'updatedAt': l.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_videoLessonsKey, jsonEncode(lessonsJson));
  }

  /// تحميل دروس الفيديوهات المحفوظة محلياً
  Future<List<VideoLesson>> getOfflineVideoLessons() async {
    final prefs = await SharedPreferences.getInstance();
    final lessonsJson = prefs.getString(_videoLessonsKey);
    if (lessonsJson == null) return [];

    try {
      final lessonsList = jsonDecode(lessonsJson) as List;
      return lessonsList
          .map(
            (l) => VideoLesson(
              id: l['id'],
              unitId: l['unitId'],
              subjectId: l['subjectId'],
              sectionId: l['sectionId'] ?? '',
              name: l['name'],
              description: l['description'],
              order: l['order'] ?? 0,
              iconUrl: l['iconUrl'] ?? '',
              isActive: l['isActive'] ?? true,
              createdByAdminId: l['createdByAdminId'] ?? '',
              createdAt: DateTime.parse(l['createdAt']),
              updatedAt: DateTime.parse(l['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  // ===== إدارة الفيديوهات =====

  /// حفظ الفيديوهات محلياً
  Future<void> saveVideos(List<Video> videos) async {
    final prefs = await SharedPreferences.getInstance();
    final videosJson = videos
        .map(
          (v) => {
            'id': v.id,
            'lessonId': v.lessonId,
            'unitId': v.unitId,
            'subjectId': v.subjectId,
            'sectionId': v.sectionId,
            'title': v.title,
            'description': v.description,
            'order': v.order,
            'thumbnailUrl': v.thumbnailUrl,
            'durationInSeconds': v.durationInSeconds,
            'fileSizeInBytes': v.fileSizeInBytes,
            'isActive': v.isActive,
            'isPreview': v.isPreview,
            'createdByAdminId': v.createdByAdminId,
            'createdAt': v.createdAt.toIso8601String(),
            'updatedAt': v.updatedAt.toIso8601String(),
            // حفظ الروابط المشفرة
            'encryptedUrl360': v.encryptedUrl360,
            'encryptedUrl480': v.encryptedUrl480,
            'encryptedUrl720': v.encryptedUrl720,
          },
        )
        .toList();
    await prefs.setString(_videosKey, jsonEncode(videosJson));
  }

  /// تحميل الفيديوهات المحفوظة محلياً
  Future<List<Video>> getOfflineVideos() async {
    final prefs = await SharedPreferences.getInstance();
    final videosJson = prefs.getString(_videosKey);
    if (videosJson == null) return [];

    try {
      final videosList = jsonDecode(videosJson) as List;
      return videosList
          .map(
            (v) => Video(
              id: v['id'],
              lessonId: v['lessonId'],
              unitId: v['unitId'],
              subjectId: v['subjectId'],
              sectionId: v['sectionId'] ?? '',
              title: v['title'],
              description: v['description'],
              order: v['order'] ?? 0,
              thumbnailUrl: v['thumbnailUrl'] ?? '',
              durationInSeconds: v['durationInSeconds'] ?? 0,
              fileSizeInBytes: v['fileSizeInBytes'] ?? 0,
              isActive: v['isActive'] ?? true,
              isPreview: v['isPreview'] ?? false,
              createdByAdminId: v['createdByAdminId'] ?? '',
              createdAt: DateTime.parse(v['createdAt']),
              updatedAt: DateTime.parse(v['updatedAt']),
              // استرجاع الروابط المشفرة
              encryptedUrl360: v['encryptedUrl360'],
              encryptedUrl480: v['encryptedUrl480'],
              encryptedUrl720: v['encryptedUrl720'],
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// حساب حجم البيانات المحفوظة
  Future<Map<String, int>> getStorageSize() async {
    final prefs = await SharedPreferences.getInstance();

    final questionsSize = (prefs.getString(_questionsKey) ?? '').length;
    final subjectsSize = (prefs.getString(_subjectsKey) ?? '').length;
    final unitsSize = (prefs.getString(_unitsKey) ?? '').length;
    final lessonsSize = (prefs.getString(_lessonsKey) ?? '').length;

    return {
      'questions': questionsSize,
      'subjects': subjectsSize,
      'units': unitsSize,
      'lessons': lessonsSize,
      'total': questionsSize + subjectsSize + unitsSize + lessonsSize,
    };
  }

  /// مسح جميع البيانات المحفوظة
  Future<void> clearAllOfflineData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_questionsKey);
    await prefs.remove(_subjectsKey);
    await prefs.remove(_unitsKey);
    await prefs.remove(_lessonsKey);
    await prefs.remove(_downloadedSubjectsKey);
    await prefs.remove(_lastSyncKey);
  }

  /// مسح بيانات مادة معينة
  Future<void> clearSubjectData(String subjectId) async {
    // إزالة الأسئلة
    final questions = await getOfflineQuestions();
    final filteredQuestions = questions
        .where((q) => q.subjectId != subjectId)
        .toList();
    await saveQuestions(filteredQuestions);

    // إزالة الوحدات
    final units = await getOfflineUnits();
    final filteredUnits = units.where((u) => u.subjectId != subjectId).toList();
    await saveUnits(filteredUnits);

    // إزالة الدروس (بناءً على الوحدات المحذوفة)
    final unitIds = units
        .where((u) => u.subjectId == subjectId)
        .map((u) => u.id)
        .toList();
    final lessons = await getOfflineLessons();
    final filteredLessons = lessons
        .where((l) => !unitIds.contains(l.unitId))
        .toList();
    await saveLessons(filteredLessons);

    // إزالة من قائمة المواد المحملة
    await removeDownloadedSubject(subjectId);
  }

  /// فحص ما إذا كانت البيانات متاحة محلياً
  Future<bool> hasOfflineData() async {
    final questions = await getOfflineQuestions();
    final subjects = await getOfflineSubjects();
    return questions.isNotEmpty && subjects.isNotEmpty;
  }

  /// الحصول على إحصائيات التخزين المحلي
  Future<Map<String, dynamic>> getOfflineStats() async {
    final questions = await getOfflineQuestions();
    final subjects = await getOfflineSubjects();
    final units = await getOfflineUnits();
    final lessons = await getOfflineLessons();
    final downloadedSubjects = await getDownloadedSubjects();
    final lastSync = await getLastSyncTime();
    final storageSize = await getStorageSize();

    return {
      'questionsCount': questions.length,
      'subjectsCount': subjects.length,
      'unitsCount': units.length,
      'lessonsCount': lessons.length,
      'downloadedSubjectsCount': downloadedSubjects.length,
      'lastSyncTime': lastSync?.toIso8601String(),
      'storageSizeKB': (storageSize['total']! / 1024).round(),
    };
  }

  // ===== إدارة الأقسام =====

  /// حفظ الأقسام محلياً
  Future<void> saveSections(List<Section> sections) async {
    final prefs = await SharedPreferences.getInstance();
    final sectionsJson = sections
        .map(
          (s) => {
            'id': s.id,
            'name': s.name,
            'description': s.description,
            'color': s.color,
            'isFree': s.isFree,
            'isActive': s.isActive,
            'createdAt': s.createdAt.toIso8601String(),
            'updatedAt': s.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_sectionsKey, jsonEncode(sectionsJson));
    debugPrint('💾 تم حفظ ${sections.length} قسم محلياً');
  }

  /// تحميل الأقسام المحفوظة محلياً
  Future<List<Section>> getOfflineSections() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sectionsString = prefs.getString(_sectionsKey);

      if (sectionsString == null || sectionsString.isEmpty) {
        return [];
      }

      final sectionsJson = jsonDecode(sectionsString) as List;
      final sections = sectionsJson
          .map(
            (json) => Section(
              id: json['id'] ?? '',
              name: json['name'] ?? '',
              description: json['description'] ?? '',
              color: json['color'] ?? '#6C5CE7',
              isFree: json['isFree'] ?? false,
              isActive: json['isActive'] ?? true,
              createdAt: DateTime.parse(
                json['createdAt'] ?? DateTime.now().toIso8601String(),
              ),
              updatedAt: DateTime.parse(
                json['updatedAt'] ?? DateTime.now().toIso8601String(),
              ),
            ),
          )
          .toList();

      debugPrint('📱 تم تحميل ${sections.length} قسم من التخزين المحلي');
      return sections;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام المحفوظة محلياً: $e');
      return [];
    }
  }

  /// حذف الأقسام المحفوظة محلياً
  Future<void> clearOfflineSections() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_sectionsKey);
    debugPrint('🗑️ تم حذف الأقسام المحفوظة محلياً');
  }
}
