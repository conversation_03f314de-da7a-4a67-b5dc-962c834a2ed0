import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/video_model.dart';

/// خدمة إدارة فيديوهات المواد - نظام التحميل الموحد
/// تحميل جميع فيديوهات المادة في قراءة واحدة من Firebase
class SubjectVideosService extends ChangeNotifier {
  static final SubjectVideosService _instance =
      SubjectVideosService._internal();
  static SubjectVideosService get instance => _instance;
  SubjectVideosService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// تحميل جميع فيديوهات مادة معينة في قراءة واحدة
  Future<List<Video>> getAllSubjectVideos(String subjectId) async {
    try {
      debugPrint('🔄 تحميل جميع فيديوهات المادة: $subjectId');

      // محاولة تحميل من النظام الجديد أولاً
      var querySnapshot = await _firestore
          .collection('subject_videos') // مجموعة جديدة لكل مادة
          .doc(subjectId) // وثيقة واحدة لكل مادة
          .collection('videos') // جميع فيديوهات المادة
          .where('isActive', isEqualTo: true)
          .get();

      // إذا لم توجد فيديوهات في النظام الجديد، تحميل من النظام القديم
      if (querySnapshot.docs.isEmpty) {
        debugPrint(
          '📭 لا توجد فيديوهات في النظام الجديد، تحميل من النظام القديم',
        );
        querySnapshot = await _firestore
            .collection('videos')
            .where('subjectId', isEqualTo: subjectId)
            .where('isActive', isEqualTo: true)
            .get();
      }

      debugPrint('📊 تم العثور على ${querySnapshot.docs.length} فيديو للمادة');

      final videos = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // إضافة معرف الوثيقة
              return Video.fromMap(data);
            } catch (e) {
              debugPrint('❌ خطأ في تحويل الفيديو ${doc.id}: $e');
              return null;
            }
          })
          .where((video) => video != null)
          .cast<Video>()
          .toList();

      // ترتيب الفيديوهات حسب تاريخ الإنشاء (الأقدم أولاً)
      videos.sort((a, b) => a.createdAt.compareTo(b.createdAt));

      debugPrint('✅ تم تحميل ${videos.length} فيديو بنجاح للمادة $subjectId');
      return videos;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل فيديوهات المادة: $e');
      return [];
    }
  }

  /// فلترة الفيديوهات محلياً حسب الوحدة
  List<Video> filterVideosByUnit(List<Video> videos, String unitId) {
    return videos.where((v) => v.unitId == unitId).toList();
  }

  /// فلترة الفيديوهات محلياً حسب الدرس
  List<Video> filterVideosByLesson(List<Video> videos, String lessonId) {
    return videos.where((v) => v.lessonId == lessonId).toList();
  }

  /// البحث في الفيديوهات محلياً
  List<Video> searchVideos(List<Video> videos, String query) {
    if (query.trim().isEmpty) return videos;

    final searchQuery = query.toLowerCase().trim();
    return videos.where((video) {
      // البحث في عنوان الفيديو
      final titleMatch = video.title.toLowerCase().contains(searchQuery);

      // البحث في الوصف
      final descriptionMatch = video.description.toLowerCase().contains(
        searchQuery,
      );

      return titleMatch || descriptionMatch;
    }).toList();
  }

  /// التحقق من وجود تحديثات للمادة
  Future<bool> hasUpdates(String subjectId, DateTime lastUpdateTime) async {
    try {
      debugPrint('🔍 التحقق من تحديثات فيديوهات المادة: $subjectId');
      debugPrint('📅 آخر تحديث محلي: $lastUpdateTime');

      // التحقق من آخر تحديث في قاعدة البيانات
      final metadataDoc = await _firestore
          .collection('subject_videos_metadata')
          .doc(subjectId)
          .get();

      if (!metadataDoc.exists) {
        debugPrint('❌ لا توجد بيانات وصفية للمادة');
        return false;
      }

      final data = metadataDoc.data()!;
      final serverLastUpdate = (data['lastUpdate'] as Timestamp).toDate();

      debugPrint('📅 آخر تحديث في الخادم: $serverLastUpdate');

      final hasNewUpdates = serverLastUpdate.isAfter(lastUpdateTime);
      debugPrint(hasNewUpdates ? '🆕 توجد تحديثات جديدة' : '✅ لديك آخر تحديث');

      return hasNewUpdates;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من التحديثات: $e');
      return false;
    }
  }

  /// تحديث بيانات المادة الوصفية (للاستخدام في تطبيق الإدارة)
  Future<void> updateSubjectMetadata(String subjectId) async {
    try {
      await _firestore.collection('subject_videos_metadata').doc(subjectId).set(
        {'lastUpdate': FieldValue.serverTimestamp(), 'subjectId': subjectId},
        SetOptions(merge: true),
      );

      debugPrint('✅ تم تحديث بيانات المادة الوصفية: $subjectId');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات الوصفية: $e');
    }
  }

  /// إنشاء فيديو جديد في المادة (للاستخدام في تطبيق الإدارة)
  Future<String> createVideoInSubject(Video video) async {
    try {
      final docRef = _firestore
          .collection('subject_videos')
          .doc(video.subjectId)
          .collection('videos')
          .doc();

      final videoWithId = video.copyWith(id: docRef.id);

      await docRef.set(videoWithId.toMap());

      // تحديث بيانات المادة الوصفية
      await updateSubjectMetadata(video.subjectId);

      debugPrint('✅ تم إنشاء الفيديو في المادة: ${videoWithId.id}');
      notifyListeners();
      return videoWithId.id;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الفيديو في المادة: $e');
      rethrow;
    }
  }

  /// تحديث فيديو في المادة (للاستخدام في تطبيق الإدارة)
  Future<void> updateVideoInSubject(Video video) async {
    try {
      await _firestore
          .collection('subject_videos')
          .doc(video.subjectId)
          .collection('videos')
          .doc(video.id)
          .update(video.copyWith(updatedAt: DateTime.now()).toMap());

      // تحديث بيانات المادة الوصفية
      await updateSubjectMetadata(video.subjectId);

      debugPrint('✅ تم تحديث الفيديو في المادة: ${video.id}');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الفيديو في المادة: $e');
      rethrow;
    }
  }

  /// حذف فيديو من المادة (للاستخدام في تطبيق الإدارة)
  Future<void> deleteVideoFromSubject(String subjectId, String videoId) async {
    try {
      await _firestore
          .collection('subject_videos')
          .doc(subjectId)
          .collection('videos')
          .doc(videoId)
          .delete();

      // تحديث بيانات المادة الوصفية
      await updateSubjectMetadata(subjectId);

      debugPrint('✅ تم حذف الفيديو من المادة: $videoId');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في حذف الفيديو من المادة: $e');
      rethrow;
    }
  }

  /// الحصول على قائمة الوحدات التي تحتوي على فيديوهات
  Future<List<String>> getUnitsWithVideos(String subjectId) async {
    final allVideos = await getAllSubjectVideos(subjectId);
    final unitIds = allVideos
        .map((v) => v.unitId)
        .where((unitId) => unitId.isNotEmpty)
        .toSet()
        .toList();

    return unitIds;
  }

  /// الحصول على قائمة الدروس التي تحتوي على فيديوهات في وحدة معينة
  Future<List<String>> getLessonsWithVideos(
    String subjectId,
    String unitId,
  ) async {
    final allVideos = await getAllSubjectVideos(subjectId);
    final filteredVideos = allVideos.where((v) => v.unitId == unitId).toList();

    final lessonIds = filteredVideos
        .map((v) => v.lessonId)
        .where((lessonId) => lessonId.isNotEmpty)
        .toSet()
        .toList();

    return lessonIds;
  }
}
