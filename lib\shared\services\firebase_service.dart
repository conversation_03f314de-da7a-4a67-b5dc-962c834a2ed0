import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../core/utils/logger.dart';
import '../models/subject_model.dart';
import '../models/subscription_code_model.dart';
import '../models/user_subscription_model.dart';
import 'device_service.dart';

class FirebaseService {
  static FirebaseService? _instance;
  static FirebaseService get instance => _instance ??= FirebaseService._();

  FirebaseService._();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final DeviceService _deviceService = DeviceService.instance;

  // Collections
  static const String subjectsCollection = 'subjects';
  static const String codesCollection = 'subscription_codes';
  static const String subscriptionsCollection = 'user_subscriptions';

  /// تسجيل دخول مجهول
  Future<User?> signInAnonymously() async {
    try {
      final userCredential = await _auth.signInAnonymously();
      return userCredential.user;
    } catch (e) {
      Logger.error('Error signing in anonymously', e);
      return null;
    }
  }

  /// الحصول على جميع المواد
  Future<List<Subject>> getAllSubjects() async {
    try {
      final querySnapshot = await _firestore
          .collection(subjectsCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      return querySnapshot.docs
          .map((doc) => Subject.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      Logger.error('Error getting subjects', e);
      return [];
    }
  }

  /// التحقق من صحة الكود
  Future<SubscriptionCode?> validateCode(String code) async {
    try {
      final querySnapshot = await _firestore
          .collection(codesCollection)
          .where('code', isEqualTo: code)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return null; // الكود غير موجود
      }

      final codeDoc = querySnapshot.docs.first;
      final subscriptionCode = SubscriptionCode.fromMap({
        ...codeDoc.data(),
        'id': codeDoc.id,
      });

      return subscriptionCode.isValid ? subscriptionCode : null;
    } catch (e) {
      Logger.error('Error validating code', e);
      return null;
    }
  }

  /// استخدام الكود وتفعيل الاشتراك (بدون transactions لتجنب مشاكل Windows)
  Future<bool> useSubscriptionCode(String code) async {
    try {
      final deviceId = await _deviceService.getDeviceId();

      // التحقق من صحة الكود
      final subscriptionCode = await validateCode(code);
      if (subscriptionCode == null) {
        print('❌ كود اشتراك غير صحيح: $code');
        return false; // الكود غير صحيح
      }

      // التحقق من أن الكود لم يُستخدم من قبل
      if (subscriptionCode.isUsed) {
        print('❌ الكود مستخدم بالفعل: $code');
        return false; // الكود مستخدم بالفعل
      }

      // الحصول على اشتراك المستخدم الحالي
      final userSubscription = await _getUserSubscription(deviceId);

      // التحقق من أن المستخدم لم يستخدم هذا الكود من قبل
      if (userSubscription != null && userSubscription.hasUsedCode(code)) {
        print('❌ المستخدم استخدم هذا الكود من قبل: $code');
        return false;
      }

      // تحديث الكود ليصبح مستخدم (بدون transaction)
      final codeRef = _firestore
          .collection(codesCollection)
          .doc(subscriptionCode.id);

      await codeRef.update({
        'status': CodeStatus.used.name,
        'usedByDeviceId': deviceId,
        'usedAt': DateTime.now().millisecondsSinceEpoch,
      });

      // تحديث أو إنشاء اشتراك المستخدم
      final subscriptionRef = _firestore
          .collection(subscriptionsCollection)
          .doc(deviceId);

      final expiryDate =
          subscriptionCode.expiresAt ??
          DateTime.now().add(const Duration(days: 365));

      if (userSubscription == null) {
        // إنشاء اشتراك جديد
        final newSubscription =
            UserSubscription(
              id: deviceId,
              deviceId: deviceId,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              expiresAt: expiryDate,
            ).addSubscription(
              subscriptionCode.subjectIds,
              subscriptionCode.videoSubjectIds,
              code,
              expiryDate,
            );

        await subscriptionRef.set(newSubscription.toMap());
      } else {
        // تحديث الاشتراك الموجود
        final updatedSubscription = userSubscription.addSubscription(
          subscriptionCode.subjectIds,
          subscriptionCode.videoSubjectIds,
          code,
          expiryDate,
        );

        await subscriptionRef.update(updatedSubscription.toMap());
      }

      print('✅ تم استخدام كود الاشتراك بنجاح: $code');
      return true;
    } catch (e) {
      Logger.error('Error using subscription code', e);
      return false;
    }
  }

  /// الحصول على اشتراك المستخدم
  Future<UserSubscription?> getUserSubscription([String? deviceId]) async {
    try {
      deviceId ??= await _deviceService.getDeviceId();

      final doc = await _firestore
          .collection(subscriptionsCollection)
          .doc(deviceId)
          .get();

      if (!doc.exists) {
        return null;
      }

      return UserSubscription.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      Logger.error('Error getting user subscription', e);
      return null;
    }
  }

  /// الحصول على اشتراك المستخدم (private method للاستخدام في transactions)
  Future<UserSubscription?> _getUserSubscription(String deviceId) async {
    try {
      final doc = await _firestore
          .collection(subscriptionsCollection)
          .doc(deviceId)
          .get();

      if (!doc.exists) {
        return null;
      }

      return UserSubscription.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      Logger.error('Error getting user subscription', e);
      return null;
    }
  }

  /// التحقق من اشتراك المستخدم في مادة معينة
  Future<bool> isUserSubscribedToSubject(String subjectId) async {
    try {
      final subscription = await getUserSubscription();
      return subscription?.isSubscribedToSubject(subjectId) ?? false;
    } catch (e) {
      Logger.error('Error checking subject subscription', e);
      return false;
    }
  }

  /// الحصول على المواد المشترك بها المستخدم
  Future<List<Subject>> getUserSubscribedSubjects() async {
    try {
      final subscription = await getUserSubscription();
      if (subscription == null || subscription.subscribedSubjectIds.isEmpty) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection(subjectsCollection)
          .where(
            FieldPath.documentId,
            whereIn: subscription.subscribedSubjectIds,
          )
          .get();

      return querySnapshot.docs
          .map((doc) => Subject.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      Logger.error('Error getting subscribed subjects', e);
      return [];
    }
  }

  /// stream للاستماع لتغييرات اشتراك المستخدم
  Stream<UserSubscription?> getUserSubscriptionStream() async* {
    try {
      final deviceId = await _deviceService.getDeviceId();

      yield* _firestore
          .collection(subscriptionsCollection)
          .doc(deviceId)
          .snapshots()
          .map((doc) {
            if (!doc.exists) return null;
            return UserSubscription.fromMap({...doc.data()!, 'id': doc.id});
          });
    } catch (e) {
      Logger.error('Error in subscription stream', e);
      yield null;
    }
  }
}
