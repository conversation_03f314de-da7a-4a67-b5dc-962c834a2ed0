import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
// import 'device_service.dart'; // غير مستخدم حالياً
import 'subscription_service.dart';
import 'data_distribution_service.dart';
import '../models/video_section_model.dart';

// استيرادات مؤقتة - سيتم إضافة النماذج لاحقاً
typedef Section = Map<String, dynamic>;
typedef Subject = Map<String, dynamic>;
typedef VideoSection = Map<String, dynamic>;
typedef VideoSubject = Map<String, dynamic>;

/// خدمة البيانات الموحدة المحسنة
/// نظام ثوري يوفر 95%+ من مساحة التخزين و 90%+ من القراءات
class OptimizedUnifiedDataService {
  static final OptimizedUnifiedDataService _instance =
      OptimizedUnifiedDataService._internal();
  static OptimizedUnifiedDataService get instance => _instance;
  OptimizedUnifiedDataService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  // final DeviceService _deviceService = DeviceService.instance; // غير مستخدم حالياً
  final SubscriptionService _subscriptionService = SubscriptionService.instance;
  final DataDistributionService _dataDistributionService =
      DataDistributionService.instance;

  /// مجموعة البيانات الموحدة الجديدة
  static const String _unifiedCollection = 'optimized_unified_data';
  static const String _generalDocId = 'general_data';
  static const String _subjectPrefix = 'subject_';
  static const String _freeSubjectPrefix = 'free_subject_';
  static const String _videoSubjectPrefix = 'video_subject_';
  static const String _freeVideoSubjectPrefix = 'free_video_subject_';

  // ═══════════════════════════════════════════════════════════════
  // 🏗️ إنشاء البيانات الموحدة (للأدمن)
  // ═══════════════════════════════════════════════════════════════

  /// إنشاء الوثيقة العامة (أقسام + مواد بدون محتوى)
  Future<bool> generateGeneralData() async {
    try {
      debugPrint('🔄 إنشاء الوثيقة العامة...');

      // جمع البيانات العامة
      final sections = await _getAllSections();
      final subjects = await _getAllSubjects();
      final videoSections = await _getAllVideoSections();
      final videoSubjects = await _getAllVideoSubjects();

      // إنشاء الوثيقة العامة
      // 🔍 تشخيص البيانات قبل الحفظ
      debugPrint('🔍 [ADMIN DEBUG] البيانات قبل الحفظ:');
      debugPrint('   - أقسام الاختبارات: ${sections.length}');
      for (var section in sections) {
        debugPrint('     - ${section['name']}: مجاني=${section['isFree']}');
      }
      debugPrint('   - أقسام الفيديو: ${videoSections.length}');
      for (var section in videoSections) {
        debugPrint('     - ${section['name']}: مجاني=${section['isFree']}');
      }

      final generalData = {
        'version': DateTime.now().millisecondsSinceEpoch,
        'lastUpdated': FieldValue.serverTimestamp(),
        'data': {
          // أقسام الاختبارات (مدفوعة + مجانية)
          'test_sections': sections,
          'test_subjects': subjects,

          // أقسام الفيديوهات (مدفوعة + مجانية)
          'video_sections': videoSections,
          'video_subjects': videoSubjects,
        },
      };

      // حفظ الوثيقة العامة
      await _firestore
          .collection(_unifiedCollection)
          .doc(_generalDocId)
          .set(generalData);

      debugPrint('✅ تم إنشاء الوثيقة العامة بنجاح');
      debugPrint('📊 الإحصائيات:');
      debugPrint('   - أقسام الاختبارات: ${sections.length}');
      debugPrint('   - مواد الاختبارات: ${subjects.length}');
      debugPrint('   - أقسام الفيديوهات: ${videoSections.length}');
      debugPrint('   - مواد الفيديوهات: ${videoSubjects.length}');

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الوثيقة العامة: $e');
      return false;
    }
  }

  /// إنشاء وثائق المواد المدفوعة (وثيقة لكل مادة)
  Future<bool> generatePaidSubjectsData() async {
    try {
      debugPrint('🔄 إنشاء وثائق المواد المدفوعة...');

      // جمع جميع المواد المدفوعة
      final paidSubjects = await _getAllPaidSubjects();
      final paidVideoSubjects = await _getAllPaidVideoSubjects();

      debugPrint('📊 إحصائيات المواد المدفوعة:');
      debugPrint('   - مواد الاختبارات: ${paidSubjects.length}');
      debugPrint('   - مواد الفيديوهات: ${paidVideoSubjects.length}');

      for (final subject in paidVideoSubjects) {
        debugPrint('   📹 مادة فيديو: ${subject['name']} (${subject['id']})');
      }

      int successCount = 0;
      int totalCount = paidSubjects.length + paidVideoSubjects.length;

      // إنشاء وثيقة لكل مادة اختبارات مدفوعة
      for (final subject in paidSubjects) {
        final success = await _generateSubjectDocument(subject, false);
        if (success) successCount++;
      }

      // إنشاء وثيقة لكل مادة فيديوهات مدفوعة
      for (final subject in paidVideoSubjects) {
        final success = await _generateVideoSubjectDocument(subject, false);
        if (success) successCount++;
      }

      debugPrint('✅ تم إنشاء $successCount من $totalCount وثيقة مادة مدفوعة');
      return successCount == totalCount;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء وثائق المواد المدفوعة: $e');
      return false;
    }
  }

  /// إنشاء وثائق المواد المجانية (وثيقة لكل مادة)
  Future<bool> generateFreeSubjectsData() async {
    try {
      debugPrint('🔄 إنشاء وثائق المواد المجانية...');

      // جمع جميع المواد المجانية
      final freeSubjects = await _getAllFreeSubjects();
      final freeVideoSubjects = await _getAllFreeVideoSubjects();

      int successCount = 0;
      int totalCount = freeSubjects.length + freeVideoSubjects.length;

      // إنشاء وثيقة لكل مادة اختبارات مجانية
      for (final subject in freeSubjects) {
        final success = await _generateSubjectDocument(subject, true);
        if (success) successCount++;
      }

      // إنشاء وثيقة لكل مادة فيديوهات مجانية
      for (final subject in freeVideoSubjects) {
        final success = await _generateVideoSubjectDocument(subject, true);
        if (success) successCount++;
      }

      debugPrint('✅ تم إنشاء $successCount من $totalCount وثيقة مادة مجانية');
      return successCount == totalCount;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء وثائق المواد المجانية: $e');
      return false;
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // 📱 تحميل البيانات (للطالب)
  // ═══════════════════════════════════════════════════════════════

  /// تحميل البيانات للطالب (الوثيقة العامة + المواد المشترك بها)
  Future<bool> loadStudentData() async {
    try {
      debugPrint('🔄 [STUDENT MONITOR] تحميل بيانات الطالب...');
      debugPrint('🕐 [STUDENT MONITOR] الوقت: ${DateTime.now()}');

      // 1. تحميل الوثيقة العامة
      debugPrint('📥 [STUDENT MONITOR] تحميل الوثيقة العامة...');
      final generalSuccess = await _loadGeneralData();
      if (!generalSuccess) {
        debugPrint('❌ [STUDENT MONITOR] فشل في تحميل الوثيقة العامة');
        return false;
      }
      debugPrint('✅ [STUDENT MONITOR] تم تحميل الوثيقة العامة بنجاح');

      // 2. فحص الاشتراك
      debugPrint('🔍 [STUDENT MONITOR] فحص حالة الاشتراك...');
      final hasSubscription = _subscriptionService.hasActiveSubscription();
      if (!hasSubscription) {
        debugPrint(
          'ℹ️ [STUDENT MONITOR] لا يوجد اشتراك نشط - تم تحميل البيانات العامة فقط',
        );

        // تحديث خدمة فرز البيانات
        debugPrint('🔄 [STUDENT MONITOR] تحديث خدمة فرز البيانات...');
        await DataDistributionService.instance.refreshAllData();
        debugPrint('✅ [STUDENT MONITOR] تم تحديث جميع البيانات بنجاح');

        return true;
      }
      debugPrint('✅ [STUDENT MONITOR] يوجد اشتراك نشط');

      // 3. تحميل المواد المشترك بها
      debugPrint('📋 [STUDENT MONITOR] جلب قائمة المواد المشترك بها...');
      final subscription = await _subscriptionService.getCurrentSubscription();
      final subscribedSubjects =
          subscription?.subscribedSubjectIds ?? <String>[];
      final videoSubjects = subscription?.videoSubjectIds ?? <String>[];
      final allSubscribedSubjects = [...subscribedSubjects, ...videoSubjects];
      debugPrint(
        '📊 [STUDENT MONITOR] المواد المشترك بها: ${allSubscribedSubjects.length}',
      );
      debugPrint('   - مواد الاختبارات: ${subscribedSubjects.length}');
      debugPrint('   - مواد الفيديوهات: ${videoSubjects.length}');

      int loadedCount = 0;
      for (final subjectId in allSubscribedSubjects) {
        debugPrint('📥 [STUDENT MONITOR] تحميل المادة: $subjectId');
        final success = await _loadSubjectData(subjectId);
        if (success) {
          loadedCount++;
          debugPrint('✅ [STUDENT MONITOR] تم تحميل المادة: $subjectId');
        } else {
          debugPrint('❌ [STUDENT MONITOR] فشل في تحميل المادة: $subjectId');
        }
      }

      debugPrint(
        '📊 [STUDENT MONITOR] تم تحميل $loadedCount من ${allSubscribedSubjects.length} مادة مدفوعة',
      );
      debugPrint(
        '📊 [STUDENT MONITOR] إجمالي القراءات: ${1 + loadedCount} قراءة فقط!',
      );

      // تحديث خدمة فرز البيانات
      debugPrint('🔄 [STUDENT MONITOR] تحديث خدمة فرز البيانات...');
      await DataDistributionService.instance.refreshAllData();
      debugPrint('✅ [STUDENT MONITOR] تم تحديث جميع البيانات بنجاح');

      debugPrint('🕐 [STUDENT MONITOR] انتهى في: ${DateTime.now()}');

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الطالب: $e');
      return false;
    }
  }

  /// تحميل مواد مجانية محددة (للطالب)
  Future<bool> loadSelectedFreeSubjects(
    List<String> subjectIds,
    bool isVideo,
  ) async {
    try {
      final contentType = isVideo ? 'فيديو' : 'اختبار';
      debugPrint('🔄 تحميل ${subjectIds.length} مادة $contentType مجانية...');

      int loadedCount = 0;
      for (final subjectId in subjectIds) {
        final docId = _freeSubjectPrefix + subjectId;
        debugPrint('🔍 محاولة تحميل وثيقة: $docId');

        final success = await _loadDocumentData(docId);
        if (success) {
          loadedCount++;
          debugPrint('✅ تم تحميل مادة $contentType: $subjectId');
        } else {
          debugPrint('❌ فشل تحميل مادة $contentType: $subjectId');
        }
      }

      debugPrint(
        '✅ تم تحميل $loadedCount من ${subjectIds.length} مادة $contentType مجانية',
      );
      debugPrint('📊 عدد القراءات: $loadedCount قراءة فقط!');

      // إعادة تحديث خدمة فرز البيانات
      if (loadedCount > 0) {
        debugPrint('🔄 تحديث خدمة فرز البيانات بعد تحميل المواد المجانية...');
        await _dataDistributionService.refreshAllData();
        debugPrint('✅ تم تحديث خدمة فرز البيانات');
      }

      return loadedCount == subjectIds.length;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المواد المجانية: $e');
      return false;
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // 🔧 دوال مساعدة
  // ═══════════════════════════════════════════════════════════════

  /// تحميل الوثيقة العامة
  Future<bool> _loadGeneralData() async {
    return await _loadDocumentData(_generalDocId);
  }

  /// تحميل بيانات مادة محددة
  Future<bool> _loadSubjectData(String subjectId) async {
    final docId = _subjectPrefix + subjectId;
    return await _loadDocumentData(docId);
  }

  /// تحميل وثيقة محددة مع فحص الإصدار الذكي
  /// يحدث البيانات فقط عند وجود إصدار جديد
  Future<bool> _loadDocumentData(String docId) async {
    try {
      // فحص الإصدار المحلي
      final localVersion = await _getLocalVersion(docId);

      // فحص الإصدار على Firebase (قراءة خفيفة)
      final doc = await _firestore
          .collection(_unifiedCollection)
          .doc(docId)
          .get();

      if (!doc.exists) {
        if (docId.startsWith(_freeSubjectPrefix)) {
          debugPrint('❌ الوثيقة المجانية غير موجودة: $docId');
          debugPrint('💡 يجب على الأدمن إنشاء هذه الوثيقة المجانية أولاً');
        } else {
          debugPrint('⚠️ الوثيقة غير موجودة: $docId');
        }
        return false;
      }

      final data = doc.data()!;
      final remoteVersion = data['version'] as int;

      // مقارنة الإصدارات - الجزء الذكي!
      if (localVersion == remoteVersion) {
        debugPrint('ℹ️ الوثيقة محدثة محلياً: $docId (إصدار: $localVersion)');
        return true; // لا حاجة للتحميل!
      }

      // تحديث البيانات المحلية (استبدال ذكي)
      debugPrint('🔄 [DEBUG] بدء استبدال البيانات المحلية: $docId');
      await _replaceLocalData(docId, data);
      debugPrint('✅ تم تحديث الوثيقة: $docId');
      debugPrint('   - الإصدار القديم: $localVersion');
      debugPrint('   - الإصدار الجديد: $remoteVersion');
      debugPrint('✅ [DEBUG] انتهى استبدال البيانات المحلية: $docId');

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الوثيقة $docId: $e');
      return false;
    }
  }

  /// استبدال البيانات المحلية (الطريقة الذكية)
  /// تحذف النسخة القديمة وتحفظ الجديدة في نفس المكان
  Future<void> _replaceLocalData(
    String docId,
    Map<String, dynamic> data,
  ) async {
    try {
      debugPrint('🔄 [DEBUG] _replaceLocalData($docId):');
      debugPrint('   - عدد المفاتيح: ${data.keys.length}');

      // حذف البيانات القديمة أولاً
      debugPrint('🗑️ [DEBUG] حذف البيانات القديمة...');
      await _deleteLocalData(docId);

      // حفظ البيانات الجديدة
      debugPrint('💾 [DEBUG] حفظ البيانات الجديدة...');
      await _saveLocalData(docId, data);

      debugPrint('🔄 تم استبدال البيانات المحلية: $docId');
    } catch (e) {
      debugPrint('❌ خطأ في استبدال البيانات المحلية: $e');
    }
  }

  /// حفظ البيانات محلياً
  Future<void> _saveLocalData(String docId, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحويل Timestamp إلى int قبل التشفير
      final cleanData = _convertTimestampsToInt(data);
      final dataString = jsonEncode(cleanData);
      await prefs.setString(docId, dataString);

      debugPrint('💾 [STUDENT MONITOR] تم حفظ البيانات محلياً: $docId');
      debugPrint('   - حجم البيانات: ${dataString.length} حرف');
      debugPrint(
        '   - حجم البيانات: ${(dataString.length / 1024).toStringAsFixed(2)} KB',
      );

      // 🔍 تحليل محتوى البيانات
      if (cleanData['data'] != null) {
        final content = cleanData['data'] as Map<String, dynamic>;
        debugPrint('📊 [STUDENT MONITOR] محتوى الوثيقة:');
        content.forEach((key, value) {
          if (value is List) {
            debugPrint('   - $key: ${value.length} عنصر');
          }
        });
      }

      // تحديث خدمة فرز البيانات
      debugPrint('🔄 [STUDENT MONITOR] تحديث خدمة فرز البيانات...');
      await DataDistributionService.instance.refreshAllData();
    } catch (e) {
      debugPrint('❌ [STUDENT MONITOR] خطأ في حفظ البيانات محلياً: $e');
    }
  }

  /// حذف البيانات المحلية
  Future<void> _deleteLocalData(String docId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(docId);
      debugPrint('🗑️ تم حذف البيانات المحلية القديمة: $docId');
    } catch (e) {
      debugPrint('❌ خطأ في حذف البيانات المحلية: $e');
    }
  }

  /// جلب الإصدار المحلي
  Future<int> _getLocalVersion(String docId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(docId);
      if (dataString != null) {
        final data = jsonDecode(dataString);
        return data['version'] ?? 0;
      }
      return 0; // افتراضي - سيؤدي لتحميل البيانات
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإصدار المحلي: $e');
      return 0;
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // 📊 دوال جمع البيانات (للأدمن)
  // ═══════════════════════════════════════════════════════════════

  Future<List<Section>> _getAllSections() async {
    try {
      // جمع جميع الأقسام من Firebase
      final snapshot = await _firestore.collection('sections').get();
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جمع الأقسام: $e');
      return [];
    }
  }

  Future<List<Subject>> _getAllSubjects() async {
    try {
      // جمع جميع المواد من Firebase
      final snapshot = await _firestore.collection('subjects').get();
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جمع المواد: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> _getAllVideoSections() async {
    try {
      // جمع جميع أقسام الفيديو من Firebase
      final snapshot = await _firestore.collection('video_sections').get();
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جمع أقسام الفيديو: $e');
      return [];
    }
  }

  Future<List<VideoSubject>> _getAllVideoSubjects() async {
    try {
      // جمع جميع مواد الفيديو من Firebase
      final snapshot = await _firestore.collection('video_subjects').get();
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جمع مواد الفيديو: $e');
      return [];
    }
  }

  Future<List<Subject>> _getAllPaidSubjects() async {
    try {
      // جمع المواد المدفوعة فقط
      final allSubjects = await _getAllSubjects();
      debugPrint('🔍 [DEBUG] _getAllPaidSubjects():');
      debugPrint('   - إجمالي مواد الاختبارات: ${allSubjects.length}');

      for (final subject in allSubjects) {
        debugPrint(
          '   - ${subject['name']}: isFree=${subject['isFree']} (ID: ${subject['id']})',
        );
      }

      final paidSubjects = allSubjects
          .where((subject) => subject['isFree'] != true)
          .toList();
      debugPrint('   - مواد الاختبارات المدفوعة: ${paidSubjects.length}');

      return paidSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في جمع المواد المدفوعة: $e');
      return [];
    }
  }

  Future<List<VideoSubject>> _getAllPaidVideoSubjects() async {
    try {
      // جمع مواد الفيديو المدفوعة فقط
      final allSubjects = await _getAllVideoSubjects();
      debugPrint('🔍 [DEBUG] _getAllPaidVideoSubjects():');
      debugPrint('   - إجمالي مواد الفيديو: ${allSubjects.length}');

      for (final subject in allSubjects) {
        debugPrint(
          '   - ${subject['name']}: isFree=${subject['isFree']} (ID: ${subject['id']})',
        );
      }

      final paidVideoSubjects = allSubjects
          .where((subject) => subject['isFree'] != true)
          .toList();
      debugPrint('   - مواد الفيديو المدفوعة: ${paidVideoSubjects.length}');

      return paidVideoSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في جمع مواد الفيديو المدفوعة: $e');
      return [];
    }
  }

  Future<List<Subject>> _getAllFreeSubjects() async {
    try {
      // جمع المواد المجانية فقط
      final allSubjects = await _getAllSubjects();
      debugPrint('🔍 [DEBUG] _getAllFreeSubjects():');
      debugPrint('   - إجمالي مواد الاختبارات: ${allSubjects.length}');

      for (final subject in allSubjects) {
        debugPrint(
          '   - ${subject['name']}: isFree=${subject['isFree']} (ID: ${subject['id']})',
        );
      }

      final freeSubjects = allSubjects
          .where((subject) => subject['isFree'] == true)
          .toList();
      debugPrint('   - مواد الاختبارات المجانية: ${freeSubjects.length}');

      return freeSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في جمع المواد المجانية: $e');
      return [];
    }
  }

  Future<List<VideoSubject>> _getAllFreeVideoSubjects() async {
    try {
      // جمع مواد الفيديو المجانية فقط
      final allSubjects = await _getAllVideoSubjects();
      final allVideoSections = await _getAllVideoSections();

      debugPrint('🔍 [DEBUG] _getAllFreeVideoSubjects():');
      debugPrint('   - إجمالي مواد الفيديو: ${allSubjects.length}');
      debugPrint('   - إجمالي أقسام الفيديو: ${allVideoSections.length}');

      for (final subject in allSubjects) {
        final sectionId = subject['sectionId'] as String? ?? '';

        // البحث عن القسم المرتبط لتحديد isFree
        final relatedSection = allVideoSections.firstWhere(
          (section) => section['id'] == sectionId,
          orElse: () => {'isFree': false},
        );

        // تصحيح خاصية isFree بناءً على القسم
        final originalIsFree = subject['isFree'] ?? false;
        final sectionIsFree = relatedSection['isFree'] ?? false;

        if (originalIsFree != sectionIsFree) {
          debugPrint(
            '🔧 تصحيح مادة فيديو: ${subject['name']} - isFree: $originalIsFree → $sectionIsFree',
          );
          subject['isFree'] = sectionIsFree;
        }

        debugPrint(
          '   - ${subject['name']}: isFree=${subject['isFree']} (ID: ${subject['id']}, Section: $sectionId)',
        );
      }

      final freeSubjects = allSubjects
          .where((subject) => (subject['isFree'] ?? false) == true)
          .toList();
      debugPrint('   - مواد الفيديو المجانية: ${freeSubjects.length}');

      return freeSubjects;
    } catch (e) {
      debugPrint('❌ خطأ في جمع مواد الفيديو المجانية: $e');
      return [];
    }
  }

  Future<bool> _generateSubjectDocument(Subject subject, bool isFree) async {
    try {
      debugPrint('🔄 إنشاء وثيقة مادة: ${subject['name']}');

      final prefix = isFree ? _freeSubjectPrefix : _subjectPrefix;
      final docId = prefix + subject['id'];

      // جمع بيانات المادة الكاملة
      debugPrint('🔄 جمع بيانات المادة: ${subject['name']}');
      final units = await _getUnitsForSubject(subject['id']);
      final lessons = await _getLessonsForSubject(subject['id']);
      final questions = await _getQuestionsForSubject(subject['id']);

      debugPrint('📊 تم جمع البيانات:');
      debugPrint('   - الوحدات: ${units.length}');
      debugPrint('   - الدروس: ${lessons.length}');
      debugPrint('   - الأسئلة: ${questions.length}');

      // إنشاء وثيقة المادة
      final subjectData = {
        'version': DateTime.now().millisecondsSinceEpoch,
        'lastUpdated': FieldValue.serverTimestamp(),
        'subject_info': subject,
        'data': {'units': units, 'lessons': lessons, 'questions': questions},
      };

      await _firestore
          .collection(_unifiedCollection)
          .doc(docId)
          .set(subjectData);

      debugPrint('✅ تم إنشاء وثيقة مادة: ${subject['name']}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء وثيقة مادة ${subject['name']}: $e');
      return false;
    }
  }

  Future<bool> _generateVideoSubjectDocument(
    VideoSubject subject,
    bool isFree,
  ) async {
    try {
      debugPrint('🔄 إنشاء وثيقة مادة فيديو: ${subject['name']}');

      final prefix = isFree ? _freeVideoSubjectPrefix : _videoSubjectPrefix;
      final docId = prefix + subject['id'];

      // جمع بيانات مادة الفيديو الكاملة
      debugPrint('🔄 جمع بيانات مادة الفيديو: ${subject['name']}');
      debugPrint('🔍 معرف المادة: ${subject['id']}');

      final videoUnits = await _getVideoUnitsForSubject(subject['id']);
      final videoLessons = await _getVideoLessonsForSubject(subject['id']);
      final videos = await _getVideosForSubject(subject['id']);

      debugPrint('📊 تم جمع بيانات الفيديو:');
      debugPrint('   - وحدات الفيديو: ${videoUnits.length}');
      debugPrint('   - دروس الفيديو: ${videoLessons.length}');
      debugPrint('   - الفيديوهات: ${videos.length}');

      if (videoLessons.isEmpty && videos.isEmpty) {
        debugPrint(
          '⚠️ تحذير: لا توجد دروس فيديو أو فيديوهات للمادة ${subject['name']}',
        );
        debugPrint(
          '💡 تأكد من إضافة دروس فيديو وفيديوهات لهذه المادة في تطبيق الأدمن',
        );
      }

      // إنشاء وثيقة مادة الفيديو
      final subjectData = {
        'version': DateTime.now().millisecondsSinceEpoch,
        'lastUpdated': FieldValue.serverTimestamp(),
        'subject_info': subject,
        'data': {
          'video_units': videoUnits,
          'video_lessons': videoLessons,
          'videos': videos,
        },
      };

      await _firestore
          .collection(_unifiedCollection)
          .doc(docId)
          .set(subjectData);

      debugPrint('✅ تم إنشاء وثيقة مادة فيديو: ${subject['name']}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء وثيقة مادة فيديو ${subject['name']}: $e');
      return false;
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // 📈 إحصائيات التوفير
  // ═══════════════════════════════════════════════════════════════

  /// حساب التوفير المتوقع
  Map<String, dynamic> calculateSavings({
    required int totalStudents,
    required int totalSubjects,
    required int avgSubscribedSubjects,
  }) {
    // النظام القديم: وثيقة لكل طالب
    final oldDocuments = totalStudents;
    final oldReads = totalStudents; // قراءة واحدة لكل طالب

    // النظام الجديد: وثيقة عامة + وثيقة لكل مادة
    final newDocuments = 1 + totalSubjects; // وثيقة عامة + وثائق المواد
    final newReads =
        1 + avgSubscribedSubjects; // وثيقة عامة + المواد المشترك بها

    final documentSaving = ((oldDocuments - newDocuments) / oldDocuments * 100)
        .round();
    final readSaving = ((oldReads - newReads) / oldReads * 100).round();

    return {
      'old_documents': oldDocuments,
      'new_documents': newDocuments,
      'document_saving_percent': documentSaving,
      'old_reads_per_student': oldReads,
      'new_reads_per_student': newReads,
      'read_saving_percent': readSaving,
    };
  }

  /// طباعة إحصائيات التوفير
  void printSavingsExample() {
    final savings = calculateSavings(
      totalStudents: 10000,
      totalSubjects: 20,
      avgSubscribedSubjects: 6,
    );

    debugPrint('💾 إحصائيات التوفير المتوقعة:');
    debugPrint('📄 الوثائق:');
    debugPrint('   - النظام القديم: ${savings['old_documents']} وثيقة');
    debugPrint('   - النظام الجديد: ${savings['new_documents']} وثيقة');
    debugPrint('   - توفير: ${savings['document_saving_percent']}%');
    debugPrint('📖 القراءات لكل طالب:');
    debugPrint('   - النظام القديم: ${savings['old_reads_per_student']} قراءة');
    debugPrint('   - النظام الجديد: ${savings['new_reads_per_student']} قراءة');
    debugPrint('   - توفير: ${savings['read_saving_percent']}%');
  }

  /// جمع وحدات المادة
  Future<List<Map<String, dynamic>>> _getUnitsForSubject(
    String subjectId,
  ) async {
    try {
      final snapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جمع وحدات المادة $subjectId: $e');
      return [];
    }
  }

  /// جمع دروس المادة
  Future<List<Map<String, dynamic>>> _getLessonsForSubject(
    String subjectId,
  ) async {
    try {
      final snapshot = await _firestore
          .collection('lessons')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جمع دروس المادة $subjectId: $e');
      return [];
    }
  }

  /// جمع أسئلة المادة
  Future<List<Map<String, dynamic>>> _getQuestionsForSubject(
    String subjectId,
  ) async {
    try {
      final snapshot = await _firestore
          .collection('questions')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جمع أسئلة المادة $subjectId: $e');
      return [];
    }
  }

  /// جمع وحدات الفيديو للمادة
  Future<List<Map<String, dynamic>>> _getVideoUnitsForSubject(
    String subjectId,
  ) async {
    try {
      final snapshot = await _firestore
          .collection('video_units')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جمع وحدات الفيديو للمادة $subjectId: $e');
      return [];
    }
  }

  /// جمع دروس الفيديو للمادة
  Future<List<Map<String, dynamic>>> _getVideoLessonsForSubject(
    String subjectId,
  ) async {
    try {
      // أولاً: البحث بـ subjectId مباشرة
      var snapshot = await _firestore
          .collection('video_lessons')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      List<Map<String, dynamic>> videoLessons = snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();

      // إذا لم نجد دروس فيديو، ابحث من خلال الوحدات
      if (videoLessons.isEmpty) {
        debugPrint(
          '🔍 لم نجد دروس فيديو بـ subjectId، البحث من خلال الوحدات...',
        );

        // جمع وحدات الفيديو للمادة
        final videoUnits = await _getVideoUnitsForSubject(subjectId);

        // جمع دروس الفيديو لكل وحدة
        for (final unit in videoUnits) {
          final unitSnapshot = await _firestore
              .collection('video_lessons')
              .where('unitId', isEqualTo: unit['id'])
              .get();

          final unitVideoLessons = unitSnapshot.docs.map((doc) {
            final data = doc.data();
            return {...data, 'id': doc.id};
          }).toList();

          videoLessons.addAll(unitVideoLessons);
        }
      }

      debugPrint('✅ تم جمع ${videoLessons.length} درس فيديو للمادة $subjectId');
      return videoLessons;
    } catch (e) {
      debugPrint('❌ خطأ في جمع دروس الفيديو للمادة $subjectId: $e');
      return [];
    }
  }

  /// جمع فيديوهات المادة
  Future<List<Map<String, dynamic>>> _getVideosForSubject(
    String subjectId,
  ) async {
    try {
      // أولاً: البحث بـ subjectId مباشرة
      var snapshot = await _firestore
          .collection('videos')
          .where('subjectId', isEqualTo: subjectId)
          .get();

      List<Map<String, dynamic>> videos = snapshot.docs.map((doc) {
        final data = doc.data();
        return {...data, 'id': doc.id};
      }).toList();

      // إذا لم نجد فيديوهات، ابحث من خلال الوحدات والدروس
      if (videos.isEmpty) {
        debugPrint(
          '🔍 لم نجد فيديوهات بـ subjectId، البحث من خلال الوحدات والدروس...',
        );

        // جمع وحدات الفيديو للمادة
        final videoUnits = await _getVideoUnitsForSubject(subjectId);

        // جمع الفيديوهات لكل وحدة
        for (final unit in videoUnits) {
          final unitSnapshot = await _firestore
              .collection('videos')
              .where('unitId', isEqualTo: unit['id'])
              .get();

          final unitVideos = unitSnapshot.docs.map((doc) {
            final data = doc.data();
            return {...data, 'id': doc.id};
          }).toList();

          videos.addAll(unitVideos);
        }

        // جمع دروس الفيديو من خلال الوحدات (لتجنب الاستدعاء المتكرر)
        for (final unit in videoUnits) {
          final lessonsSnapshot = await _firestore
              .collection('video_lessons')
              .where('unitId', isEqualTo: unit['id'])
              .get();

          // جمع الفيديوهات لكل درس في هذه الوحدة
          for (final lessonDoc in lessonsSnapshot.docs) {
            final lessonSnapshot = await _firestore
                .collection('videos')
                .where('lessonId', isEqualTo: lessonDoc.id)
                .get();

            final lessonVideos = lessonSnapshot.docs.map((doc) {
              final data = doc.data();
              return {...data, 'id': doc.id};
            }).toList();

            videos.addAll(lessonVideos);
          }
        }
      }

      debugPrint('✅ تم جمع ${videos.length} فيديو للمادة $subjectId');
      return videos;
    } catch (e) {
      debugPrint('❌ خطأ في جمع فيديوهات المادة $subjectId: $e');
      return [];
    }
  }

  /// تحويل جميع Timestamp إلى int (milliseconds) لتجنب مشاكل JSON
  Map<String, dynamic> _convertTimestampsToInt(Map<String, dynamic> data) {
    debugPrint('🔄 [DEBUG] _convertTimestampsToInt() بدء التحويل...');
    debugPrint('   - عدد المفاتيح: ${data.keys.length}');

    final result = <String, dynamic>{};
    int timestampCount = 0;

    data.forEach((key, value) {
      if (value is Timestamp) {
        // تحويل Timestamp إلى milliseconds
        result[key] = value.millisecondsSinceEpoch;
        timestampCount++;
        debugPrint(
          '   - تم تحويل Timestamp: $key → ${value.millisecondsSinceEpoch}',
        );
      } else if (value is Map<String, dynamic>) {
        // تحويل المخططات المتداخلة
        result[key] = _convertTimestampsToInt(value);
      } else if (value is List) {
        // تحويل القوائم
        result[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _convertTimestampsToInt(item);
          } else if (item is Timestamp) {
            timestampCount++;
            debugPrint(
              '   - تم تحويل Timestamp في قائمة: ${item.millisecondsSinceEpoch}',
            );
            return item.millisecondsSinceEpoch;
          }
          return item;
        }).toList();
      } else {
        // نسخ القيم الأخرى كما هي
        result[key] = value;
      }
    });

    debugPrint('✅ [DEBUG] انتهى التحويل - تم تحويل $timestampCount Timestamp');
    return result;
  }
}
