import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// خدمة حماية الشاشة من التسجيل والتقاط الصور
class ScreenProtectionService {
  static final ScreenProtectionService _instance =
      ScreenProtectionService._internal();
  static ScreenProtectionService get instance => _instance;
  ScreenProtectionService._internal();

  static const MethodChannel _channel = MethodChannel('screen_protection');

  bool _isProtectionEnabled = false;
  bool _isInitialized = false;
  bool _isRecordingDetected = false;
  DateTime? _lastCheck;

  /// تهيئة خدمة حماية الشاشة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (!kIsWeb && Platform.isAndroid) {
        await _initializeAndroid();
      } else if (!kIsWeb && Platform.isWindows) {
        await _initializeWindows();
      }

      _isInitialized = true;
      debugPrint('🛡️ تم تهيئة خدمة حماية الشاشة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة حماية الشاشة: $e');
    }
  }

  /// تهيئة الحماية للأندرويد
  Future<void> _initializeAndroid() async {
    try {
      // يمكن إضافة كود native للأندرويد هنا
      debugPrint('🤖 تهيئة حماية الشاشة للأندرويد');
    } catch (e) {
      debugPrint('خطأ في تهيئة حماية الأندرويد: $e');
    }
  }

  /// تهيئة الحماية للويندوز
  Future<void> _initializeWindows() async {
    try {
      // يمكن إضافة كود native للويندوز هنا
      debugPrint('🖥️ تهيئة حماية الشاشة للويندوز');
    } catch (e) {
      debugPrint('خطأ في تهيئة حماية الويندوز: $e');
    }
  }

  /// تفعيل حماية الشاشة
  Future<void> enableProtection() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      if (!kIsWeb && Platform.isAndroid) {
        await _enableAndroidProtection();
      } else if (!kIsWeb && Platform.isWindows) {
        await _enableWindowsProtection();
      }

      _isProtectionEnabled = true;
      debugPrint('🛡️ تم تفعيل حماية الشاشة');
    } catch (e) {
      debugPrint('❌ خطأ في تفعيل حماية الشاشة: $e');
    }
  }

  /// تفعيل الحماية للأندرويد
  Future<void> _enableAndroidProtection() async {
    try {
      // تفعيل FLAG_SECURE لمنع تسجيل الشاشة
      final result = await _channel.invokeMethod('enableProtection');
      if (result == true) {
        debugPrint('✅ تم تفعيل FLAG_SECURE للأندرويد');
      }

      // منع لقطات الشاشة والتسجيل
      await SystemChrome.setApplicationSwitcherDescription(
        const ApplicationSwitcherDescription(
          label: 'Smart Edu',
          primaryColor: 0xFF000000,
        ),
      );

      // يمكن إضافة المزيد من الحماية هنا
      debugPrint('🤖 تم تفعيل حماية الأندرويد');
    } catch (e) {
      debugPrint('خطأ في تفعيل حماية الأندرويد: $e');
    }
  }

  /// تفعيل الحماية للويندوز
  Future<void> _enableWindowsProtection() async {
    try {
      // يمكن إضافة حماية خاصة بالويندوز هنا
      debugPrint('🖥️ تم تفعيل حماية الويندوز');
    } catch (e) {
      debugPrint('خطأ في تفعيل حماية الويندوز: $e');
    }
  }

  /// إلغاء تفعيل حماية الشاشة
  Future<void> disableProtection() async {
    try {
      if (!kIsWeb && Platform.isAndroid) {
        await _disableAndroidProtection();
      } else if (!kIsWeb && Platform.isWindows) {
        await _disableWindowsProtection();
      }

      _isProtectionEnabled = false;
      debugPrint('🔓 تم إلغاء تفعيل حماية الشاشة');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء تفعيل حماية الشاشة: $e');
    }
  }

  /// إلغاء تفعيل الحماية للأندرويد
  Future<void> _disableAndroidProtection() async {
    try {
      // إلغاء FLAG_SECURE
      final result = await _channel.invokeMethod('disableProtection');
      if (result == true) {
        debugPrint('✅ تم إلغاء FLAG_SECURE للأندرويد');
      }

      debugPrint('🤖 تم إلغاء تفعيل حماية الأندرويد');
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل حماية الأندرويد: $e');
    }
  }

  /// إلغاء تفعيل الحماية للويندوز
  Future<void> _disableWindowsProtection() async {
    try {
      debugPrint('🖥️ تم إلغاء تفعيل حماية الويندوز');
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل حماية الويندوز: $e');
    }
  }

  /// فحص حالة الحماية
  bool get isProtectionEnabled => _isProtectionEnabled;

  /// فحص ما إذا كان التسجيل نشطاً (للأندرويد)
  Future<bool> isRecordingActive() async {
    try {
      if (!kIsWeb && Platform.isAndroid) {
        return await _checkAndroidRecording();
      } else if (!kIsWeb && Platform.isWindows) {
        return await _checkWindowsRecording();
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في فحص التسجيل: $e');
      return false;
    }
  }

  /// فحص التسجيل للأندرويد
  Future<bool> _checkAndroidRecording() async {
    try {
      // يمكن إضافة فحص أكثر تقدماً هنا
      return false;
    } catch (e) {
      debugPrint('خطأ في فحص تسجيل الأندرويد: $e');
      return false;
    }
  }

  /// فحص التسجيل للويندوز - فحص حقيقي للعمليات النشطة
  Future<bool> _checkWindowsRecording() async {
    try {
      // منع الفحص المتكرر السريع
      final now = DateTime.now();
      if (_lastCheck != null && now.difference(_lastCheck!).inSeconds < 2) {
        return _isRecordingDetected;
      }
      _lastCheck = now;

      debugPrint('🔍 فحص العمليات النشطة للكشف عن تسجيل الشاشة...');

      // قائمة العمليات المشبوهة الشائعة
      final suspiciousProcesses = [
        'obs64.exe', 'obs32.exe', 'obs.exe',
        'bandicam.exe', 'bdcam.exe',
        'fraps.exe', 'fraps64.exe',
        'camtasia.exe', 'camtasiastudio.exe',
        'screenrec.exe', 'screenrecorder.exe',
        'ffmpeg.exe', 'ffplay.exe',
        'vlc.exe', 'vlc-cache-gen.exe',
        'potplayer.exe', 'potplayer64.exe',
        'kmplayer.exe', 'kmplayer64.exe',
        'nvidia-share.exe', 'nvcontainer.exe', // NVIDIA ShadowPlay
        'gamebar.exe', 'gamebarftserver.exe', // Windows Game Bar
        'xbox.exe', 'xboxgamebar.exe',
        'discord.exe', 'discordptb.exe', // Discord screen share
        'teams.exe', 'msteams.exe', // Microsoft Teams
        'zoom.exe', 'zoomvideocommunications.exe',
        'skype.exe', 'lync.exe',
        'webex.exe', 'webexmta.exe',
        'anydesk.exe', 'teamviewer.exe',
        'chrome.exe',
        'firefox.exe',
        'msedge.exe', // Browsers with screen capture
        'streamlabs.exe', 'xsplit.exe',
        'action.exe', 'mirillis.exe',
        'dxtory.exe', 'dxtory2.exe',
        'playclaw.exe', 'playclaw6.exe',
        'shadowplay.exe', 'geforce.exe',
        'radeonrelive.exe', 'amd.exe',
      ];

      // تشغيل أمر tasklist للحصول على العمليات النشطة
      final result = await Process.run('tasklist', [
        '/fo',
        'csv',
      ], runInShell: true);

      if (result.exitCode == 0) {
        final output = result.stdout.toString().toLowerCase();

        for (final process in suspiciousProcesses) {
          if (output.contains(process.toLowerCase())) {
            debugPrint('⚠️ تم اكتشاف عملية مشبوهة: $process');
            _isRecordingDetected = true;
            return true;
          }
        }

        // فحص إضافي للكلمات المفتاحية المشبوهة
        final suspiciousKeywords = [
          'record',
          'capture',
          'stream',
          'broadcast',
          'screen',
          'video',
          'cam',
          'rec',
        ];

        for (final keyword in suspiciousKeywords) {
          if (output.contains('$keyword.exe') ||
              output.contains('${keyword}er.exe')) {
            debugPrint('⚠️ تم اكتشاف عملية مشبوهة تحتوي على: $keyword');
            _isRecordingDetected = true;
            return true;
          }
        }

        debugPrint('✅ لم يتم اكتشاف أي عمليات تسجيل مشبوهة');
        _isRecordingDetected = false;
        return false;
      } else {
        debugPrint('❌ فشل في تشغيل أمر tasklist: ${result.stderr}');
        // في حالة الفشل، نفترض وجود تهديد للأمان
        _isRecordingDetected = true;
        return true;
      }
    } catch (e) {
      debugPrint('❌ خطأ في فحص تسجيل الويندوز: $e');
      // في حالة الخطأ، نفترض وجود تهديد للأمان
      _isRecordingDetected = true;
      return true;
    }
  }

  /// فحص مستمر للتسجيل أثناء تشغيل الفيديو
  Future<bool> performContinuousRecordingCheck() async {
    try {
      if (!kIsWeb && Platform.isWindows) {
        return await _checkWindowsRecording();
      } else if (!kIsWeb && Platform.isAndroid) {
        return await _checkAndroidRecording();
      }
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في الفحص المستمر: $e');
      return true; // في حالة الخطأ، نفترض وجود تهديد
    }
  }

  /// تطبيق حماية مؤقتة أثناء تشغيل الفيديو
  Future<void> applyVideoProtection() async {
    await enableProtection();

    // إضافة حماية إضافية أثناء تشغيل الفيديو
    if (!kIsWeb && Platform.isAndroid) {
      try {
        // منع التطبيق من الظهور في recent apps
        await SystemChrome.setApplicationSwitcherDescription(
          const ApplicationSwitcherDescription(
            label: '',
            primaryColor: 0xFF000000,
          ),
        );
      } catch (e) {
        debugPrint('خطأ في تطبيق حماية الفيديو: $e');
      }
    }

    // بدء الفحص المستمر
    debugPrint('🛡️ بدء الفحص المستمر لتسجيل الشاشة...');
  }

  /// إزالة الحماية المؤقتة بعد انتهاء الفيديو
  Future<void> removeVideoProtection() async {
    await disableProtection();

    // إعادة التطبيق للحالة العادية
    if (!kIsWeb && Platform.isAndroid) {
      try {
        await SystemChrome.setApplicationSwitcherDescription(
          const ApplicationSwitcherDescription(
            label: 'Smart Edu',
            primaryColor: 0xFF2196F3,
          ),
        );
      } catch (e) {
        debugPrint('خطأ في إزالة حماية الفيديو: $e');
      }
    }
  }
}
