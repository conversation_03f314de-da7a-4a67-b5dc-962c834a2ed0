import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/adaptive_sizing.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/student_data_service.dart';
import '../../../../shared/services/share_service.dart';
import '../../../../shared/services/data_distribution_service.dart';

import '../../../../shared/utils/text_direction_helper.dart';
import 'subject_units_page.dart';

class QuestionsViewerPage extends StatefulWidget {
  final String title;
  final Subject subject;
  final String? unitId;
  final String? lessonId;
  final QuestionFilterType questionType;
  final String? singleQuestionId; // معرف السؤال الواحد للمشاركة
  final bool isFreeAccess; // هل هذا وصول مجاني؟

  const QuestionsViewerPage({
    super.key,
    required this.title,
    required this.subject,
    this.unitId,
    this.lessonId,
    required this.questionType,
    this.singleQuestionId,
    this.isFreeAccess = false,
  });

  @override
  State<QuestionsViewerPage> createState() => _QuestionsViewerPageState();
}

class _QuestionsViewerPageState extends State<QuestionsViewerPage> {
  final DataDistributionService _dataService = DataDistributionService.instance;
  List<Question> _questions = [];
  final Map<String, List<String>> _userAnswers = {};
  final Map<String, bool> _questionResults = {};
  final Map<String, bool> _favoriteQuestions = {};
  final Map<String, String> _questionNotes = {};
  bool _isLoading = false; // ابدأ بدون تحميل - سيتم تعيينه حسب الحاجة
  bool _showResults = false;

  // متغيرات المؤقت
  Timer? _timer;
  int _remainingSeconds = 0;
  bool _isTimerActive = false;

  // إحصائيات
  int get _totalQuestions => _questions.length;
  int get _correctAnswers =>
      _questionResults.values.where((result) => result).length;
  int get _wrongAnswers =>
      _questionResults.values.where((result) => !result).length;

  @override
  void initState() {
    super.initState();
    _loadQuestions();
    _loadUserData();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  /// تحميل بيانات المستخدم المحفوظة
  Future<void> _loadUserData() async {
    for (final question in _questions) {
      // تحميل حالة المفضلة
      final isFavorite = await StudentDataService.instance.isFavorite(
        question.id,
        widget.subject.id,
      );
      _favoriteQuestions[question.id] = isFavorite;

      // تحميل الملاحظات
      final note = await StudentDataService.instance.getQuestionNote(
        question.id,
      );
      if (note.isNotEmpty) {
        _questionNotes[question.id] = note;
      }

      // تحميل النتائج
      final result = await StudentDataService.instance.getQuestionResult(
        question.id,
      );
      if (result != null) {
        _questionResults[question.id] = result;
      }

      // تحميل الإجابات
      final answers = await StudentDataService.instance
          .getUserAnswersForQuestion(question.id);
      if (answers.isNotEmpty) {
        _userAnswers[question.id] = answers;
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadQuestions() async {
    try {
      List<Question> questions = [];

      switch (widget.questionType) {
        case QuestionFilterType.unit:
          if (widget.unitId != null) {
            questions = await ExamService.instance.getQuestionsByUnit(
              widget.unitId!,
              false,
            );
            debugPrint(
              'تم تحميل ${questions.length} سؤال للوحدة ${widget.unitId}',
            );
          }
          break;
        case QuestionFilterType.lesson:
          if (widget.lessonId != null) {
            questions = await ExamService.instance.getQuestionsByLesson(
              widget.lessonId!,
              false,
            );
            debugPrint(
              'تم تحميل ${questions.length} سؤال للدرس ${widget.lessonId}',
            );
          }
          break;
        case QuestionFilterType.course:
          // تحميل أسئلة الدورات
          questions = await ExamService.instance.getCourseQuestionsBySubject(
            widget.subject.id,
          );
          debugPrint(
            'تم تحميل ${questions.length} سؤال دورة للمادة ${widget.subject.name}',
          );
          break;
        case QuestionFilterType.favorite:
          // تحميل الأسئلة المفضلة لهذه المادة
          final favoriteIds = await StudentDataService.instance
              .getFavoriteQuestions(widget.subject.id);
          if (favoriteIds.isNotEmpty) {
            // تحميل تفاصيل الأسئلة المفضلة
            questions = await ExamService.instance.getQuestionsByIds(
              favoriteIds,
            );

            // تصفية حسب الوحدة أو الدرس إذا تم تحديدها
            if (widget.unitId != null) {
              questions = questions
                  .where((q) => q.unitId == widget.unitId)
                  .toList();
              debugPrint(
                'تم تصفية ${questions.length} سؤال مفضل للوحدة ${widget.unitId}',
              );
            } else if (widget.lessonId != null) {
              questions = questions
                  .where((q) => q.lessonId == widget.lessonId)
                  .toList();
              debugPrint(
                'تم تصفية ${questions.length} سؤال مفضل للدرس ${widget.lessonId}',
              );
            } else {
              debugPrint('تم تحميل ${questions.length} سؤال مفضل');
            }
          }
          break;
        case QuestionFilterType.wrong:
          // تحميل الأسئلة الخاطئة لهذه المادة
          final wrongIds = await StudentDataService.instance.getWrongQuestions(
            widget.subject.id,
          );
          if (wrongIds.isNotEmpty) {
            // تحميل تفاصيل الأسئلة الخاطئة
            questions = await ExamService.instance.getQuestionsByIds(wrongIds);

            // تصفية حسب الوحدة أو الدرس إذا تم تحديدها
            if (widget.unitId != null) {
              questions = questions
                  .where((q) => q.unitId == widget.unitId)
                  .toList();
              debugPrint(
                'تم تصفية ${questions.length} سؤال خاطئ للوحدة ${widget.unitId}',
              );
            } else if (widget.lessonId != null) {
              questions = questions
                  .where((q) => q.lessonId == widget.lessonId)
                  .toList();
              debugPrint(
                'تم تصفية ${questions.length} سؤال خاطئ للدرس ${widget.lessonId}',
              );
            } else {
              debugPrint('تم تحميل ${questions.length} سؤال خاطئ');
            }
          }
          break;
        case QuestionFilterType.courseByUnit:
          // تحميل أسئلة الدورات للوحدة المحددة
          if (widget.unitId != null) {
            questions = await ExamService.instance.getQuestionsByUnit(
              widget.unitId!,
              true, // أسئلة دورات فقط
            );
            debugPrint(
              'تم تحميل ${questions.length} سؤال دورة للوحدة ${widget.unitId}',
            );
          }
          break;
        case QuestionFilterType.courseByLesson:
          // تحميل أسئلة الدورات للدرس المحدد
          if (widget.lessonId != null) {
            questions = await ExamService.instance.getQuestionsByLesson(
              widget.lessonId!,
              true, // أسئلة دورات فقط
            );
            debugPrint(
              'تم تحميل ${questions.length} سؤال دورة للدرس ${widget.lessonId}',
            );
          }
          break;
        case QuestionFilterType.single:
          // تحميل سؤال واحد محدد للمشاركة
          if (widget.singleQuestionId != null) {
            final question = await ExamService.instance.getQuestionById(
              widget.singleQuestionId!,
            );
            if (question != null) {
              questions = [question];
              debugPrint('تم تحميل السؤال المشارك: ${question.id}');
            } else {
              debugPrint(
                'لم يتم العثور على السؤال المشارك: ${widget.singleQuestionId}',
              );
            }
          }
          break;
      }

      // طباعة تفاصيل الأسئلة للتشخيص
      if (questions.isNotEmpty) {
        debugPrint('الأسئلة المحملة:');
        for (int i = 0; i < questions.length; i++) {
          final preview = questions[i].questionText.length > 50
              ? '${questions[i].questionText.substring(0, 50)}...'
              : questions[i].questionText;
          debugPrint('السؤال ${i + 1}: $preview');
        }
      } else {
        debugPrint('لم يتم العثور على أسئلة للمعايير المحددة');
        debugPrint('نوع السؤال: ${widget.questionType}');
        debugPrint('معرف الوحدة: ${widget.unitId}');
        debugPrint('معرف الدرس: ${widget.lessonId}');
      }

      setState(() {
        _questions = questions;
        _isLoading = false;
      });

      // تحميل بيانات المستخدم بعد تحميل الأسئلة
      await _loadUserData();
    } catch (e) {
      debugPrint('خطأ في تحميل الأسئلة: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.title,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            if (_isTimerActive)
              Text(
                'الوقت المتبقي: ${_formatTime(_remainingSeconds)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _remainingSeconds <= 60 ? Colors.red : Colors.white70,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.getPrimaryGradient(context),
          ),
        ),
        foregroundColor: Colors.white,
        actions: [
          // زر المؤقت
          IconButton(
            onPressed: _isTimerActive ? _stopTimer : _showTimerDialog,
            icon: Icon(_isTimerActive ? Icons.stop : Icons.timer),
            tooltip: _isTimerActive ? 'إيقاف المؤقت' : 'تشغيل المؤقت',
          ),

          // زر التحديث
          IconButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadQuestions();
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث الأسئلة',
          ),
          SizedBox(width: 16.w),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _questions.isEmpty
          ? _buildEmptyState()
          : Column(
              children: [
                // شريط الإحصائيات
                _buildStatsBar(),

                // الأسئلة مع مؤشر السحب
                Expanded(
                  child: Stack(
                    children: [
                      ListView.builder(
                        itemCount: _questions.length,
                        itemBuilder: (context, index) {
                          return _buildQuestionCard(_questions[index], index);
                        },
                      ),
                    ],
                  ),
                ),

                // شريط الأزرار السفلي
                _buildBottomActionBar(),
              ],
            ),
    );
  }

  Widget _buildStatsBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // العدد الكلي
          _buildStatItem(
            icon: Icons.quiz_outlined,
            label: 'الكل',
            value: _totalQuestions.toString(),
            color: AppTheme.primaryColor,
          ),

          SizedBox(width: 16.w),

          // الصحيح
          _buildStatItem(
            icon: Icons.check_circle_outline,
            label: 'صحيح',
            value: _correctAnswers.toString(),
            color: AppTheme.successColor,
          ),

          SizedBox(width: 16.w),

          // الخاطئ
          _buildStatItem(
            icon: Icons.cancel_outlined,
            label: 'خاطئ',
            value: _wrongAnswers.toString(),
            color: AppTheme.errorColor,
          ),

          const Spacer(),

          // زر تصحيح الكل
          ElevatedButton.icon(
            onPressed: _checkAllAnswers,
            icon: Icon(Icons.fact_check, size: 16.sp),
            label: Text('تصحيح الكل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16.sp),
        SizedBox(width: 4.w),
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        SizedBox(width: 2.w),
        Text(
          label,
          style: TextStyle(fontSize: 12.sp, color: AppTheme.textSecondaryColor),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.quiz_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد أسئلة متاحة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الأسئلة قريباً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionCard(Question question, int index) {
    final isCorrect = _questionResults[question.id] ?? false;
    final isFavorite = _favoriteQuestions[question.id] ?? false;
    final hasNote = _questionNotes.containsKey(question.id);

    // تحديد عرض السؤال بناءً على نوع الجهاز
    final adaptive = AdaptiveSizing.instance;
    double questionWidth;

    switch (adaptive.deviceType) {
      case AdaptiveDeviceType.mobile:
        questionWidth = double.infinity; // عرض كامل على الموبايل
        break;
      case AdaptiveDeviceType.tablet:
        questionWidth =
            MediaQuery.of(context).size.width * 0.8; // 80% على التابلت
        break;
      case AdaptiveDeviceType.desktop:
        questionWidth =
            MediaQuery.of(context).size.width *
            0.45; // 45% على Windows لعرض أفضل
        break;
    }

    return Center(
      child: Container(
        width: questionWidth,
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Card(
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              gradient: LinearGradient(
                colors: [
                  AppTheme.getSurfaceColor(context),
                  AppTheme.getBackgroundColor(context),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // رقم السؤال
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  margin: EdgeInsets.only(bottom: 16.h),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    'السؤال ${index + 1}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),

                // نص السؤال
                _buildQuestionText(question),

                SizedBox(height: 20.h),

                // خيارات الإجابة
                _buildAnswerOptions(question),

                SizedBox(height: 20.h),

                // شريط الأزرار
                _buildQuestionActions(question, isCorrect, isFavorite, hasNote),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionText(Question question) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppTheme.getBackgroundColor(context),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: question.contentType == QuestionContentType.image
          ? _buildQuestionImage(question)
          : Text(
              question.questionText,
              textDirection: TextDirection.rtl, // من اليمين لليسار
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                height: 1.5,
              ),
            ),
    );
  }

  Widget _buildQuestionImage(Question question) {
    if (question.imageUrl.isEmpty) {
      return Container(
        height: 200.h,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image_not_supported, size: 48.sp, color: Colors.grey),
              SizedBox(height: 8.h),
              Text(
                'لا توجد صورة متاحة',
                style: TextStyle(color: Colors.grey, fontSize: 14.sp),
              ),
            ],
          ),
        ),
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(8.r),
      child: Image.network(
        question.imageUrl,
        width: double.infinity,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            height: 200.h,
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            height: 200.h,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, size: 48.sp, color: Colors.red),
                  SizedBox(height: 8.h),
                  Text(
                    'خطأ في تحميل الصورة',
                    style: TextStyle(color: Colors.red, fontSize: 14.sp),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnswerOptions(Question question) {
    final userAnswer = _userAnswers[question.id];
    final questionResult = _questionResults[question.id];
    final showResults = _showResults && questionResult != null;

    return Column(
      children: question.options.map((option) {
        final isSelected = userAnswer?.contains(option) ?? false;
        final isCorrectAnswer = question.correctAnswers.contains(option);

        // تحديد اللون بناءً على حالة النتيجة
        Color? backgroundColor;
        Color? borderColor;
        Color? textColor;
        Color? iconColor;
        IconData? icon;

        if (showResults) {
          if (isCorrectAnswer) {
            // الإجابة الصحيحة - أخضر دائماً
            backgroundColor = AppTheme.successColor.withValues(alpha: 0.1);
            borderColor = AppTheme.successColor;
            textColor = AppTheme.successColor;
            iconColor = Colors.white;
            icon = Icons.check;
          } else if (isSelected && !isCorrectAnswer) {
            // الإجابة المختارة الخاطئة - أحمر
            backgroundColor = AppTheme.errorColor.withValues(alpha: 0.1);
            borderColor = AppTheme.errorColor;
            textColor = AppTheme.errorColor;
            iconColor = Colors.white;
            icon = Icons.close;
          } else {
            // الإجابات الأخرى - رمادي
            backgroundColor = Colors.transparent;
            borderColor = AppTheme.dividerColor;
            textColor = AppTheme.textSecondaryColor;
          }
        } else {
          // قبل التصحيح - الألوان العادية
          if (isSelected) {
            backgroundColor = AppTheme.primaryColor.withValues(alpha: 0.1);
            borderColor = AppTheme.primaryColor;
            textColor = AppTheme.primaryColor;
            iconColor = Colors.white;
            icon = Icons.check;
          } else {
            backgroundColor = Colors.transparent;
            borderColor = AppTheme.dividerColor;
            textColor = AppTheme.textPrimaryColor;
          }
        }

        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: InkWell(
            onTap: showResults ? null : () => _selectAnswer(question, option),
            borderRadius: BorderRadius.circular(12.r),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: backgroundColor,
                border: Border.all(color: borderColor, width: 2),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                textDirection: TextDirection.rtl, // من اليمين لليسار
                children: [
                  Container(
                    width: 20.w,
                    height: 20.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: (isSelected || (showResults && isCorrectAnswer))
                          ? (showResults && isCorrectAnswer
                                ? AppTheme.successColor
                                : showResults && isSelected && !isCorrectAnswer
                                ? AppTheme.errorColor
                                : AppTheme.primaryColor)
                          : Colors.transparent,
                      border: Border.all(color: borderColor, width: 2),
                    ),
                    child:
                        (isSelected || (showResults && isCorrectAnswer)) &&
                            icon != null
                        ? Icon(icon, size: 12.sp, color: iconColor)
                        : null,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      option,
                      textDirection: TextDirection.rtl, // من اليمين لليسار
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: textColor,
                        fontWeight:
                            (isSelected || (showResults && isCorrectAnswer))
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildQuestionActions(
    Question question,
    bool isCorrect,
    bool isFavorite,
    bool hasNote,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 6. زر التوضيح (إذا كان متوفراً) - آخر زر من اليمين
        if (question.explanation.isNotEmpty)
          _buildCompactActionButton(
            icon: Icons.lightbulb_outline,
            label: 'توضيح',
            color: AppTheme.accentColor,
            onPressed: () => _showExplanation(question),
          )
        else
          // مساحة فارغة إذا لم يكن هناك توضيح
          SizedBox(width: 60.w),

        // 5. زر الإبلاغ
        _buildCompactActionButton(
          icon: Icons.report_outlined,
          label: 'إبلاغ',
          color: AppTheme.errorColor,
          onPressed: () => _reportQuestion(question),
        ),

        // 4. زر المشاركة
        _buildCompactActionButton(
          icon: Icons.share,
          label: 'مشاركة',
          color: Colors.blue,
          onPressed: () => _shareQuestion(question),
        ),

        // 3. زر المفضلة
        _buildCompactActionButton(
          icon: isFavorite ? Icons.star : Icons.star_outline,
          label: 'مفضلة',
          color: AppTheme.warningColor,
          onPressed: () => _toggleFavorite(question),
        ),

        // 2. زر الملاحظة (بنفسجي)
        _buildCompactActionButton(
          icon: hasNote ? Icons.note : Icons.note_add,
          label: 'ملاحظة',
          color: AppTheme.primaryColor,
          onPressed: () => _addNote(question),
        ),

        // 1. زر التصحيح (أخضر) - أول زر من اليمين
        _buildCompactActionButton(
          icon: Icons.fact_check,
          label: 'تصحيح',
          color: AppTheme.successColor,
          onPressed: () => _checkAnswer(question),
        ),
      ],
    );
  }

  Widget _buildCompactActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 24.sp, color: color),
              SizedBox(height: 6.h),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // مؤشر السؤال الحالي
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25.r),
              ),
              child: Text(
                '$_totalQuestions أسئلة',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ),

          SizedBox(width: 12.w),

          // زر إعادة التعيين
          IconButton(
            onPressed: _resetAnswers,
            icon: Icon(
              Icons.refresh,
              color: AppTheme.textSecondaryColor,
              size: 24.sp,
            ),
            tooltip: 'إعادة تعيين',
          ),
        ],
      ),
    );
  }

  // الدوال المساعدة

  void _selectAnswer(Question question, String option) {
    setState(() {
      if (question.type == QuestionType.multipleChoice ||
          question.type == QuestionType.trueFalse) {
        _userAnswers[question.id] = [option];
      }
    });
  }

  void _checkAnswer(Question question) async {
    final userAnswer = _userAnswers[question.id];
    if (userAnswer == null || userAnswer.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى اختيار إجابة أولاً')));
      return;
    }

    final isCorrect = question.isCorrectAnswers(userAnswer);
    setState(() {
      _questionResults[question.id] = isCorrect;
      _showResults = true;
    });

    // حفظ النتيجة والإجابة في التخزين المحلي
    await StudentDataService.instance.saveQuestionResult(
      question.id,
      isCorrect,
      widget.subject.id,
    );
    await StudentDataService.instance.saveUserAnswers(question.id, userAnswer);
  }

  void _checkAllAnswers() async {
    for (final question in _questions) {
      final userAnswer = _userAnswers[question.id];
      if (userAnswer != null && userAnswer.isNotEmpty) {
        final isCorrect = question.isCorrectAnswers(userAnswer);
        _questionResults[question.id] = isCorrect;

        // حفظ النتيجة والإجابة في التخزين المحلي
        await StudentDataService.instance.saveQuestionResult(
          question.id,
          isCorrect,
          widget.subject.id,
        );
        await StudentDataService.instance.saveUserAnswers(
          question.id,
          userAnswer,
        );
      }
    }
    setState(() {
      _showResults = true;
    });
    _showResultsDialog();
  }

  void _toggleFavorite(Question question) async {
    final newFavoriteState = !(_favoriteQuestions[question.id] ?? false);

    setState(() {
      _favoriteQuestions[question.id] = newFavoriteState;
    });

    // حفظ في التخزين المحلي
    if (newFavoriteState) {
      await StudentDataService.instance.addToFavorites(
        question.id,
        widget.subject.id,
      );
    } else {
      await StudentDataService.instance.removeFromFavorites(
        question.id,
        widget.subject.id,
      );
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            newFavoriteState
                ? 'تم إضافة السؤال للمفضلة'
                : 'تم إزالة السؤال من المفضلة',
          ),
          backgroundColor: newFavoriteState
              ? AppTheme.successColor
              : AppTheme.textSecondaryColor,
        ),
      );
    }
  }

  void _addNote(Question question) {
    final currentNote = _questionNotes[question.id] ?? '';

    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController(text: currentNote);
        return AlertDialog(
          title: const Text('إضافة ملاحظة'),
          content: TextField(
            controller: controller,
            maxLines: 3,
            decoration: const InputDecoration(
              hintText: 'اكتب ملاحظتك هنا...',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final noteText = controller.text.trim();
                setState(() {
                  if (noteText.isNotEmpty) {
                    _questionNotes[question.id] = noteText;
                  } else {
                    _questionNotes.remove(question.id);
                  }
                });

                // حفظ في التخزين المحلي
                await StudentDataService.instance.saveQuestionNote(
                  question.id,
                  noteText,
                );

                if (mounted && context.mounted) {
                  Navigator.pop(context);
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  void _reportQuestion(Question question) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إبلاغ عن خطأ'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('رقم السؤال للإبلاغ عن الخطأ:'),
            const SizedBox(height: 10),
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(5),
              ),
              child: SelectableText(
                question.id,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'monospace',
                ),
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'انسخ رقم السؤال أولاً، ثم افتح تلغرام وأرسله إلى @Smart_Test1',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          // زر الإلغاء
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          const SizedBox(width: 8),
          // الأزرار الرئيسية في صف واحد مع تصميم أنيق
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // زر نسخ رقم السؤال
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.primaryColor,
                      AppTheme.primaryColor.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ElevatedButton.icon(
                  onPressed: () async {
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    await Clipboard.setData(ClipboardData(text: question.id));

                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text('تم نسخ رقم السؤال'),
                          ],
                        ),
                        backgroundColor: Colors.green,
                        duration: const Duration(seconds: 2),
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.copy, size: 18),
                  label: const Text('نسخ'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // زر فتح تلغرام
              Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF0088CC), Color(0xFF005F99)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF0088CC).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ElevatedButton.icon(
                  onPressed: () async {
                    Navigator.pop(context);
                    const telegramUrl = 'https://t.me/Smart_Test1';
                    const telegramAppUrl = 'tg://resolve?domain=Smart_Test1';

                    try {
                      bool launched = false;

                      // محاولة فتح التطبيق مباشرة
                      try {
                        launched = await launchUrl(
                          Uri.parse(telegramAppUrl),
                          mode: LaunchMode.externalApplication,
                        );
                      } catch (e) {
                        launched = false;
                      }

                      // إذا فشل، محاولة فتح الرابط العادي
                      if (!launched) {
                        await launchUrl(
                          Uri.parse(telegramUrl),
                          mode: LaunchMode.externalApplication,
                        );
                      }
                    } catch (e) {
                      // في حالة الفشل، لا نفعل شيء
                    }
                  },
                  icon: const Icon(Icons.telegram, size: 18),
                  label: const Text('تلغرام'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _shareQuestion(Question question) async {
    try {
      await ShareService.instance.shareQuestion(
        context,
        question,
        widget.subject,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في مشاركة السؤال: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _showExplanation(Question question) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('توضيح الحل'),
        content: TextDirectionHelper.buildSmartText(
          question.explanation,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(height: 1.5),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _resetAnswers() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين'),
        content: const Text('هل تريد مسح جميع الإجابات والبدء من جديد؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _userAnswers.clear();
                _questionResults.clear();
                _showResults = false;
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('مسح الكل'),
          ),
        ],
      ),
    );
  }

  void _showResultsDialog() {
    final percentage = _totalQuestions > 0
        ? (_correctAnswers / _totalQuestions * 100)
        : 0;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نتائج الأسئلة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${percentage.toInt()}%',
              style: TextStyle(
                fontSize: 48.sp,
                fontWeight: FontWeight.bold,
                color: percentage >= 70
                    ? AppTheme.successColor
                    : AppTheme.errorColor,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    Text(
                      _totalQuestions.toString(),
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    Text('الكل', style: TextStyle(fontSize: 12.sp)),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      _correctAnswers.toString(),
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.successColor,
                      ),
                    ),
                    Text('صحيح', style: TextStyle(fontSize: 12.sp)),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      _wrongAnswers.toString(),
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.errorColor,
                      ),
                    ),
                    Text('خاطئ', style: TextStyle(fontSize: 12.sp)),
                  ],
                ),
              ],
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // وظائف المؤقت
  void _showTimerDialog() {
    final TextEditingController minutesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تشغيل المؤقت'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل مدة المؤقت بالدقائق:'),
            const SizedBox(height: 16),
            TextField(
              controller: minutesController,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              decoration: const InputDecoration(
                hintText: 'مثال: 15',
                border: OutlineInputBorder(),
                suffixText: 'دقيقة',
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final minutes = int.tryParse(minutesController.text);
              if (minutes != null && minutes > 0) {
                Navigator.pop(context);
                _startTimer(minutes);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى إدخال رقم صحيح أكبر من صفر'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('بدء المؤقت'),
          ),
        ],
      ),
    );
  }

  void _startTimer(int minutes) {
    _timer?.cancel();
    setState(() {
      _remainingSeconds = minutes * 60;
      _isTimerActive = true;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _isTimerActive = false;
          timer.cancel();
          _showTimeUpDialog();
        }
      });
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تشغيل المؤقت لمدة $minutes دقيقة'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _stopTimer() {
    _timer?.cancel();
    setState(() {
      _isTimerActive = false;
      _remainingSeconds = 0;
    });
  }

  void _showTimeUpDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('انتهى الوقت!'),
        content: const Text('لقد انتهت مدة المؤقت. سيتم عرض النتائج الآن.'),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showResultsDialog();
            },
            child: const Text('عرض النتائج'),
          ),
        ],
      ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
