import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/exam_model.dart';
import '../../../../shared/models/exam_result_model.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../widgets/exam_card_widget.dart';
import 'exam_taking_page.dart';
import 'exam_result_page.dart' as result_page;

class ExamsPage extends StatefulWidget {
  final String? subjectId;

  const ExamsPage({super.key, this.subjectId});

  @override
  State<ExamsPage> createState() => _ExamsPageState();
}

class _ExamsPageState extends State<ExamsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedSubjectId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          'الاختبارات',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.getPrimaryGradient(context),
          ),
        ),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'الاختبارات المتاحة'),
            Tab(text: 'نتائجي'),
          ],
        ),
      ),
      body: Consumer<SubscriptionService>(
        builder: (context, subscriptionService, child) {
          return Column(
            children: [
              // فلتر المواد
              _buildSubjectFilter(subscriptionService.subscribedSubjects),

              // محتوى التبويبات
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [_buildAvailableExamsTab(), _buildMyResultsTab()],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSubjectFilter(List<Subject> subjects) {
    if (subjects.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16.w),
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppTheme.warningColor),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    'يجب تفعيل اشتراك في المواد أولاً لعرض الاختبارات',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.warningColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('جميع المواد', null),
            SizedBox(width: 8.w),
            ...subjects.map(
              (subject) => Padding(
                padding: EdgeInsets.only(left: 8.w),
                child: _buildFilterChip(subject.name, subject.id),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String? subjectId) {
    final isSelected = _selectedSubjectId == subjectId;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedSubjectId = selected ? subjectId : null;
        });
      },
      selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryColor,
      labelStyle: TextStyle(
        color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  Widget _buildAvailableExamsTab() {
    return Consumer<SubscriptionService>(
      builder: (context, subscriptionService, child) {
        final subscribedSubjectIds = subscriptionService.subscribedSubjects
            .map((subject) => subject.id)
            .toList();

        if (subscribedSubjectIds.isEmpty) {
          return _buildEmptyState(
            'لا توجد مواد مفعلة',
            'قم بتفعيل اشتراك في المواد لعرض الاختبارات',
            Icons.school_outlined,
          );
        }

        return StreamBuilder<List<Exam>>(
          stream: _getFilteredExamsStream(subscribedSubjectIds),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return _buildErrorState(snapshot.error.toString());
            }

            final exams = snapshot.data ?? [];

            if (exams.isEmpty) {
              return _buildEmptyState(
                'لا توجد اختبارات متاحة',
                'لم يتم إنشاء اختبارات للمواد المفعلة بعد',
                Icons.quiz_outlined,
              );
            }

            return ListView.builder(
              padding: EdgeInsets.all(16.w),
              itemCount: exams.length,
              itemBuilder: (context, index) {
                final exam = exams[index];
                final subject = subscriptionService.subscribedSubjects
                    .firstWhere((s) => s.id == exam.subjectId);

                return Padding(
                  padding: EdgeInsets.only(bottom: 16.h),
                  child: ExamCardWidget(
                    exam: exam,
                    subject: subject,
                    onTakeExam: () => _takeExam(exam),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildMyResultsTab() {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return _buildEmptyState(
        'يجب تسجيل الدخول',
        'قم بتسجيل الدخول لعرض نتائجك',
        Icons.login_outlined,
      );
    }

    return StreamBuilder<List<ExamResult>>(
      stream: ExamService.instance.getUserExamResults(user.uid),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorState(snapshot.error.toString());
        }

        final results = snapshot.data ?? [];

        // Filter results based on selected subject
        final filteredResults = _selectedSubjectId == null
            ? results
            : results.where((result) {
                // We need to get the exam to check its subject
                // For now, we'll show all results if no subject filter is applied
                return true;
              }).toList();

        if (filteredResults.isEmpty) {
          return _buildEmptyState(
            'لا توجد نتائج',
            'لم تقم بأداء أي اختبارات بعد',
            Icons.quiz_outlined,
          );
        }

        return ListView.builder(
          padding: EdgeInsets.all(16.w),
          itemCount: filteredResults.length,
          itemBuilder: (context, index) {
            final result = filteredResults[index];
            return Padding(
              padding: EdgeInsets.only(bottom: 16.h),
              child: _buildResultCard(result),
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState(String title, String message, IconData icon) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64.sp,
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: AppTheme.errorColor),
            SizedBox(height: 16.h),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.errorColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Stream<List<Exam>> _getFilteredExamsStream(
    List<String> subscribedSubjectIds,
  ) {
    if (_selectedSubjectId != null) {
      return ExamService.instance.getAvailableExams(_selectedSubjectId!);
    } else {
      // Get exams for all subscribed subjects
      return Stream.fromFuture(
        Future.wait(
          subscribedSubjectIds.map(
            (subjectId) =>
                ExamService.instance.getAvailableExams(subjectId).first,
          ),
        ).then((examLists) {
          final allExams = <Exam>[];
          for (final examList in examLists) {
            allExams.addAll(examList);
          }
          // Sort by creation date
          allExams.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          return allExams;
        }),
      );
    }
  }

  Widget _buildResultCard(ExamResult result) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12.r),
        onTap: () => _viewResultDetails(result),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with exam info
              Row(
                children: [
                  Expanded(
                    child: FutureBuilder<Exam?>(
                      future: ExamService.instance.getExam(result.examId),
                      builder: (context, snapshot) {
                        final exam = snapshot.data;
                        return Text(
                          exam?.title ?? 'اختبار غير معروف',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimaryColor,
                              ),
                        );
                      },
                    ),
                  ),
                  _buildStatusBadge(result),
                ],
              ),

              SizedBox(height: 12.h),

              // Score and percentage
              Row(
                children: [
                  Expanded(
                    child: _buildScoreInfo(
                      'النتيجة',
                      '${result.earnedPoints}/${result.totalPoints}',
                      Icons.score_outlined,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: _buildScoreInfo(
                      'النسبة المئوية',
                      '${result.percentage.toInt()}%',
                      Icons.percent_outlined,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Additional info
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'المحاولة',
                      '${result.attemptNumber}',
                      Icons.repeat_outlined,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: _buildInfoItem(
                      'الوقت المستغرق',
                      _formatDuration(result.timeSpent),
                      Icons.timer_outlined,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Date
              Row(
                children: [
                  Icon(
                    Icons.calendar_today_outlined,
                    size: 16.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    _formatDate(result.completedAt ?? result.startedAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(ExamResult result) {
    Color backgroundColor;
    Color textColor;
    String text;

    if (result.isPassed) {
      backgroundColor = AppTheme.successColor.withValues(alpha: 0.1);
      textColor = AppTheme.successColor;
      text = 'نجح';
    } else {
      backgroundColor = AppTheme.errorColor.withValues(alpha: 0.1);
      textColor = AppTheme.errorColor;
      text = 'راسب';
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildScoreInfo(String label, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20.sp, color: AppTheme.primaryColor),
          SizedBox(height: 4.h),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16.sp, color: AppTheme.textSecondaryColor),
        SizedBox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _viewResultDetails(ExamResult result) async {
    try {
      final exam = await ExamService.instance.getExam(result.examId);
      if (exam != null && mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                result_page.ExamResultPage(result: result, exam: exam),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحميل تفاصيل النتيجة: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _takeExam(Exam exam) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => ExamTakingPage(exam: exam)),
    );
  }
}
