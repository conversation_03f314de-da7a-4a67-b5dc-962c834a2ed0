import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/single_read_data_service.dart';
import '../../../../shared/widgets/single_read_update_button.dart';
import '../../../../shared/widgets/theme_toggle_widget.dart';
import '../../../../shared/pages/theme_settings_page.dart';
import 'subscription_activation_page.dart';
import 'sections_page.dart';
import 'video_sections_page.dart';

/// الصفحة الرئيسية المبسطة للطالب - تستخدم النظام الجديد
class SimpleStudentHomePage extends StatefulWidget {
  const SimpleStudentHomePage({super.key});

  @override
  State<SimpleStudentHomePage> createState() => _SimpleStudentHomePageState();
}

class _SimpleStudentHomePageState extends State<SimpleStudentHomePage> {
  int _currentIndex = 2; // البدء من صفحة الفيديوهات

  final List<Widget> _pages = [
    const SubscriptionActivationPage(), // تفعيل الاشتراك
    const SectionsPage(), // الاختبارات (الأقسام)
    const VideoSectionsPage(), // الفيديوهات
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          'Smart Edu',
          style: Theme.of(
            context,
          ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.getPrimaryGradient(context),
          ),
        ),
        foregroundColor: Colors.white,
        actions: [
          // زر تبديل الثيم
          const AppBarThemeToggle(),
          // زر إعدادات الثيم
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ThemeSettingsPage(),
                ),
              );
            },
            tooltip: 'إعدادات المظهر',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Stack(
        children: [
          _pages[_currentIndex],
          // زر التحديث اليدوي - يظهر في جميع الصفحات
          const SingleReadUpdateButton(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: Colors.white,
          unselectedItemColor: Colors.white70,
          type: BottomNavigationBarType.fixed,
          selectedLabelStyle: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 10.sp,
            fontWeight: FontWeight.w400,
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.card_membership_outlined),
              activeIcon: Icon(Icons.card_membership),
              label: 'تفعيل الاشتراك',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.quiz_outlined),
              activeIcon: Icon(Icons.quiz),
              label: 'الاختبارات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.video_library_outlined),
              activeIcon: Icon(Icons.video_library),
              label: 'الفيديوهات',
            ),
          ],
        ),
      ),
    );
  }
}

/// صفحة عرض المواد المبسطة
class SimpleSubjectsPage extends StatelessWidget {
  const SimpleSubjectsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SingleReadDataService>(
      builder: (context, dataService, child) {
        final sections = dataService.sections;

        if (sections.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.book_outlined, size: 80, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد أقسام متاحة',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'اضغط على زر التحديث لتحميل البيانات',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الأقسام المتاحة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(height: 16.h),
              Expanded(
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16.w,
                    mainAxisSpacing: 16.h,
                    childAspectRatio: 1.2,
                  ),
                  itemCount: sections.length,
                  itemBuilder: (context, index) {
                    final section = sections[index];
                    return _buildSectionCard(context, section);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionCard(BuildContext context, section) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        borderRadius: BorderRadius.circular(16.r),
        onTap: () {
          // الانتقال إلى صفحة المواد في هذا القسم
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم النقر على قسم: ${section.name}')),
          );
        },
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryColor.withValues(alpha: 0.1),
                AppTheme.primaryColor.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 50.w,
                height: 50.h,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(25.r),
                ),
                child: Icon(Icons.folder, color: Colors.white, size: 24.sp),
              ),
              SizedBox(height: 12.h),
              Text(
                section.name,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 8.h),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: section.isFree
                      ? Colors.green.withValues(alpha: 0.2)
                      : AppTheme.primaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  section.isFree ? 'مجاني' : 'مدفوع',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: section.isFree
                        ? Colors.green
                        : AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
