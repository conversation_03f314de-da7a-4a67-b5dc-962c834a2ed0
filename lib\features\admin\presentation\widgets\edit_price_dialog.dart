import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/pricing_service.dart';
import '../../../../shared/models/subscription_price_model.dart';

class EditPriceDialog extends StatefulWidget {
  final SubscriptionPrice price;

  const EditPriceDialog({super.key, required this.price});

  @override
  State<EditPriceDialog> createState() => _EditPriceDialogState();
}

class _EditPriceDialogState extends State<EditPriceDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _priceController;
  late final TextEditingController _subjectNameController;

  late String _selectedCurrency;
  late bool _isActive;
  bool _isLoading = false;

  final List<String> _currencies = ['USD', 'SYP'];

  @override
  void initState() {
    super.initState();
    _priceController = TextEditingController(
      text: widget.price.price.toString(),
    );
    _subjectNameController = TextEditingController(
      text: widget.price.subjectName,
    );
    _selectedCurrency = widget.price.currency;
    _isActive = widget.price.isActive;
  }

  @override
  void dispose() {
    _priceController.dispose();
    _subjectNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.edit, color: AppTheme.primaryColor, size: 24.sp),
          SizedBox(width: 8.w),
          const Text('تعديل السعر'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // اسم المادة (للعرض فقط)
              TextFormField(
                controller: _subjectNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المادة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.book),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم المادة';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16.h),

              // إدخال السعر
              TextFormField(
                controller: _priceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'السعر',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال السعر';
                  }
                  final price = double.tryParse(value);
                  if (price == null || price <= 0) {
                    return 'يرجى إدخال سعر صحيح';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16.h),

              // اختيار العملة
              DropdownButtonFormField<String>(
                value: _selectedCurrency,
                decoration: const InputDecoration(
                  labelText: 'العملة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.currency_exchange),
                ),
                items: _currencies.map((currency) {
                  return DropdownMenuItem(
                    value: currency,
                    child: Text(_getCurrencyName(currency)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCurrency = value!;
                  });
                },
              ),
              SizedBox(height: 16.h),

              // حالة التفعيل
              SwitchListTile(
                title: const Text('نشط'),
                subtitle: Text(
                  _isActive ? 'السعر مفعل ومرئي للطلاب' : 'السعر غير مفعل',
                ),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                activeColor: AppTheme.successColor,
              ),

              SizedBox(height: 16.h),

              // معلومات إضافية
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات إضافية:',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'تم الإنشاء: ${_formatDate(widget.price.createdAt)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    if (widget.price.createdAt != widget.price.updatedAt)
                      Text(
                        'آخر تحديث: ${_formatDate(widget.price.updatedAt)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updatePrice,
          child: _isLoading
              ? SizedBox(
                  width: 20.w,
                  height: 20.w,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
        ),
      ],
    );
  }

  String _getCurrencyName(String currency) {
    switch (currency) {
      case 'USD':
        return 'دولار أمريكي (USD)';
      case 'SYP':
        return 'ليرة سورية (SYP)';
      default:
        return currency;
    }
  }

  Future<void> _updatePrice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final price = double.parse(_priceController.text);
      final subjectName = _subjectNameController.text.trim();

      final updatedPrice = widget.price.copyWith(
        subjectName: subjectName,
        price: price,
        currency: _selectedCurrency,
        isActive: _isActive,
        updatedAt: DateTime.now(),
      );

      final success = await context
          .read<PricingService>()
          .updateSubscriptionPrice(updatedPrice);

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث السعر بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في تحديث السعر'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
