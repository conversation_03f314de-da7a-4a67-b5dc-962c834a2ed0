package com.example.smart_test

import android.os.Bundle
import android.view.WindowManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "screen_protection"
    private var isProtectionEnabled = false

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "enableProtection" -> {
                    enableScreenProtection()
                    result.success(true)
                }
                "disableProtection" -> {
                    disableScreenProtection()
                    result.success(true)
                }
                "isProtectionEnabled" -> {
                    result.success(isProtectionEnabled)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun enableScreenProtection() {
        try {
            window.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            )
            isProtectionEnabled = true
            println("🛡️ تم تفعيل حماية الشاشة للأندرويد (FLAG_SECURE)")
        } catch (e: Exception) {
            println("❌ خطأ في تفعيل حماية الشاشة: ${e.message}")
        }
    }

    private fun disableScreenProtection() {
        try {
            window.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
            isProtectionEnabled = false
            println("✅ تم إلغاء حماية الشاشة للأندرويد")
        } catch (e: Exception) {
            println("❌ خطأ في إلغاء حماية الشاشة: ${e.message}")
        }
    }
}
