import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// نظام الأبعاد التكيفي للتطبيق
/// يتكيف مع أحجام الشاشات المختلفة (هاتف، تابلت، كمبيوتر)
class AdaptiveSizing {
  static AdaptiveSizing? _instance;
  static AdaptiveSizing get instance => _instance ??= AdaptiveSizing._();
  AdaptiveSizing._();

  /// نوع الجهاز الحالي
  AdaptiveDeviceType get deviceType {
    if (kIsWeb) return AdaptiveDeviceType.desktop;
    if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      return AdaptiveDeviceType.desktop;
    }
    if (Platform.isAndroid || Platform.isIOS) {
      // تحديد إذا كان تابلت أم هاتف بناءً على حجم الشاشة
      final shortestSide = ScreenUtil().screenWidth < ScreenUtil().screenHeight
          ? ScreenUtil().screenWidth
          : ScreenUtil().screenHeight;
      return shortestSide > 600
          ? AdaptiveDeviceType.tablet
          : AdaptiveDeviceType.mobile;
    }
    return AdaptiveDeviceType.mobile;
  }

  /// عامل التكبير للنصوص
  double get textScaleFactor {
    switch (deviceType) {
      case AdaptiveDeviceType.mobile:
        return 1.0;
      case AdaptiveDeviceType.tablet:
        return 1.1;
      case AdaptiveDeviceType.desktop:
        return 1.0; // تقليل حجم النص على Windows
    }
  }

  /// عامل التكبير للأيقونات
  double get iconScaleFactor {
    switch (deviceType) {
      case AdaptiveDeviceType.mobile:
        return 1.0;
      case AdaptiveDeviceType.tablet:
        return 1.1;
      case AdaptiveDeviceType.desktop:
        return 0.8; // تقليل حجم الأيقونات أكثر على Windows
    }
  }

  /// عامل التكبير للمسافات والحشو
  double get spacingScaleFactor {
    switch (deviceType) {
      case AdaptiveDeviceType.mobile:
        return 1.0;
      case AdaptiveDeviceType.tablet:
        return 1.1;
      case AdaptiveDeviceType.desktop:
        return 0.8; // تقليل المسافات على Windows
    }
  }

  /// عامل التكبير للأزرار
  double get buttonScaleFactor {
    switch (deviceType) {
      case AdaptiveDeviceType.mobile:
        return 1.0;
      case AdaptiveDeviceType.tablet:
        return 1.1;
      case AdaptiveDeviceType.desktop:
        return 0.9; // تقليل حجم الأزرار على Windows
    }
  }

  /// حجم النص التكيفي
  double adaptiveTextSize(double baseSize) {
    return (baseSize * textScaleFactor).sp;
  }

  /// حجم الأيقونة التكيفي
  double adaptiveIconSize(double baseSize) {
    return (baseSize * iconScaleFactor).sp;
  }

  /// المسافة التكيفية
  double adaptiveSpacing(double baseSpacing) {
    return (baseSpacing * spacingScaleFactor).w;
  }

  /// الحشو التكيفي
  EdgeInsets adaptivePadding(EdgeInsets basePadding) {
    return EdgeInsets.only(
      left: (basePadding.left * spacingScaleFactor).w,
      top: (basePadding.top * spacingScaleFactor).h,
      right: (basePadding.right * spacingScaleFactor).w,
      bottom: (basePadding.bottom * spacingScaleFactor).h,
    );
  }

  /// حجم الزر التكيفي
  Size adaptiveButtonSize(Size baseSize) {
    return Size(
      (baseSize.width * buttonScaleFactor).w,
      (baseSize.height * buttonScaleFactor).h,
    );
  }

  /// نصف قطر الحواف التكيفي
  double adaptiveBorderRadius(double baseRadius) {
    return (baseRadius * spacingScaleFactor).r;
  }

  /// عدد الأعمدة في الشبكة بناءً على نوع الجهاز
  int adaptiveGridColumns({
    int mobileColumns = 2,
    int tabletColumns = 3,
    int desktopColumns = 4,
  }) {
    switch (deviceType) {
      case AdaptiveDeviceType.mobile:
        return mobileColumns;
      case AdaptiveDeviceType.tablet:
        return tabletColumns;
      case AdaptiveDeviceType.desktop:
        return desktopColumns;
    }
  }

  /// نسبة العرض إلى الارتفاع للبطاقات
  double adaptiveCardAspectRatio({
    double mobileRatio = 0.85, // تقليل النسبة لإعطاء مساحة أكثر للارتفاع
    double tabletRatio = 1.0,
    double desktopRatio = 1.2,
  }) {
    switch (deviceType) {
      case AdaptiveDeviceType.mobile:
        return mobileRatio;
      case AdaptiveDeviceType.tablet:
        return tabletRatio;
      case AdaptiveDeviceType.desktop:
        return desktopRatio;
    }
  }

  /// المسافة بين العناصر في الشبكة
  double adaptiveGridSpacing({
    double mobileSpacing = 16.0,
    double tabletSpacing = 18.0,
    double desktopSpacing = 16.0, // تقليل المسافات على Windows
  }) {
    switch (deviceType) {
      case AdaptiveDeviceType.mobile:
        return mobileSpacing.w;
      case AdaptiveDeviceType.tablet:
        return tabletSpacing.w;
      case AdaptiveDeviceType.desktop:
        return desktopSpacing.w;
    }
  }

  /// الحد الأقصى لعرض المحتوى (للشاشات الكبيرة)
  double get maxContentWidth {
    switch (deviceType) {
      case AdaptiveDeviceType.mobile:
        return double.infinity;
      case AdaptiveDeviceType.tablet:
        return 800.w;
      case AdaptiveDeviceType.desktop:
        return 1200.w;
    }
  }

  /// هل الجهاز يدعم التمرير الأفقي
  bool get supportsHorizontalScrolling {
    return deviceType == AdaptiveDeviceType.desktop;
  }

  /// هل يجب استخدام التخطيط المضغوط
  bool get useCompactLayout {
    return deviceType == AdaptiveDeviceType.mobile;
  }

  /// هل يجب إظهار الشريط الجانبي
  bool get shouldShowSidebar {
    return deviceType == AdaptiveDeviceType.desktop;
  }

  /// معلومات الجهاز للتصحيح
  String get deviceInfo {
    return 'Device: ${deviceType.name}, '
        'Screen: ${ScreenUtil().screenWidth.toInt()}x${ScreenUtil().screenHeight.toInt()}, '
        'Text Scale: ${textScaleFactor.toStringAsFixed(1)}, '
        'Icon Scale: ${iconScaleFactor.toStringAsFixed(1)}';
  }
}

/// أنواع الأجهزة المدعومة
enum AdaptiveDeviceType { mobile, tablet, desktop }

/// امتداد لتسهيل استخدام الأبعاد التكيفية
extension AdaptiveExtensions on num {
  /// حجم النص التكيفي
  double get adaptiveText =>
      AdaptiveSizing.instance.adaptiveTextSize(toDouble());

  /// حجم الأيقونة التكيفي
  double get adaptiveIcon =>
      AdaptiveSizing.instance.adaptiveIconSize(toDouble());

  /// المسافة التكيفية
  double get adaptiveSpacing =>
      AdaptiveSizing.instance.adaptiveSpacing(toDouble());

  /// نصف قطر الحواف التكيفي
  double get adaptiveRadius =>
      AdaptiveSizing.instance.adaptiveBorderRadius(toDouble());
}

/// امتداد للحشو التكيفي
extension AdaptivePaddingExtensions on EdgeInsets {
  /// الحشو التكيفي
  EdgeInsets get adaptive => AdaptiveSizing.instance.adaptivePadding(this);
}
