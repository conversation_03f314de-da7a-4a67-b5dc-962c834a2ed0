import 'package:flutter/foundation.dart';

/// خدمة تسجيل الأحداث والأخطاء
class Logger {
  static const String _tag = 'SmartTest';

  /// تسجيل معلومات عامة
  static void info(String message) {
    if (kDebugMode) {
      print('[$_tag] INFO: $message');
    }
  }

  /// تسجيل تحذير
  static void warning(String message) {
    if (kDebugMode) {
      print('[$_tag] WARNING: $message');
    }
  }

  /// تسجيل خطأ
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      print('[$_tag] ERROR: $message');
      if (error != null) {
        print('[$_tag] Error details: $error');
      }
      if (stackTrace != null) {
        print('[$_tag] Stack trace: $stackTrace');
      }
    }
  }

  /// تسجيل نجاح عملية
  static void success(String message) {
    if (kDebugMode) {
      print('[$_tag] SUCCESS: $message');
    }
  }

  /// تسجيل بداية عملية
  static void start(String message) {
    if (kDebugMode) {
      print('[$_tag] START: $message');
    }
  }
}
