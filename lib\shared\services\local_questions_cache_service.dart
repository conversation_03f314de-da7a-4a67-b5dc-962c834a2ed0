import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/question_model.dart';
import 'subject_questions_service.dart';

/// خدمة التخزين المحلي للأسئلة
/// تدير تخزين أسئلة المواد محلياً وتتبع التحديثات
class LocalQuestionsCacheService {
  static final LocalQuestionsCacheService _instance =
      LocalQuestionsCacheService._internal();
  static LocalQuestionsCacheService get instance => _instance;
  LocalQuestionsCacheService._internal();

  /// تنظيف شامل للبيانات التالفة عند بدء التطبيق
  Future<void> cleanupOnStartup() async {
    try {
      debugPrint('🧹 بدء تنظيف البيانات التالفة...');
      final prefs = await SharedPreferences.getInstance();

      // حذف جميع البيانات المتعلقة بالمواد المشترك بها لإعادة تعيينها
      debugPrint('🧹 حذف جميع بيانات المواد المشترك بها...');
      await prefs.remove(_subscribedSubjectsKey);

      // حذف أي بيانات أخرى قد تكون تالفة
      final allKeys = prefs.getKeys();
      for (final key in allKeys) {
        if (key.startsWith('subscribed_') || key.contains('subjects')) {
          final rawData = prefs.get(key);
          if (rawData is String && key != 'user_subscription_data') {
            debugPrint('🧹 حذف بيانات تالفة للمفتاح: $key');
            await prefs.remove(key);
          }
        }
      }

      debugPrint('✅ تم تنظيف البيانات التالفة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات عند البدء: $e');
    }
  }

  static const String _questionsPrefix = 'subject_questions_';
  static const String _lastUpdatePrefix = 'last_update_';
  static const String _subscribedSubjectsKey = 'subscribed_subjects';

  /// حفظ أسئلة مادة محلياً
  Future<void> saveSubjectQuestions(
    String subjectId,
    List<Question> questions,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحويل الأسئلة إلى JSON للتخزين المحلي
      final questionsJson = questions.map((q) => q.toLocalMap()).toList();
      final jsonString = jsonEncode(questionsJson);

      // حفظ الأسئلة
      await prefs.setString('$_questionsPrefix$subjectId', jsonString);

      // حفظ وقت التحديث
      await prefs.setString(
        '$_lastUpdatePrefix$subjectId',
        DateTime.now().toIso8601String(),
      );

      debugPrint('✅ تم حفظ ${questions.length} سؤال للمادة $subjectId محلياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ أسئلة المادة محلياً: $e');
    }
  }

  /// تحميل أسئلة مادة من التخزين المحلي
  Future<List<Question>> getSubjectQuestions(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('$_questionsPrefix$subjectId');

      if (jsonString == null) {
        debugPrint('📭 لا توجد أسئلة محفوظة محلياً للمادة $subjectId');
        return [];
      }

      final questionsJson = jsonDecode(jsonString) as List;
      final questions = questionsJson
          .map((json) => Question.fromLocalMap(json as Map<String, dynamic>))
          .toList();

      debugPrint(
        '📚 تم تحميل ${questions.length} سؤال للمادة $subjectId من التخزين المحلي',
      );
      return questions;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أسئلة المادة من التخزين المحلي: $e');
      return [];
    }
  }

  /// الحصول على آخر وقت تحديث لمادة
  Future<DateTime?> getLastUpdateTime(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString('$_lastUpdatePrefix$subjectId');

      if (timeString == null) return null;

      return DateTime.parse(timeString);
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على وقت التحديث: $e');
      return null;
    }
  }

  /// التحقق من وجود أسئلة محفوظة للمادة
  Future<bool> hasSubjectQuestions(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey('$_questionsPrefix$subjectId');
  }

  /// حذف أسئلة مادة من التخزين المحلي
  Future<void> deleteSubjectQuestions(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_questionsPrefix$subjectId');
      await prefs.remove('$_lastUpdatePrefix$subjectId');

      debugPrint('🗑️ تم حذف أسئلة المادة $subjectId من التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في حذف أسئلة المادة: $e');
    }
  }

  /// إضافة مادة للمواد المشترك بها
  Future<void> addSubscribedSubject(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تنظيف البيانات القديمة أولاً
      await _cleanupCorruptedData();

      final subscribedSubjects = await getSubscribedSubjects();

      if (!subscribedSubjects.contains(subjectId)) {
        subscribedSubjects.add(subjectId);
        await prefs.setStringList(_subscribedSubjectsKey, subscribedSubjects);
        debugPrint('✅ تم إضافة المادة $subjectId للمواد المشترك بها');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إضافة المادة للاشتراك: $e');
    }
  }

  /// تنظيف البيانات التالفة
  Future<void> _cleanupCorruptedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من البيانات التالفة وحذفها
      final rawData = prefs.get(_subscribedSubjectsKey);
      if (rawData != null && rawData is! List<String>) {
        debugPrint('🧹 حذف بيانات تالفة للمواد المشترك بها');
        await prefs.remove(_subscribedSubjectsKey);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات التالفة: $e');
    }
  }

  /// إزالة مادة من المواد المشترك بها
  Future<void> removeSubscribedSubject(String subjectId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final subscribedSubjects = await getSubscribedSubjects();

      subscribedSubjects.remove(subjectId);
      await prefs.setStringList(_subscribedSubjectsKey, subscribedSubjects);

      // حذف أسئلة المادة أيضاً
      await deleteSubjectQuestions(subjectId);

      debugPrint('✅ تم إزالة المادة $subjectId من المواد المشترك بها');
    } catch (e) {
      debugPrint('❌ خطأ في إزالة المادة من الاشتراك: $e');
    }
  }

  /// الحصول على قائمة المواد المشترك بها
  Future<List<String>> getSubscribedSubjects() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من وجود بيانات قديمة بصيغة خاطئة
      final rawData = prefs.get(_subscribedSubjectsKey);
      if (rawData is String) {
        // إذا كانت البيانات محفوظة كـ String بدلاً من List، نظفها
        debugPrint('🧹 تنظيف بيانات المواد المشترك بها القديمة');
        await prefs.remove(_subscribedSubjectsKey);
        return [];
      }

      return prefs.getStringList(_subscribedSubjectsKey) ?? [];
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المواد المشترك بها: $e');
      // في حالة الخطأ، نظف البيانات
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_subscribedSubjectsKey);
      } catch (_) {}
      return [];
    }
  }

  /// التحقق من الاشتراك في مادة
  Future<bool> isSubscribedToSubject(String subjectId) async {
    final subscribedSubjects = await getSubscribedSubjects();
    return subscribedSubjects.contains(subjectId);
  }

  /// فلترة الأسئلة محلياً حسب الوحدة
  List<Question> filterQuestionsByUnit(
    List<Question> questions,
    String unitId,
    bool isCourseQuestion,
  ) {
    return questions
        .where(
          (q) => q.unitId == unitId && q.isCourseQuestion == isCourseQuestion,
        )
        .toList();
  }

  /// فلترة الأسئلة محلياً حسب الدرس
  List<Question> filterQuestionsByLesson(
    List<Question> questions,
    String lessonId,
    bool isCourseQuestion,
  ) {
    return questions
        .where(
          (q) =>
              q.lessonId == lessonId && q.isCourseQuestion == isCourseQuestion,
        )
        .toList();
  }

  /// فلترة أسئلة الدورات
  List<Question> filterCourseQuestions(List<Question> questions) {
    return questions.where((q) => q.isCourseQuestion == true).toList();
  }

  /// فلترة الأسئلة العادية
  List<Question> filterRegularQuestions(List<Question> questions) {
    return questions.where((q) => q.isCourseQuestion == false).toList();
  }

  /// البحث في الأسئلة محلياً
  List<Question> searchQuestions(List<Question> questions, String query) {
    if (query.trim().isEmpty) return questions;

    final searchQuery = query.toLowerCase().trim();
    return questions.where((question) {
      // البحث في نص السؤال
      final questionTextMatch = question.questionText.toLowerCase().contains(
        searchQuery,
      );

      // البحث في الشرح
      final explanationMatch = question.explanation.toLowerCase().contains(
        searchQuery,
      );

      // البحث في الإجابات
      final optionsMatch = question.options.any(
        (option) => option.toLowerCase().contains(searchQuery),
      );

      return questionTextMatch || explanationMatch || optionsMatch;
    }).toList();
  }

  /// مسح جميع البيانات المحلية
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      // حذف جميع مفاتيح الأسئلة والتحديثات
      for (final key in keys) {
        if (key.startsWith(_questionsPrefix) ||
            key.startsWith(_lastUpdatePrefix) ||
            key == _subscribedSubjectsKey) {
          await prefs.remove(key);
        }
      }

      debugPrint('🧹 تم مسح جميع بيانات الأسئلة المحلية');
    } catch (e) {
      debugPrint('❌ خطأ في مسح البيانات المحلية: $e');
    }
  }

  /// الحصول على حجم البيانات المحفوظة محلياً
  Future<Map<String, int>> getCacheSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      int totalQuestions = 0;
      int totalSubjects = 0;

      for (final key in keys) {
        if (key.startsWith(_questionsPrefix)) {
          totalSubjects++;
          final jsonString = prefs.getString(key);
          if (jsonString != null) {
            final questionsJson = jsonDecode(jsonString) as List;
            totalQuestions += questionsJson.length;
          }
        }
      }

      return {'subjects': totalSubjects, 'questions': totalQuestions};
    } catch (e) {
      debugPrint('❌ خطأ في حساب حجم التخزين المؤقت: $e');
      return {'subjects': 0, 'questions': 0};
    }
  }
}

/// خدمة التحديث الذكي للأسئلة
class SmartUpdateService {
  static final SmartUpdateService _instance = SmartUpdateService._internal();
  static SmartUpdateService get instance => _instance;
  SmartUpdateService._internal();

  /// تحديث مادة واحدة
  Future<bool> updateSubjectQuestions(
    String subjectId,
    String subjectName,
  ) async {
    try {
      debugPrint('🔄 بدء تحديث أسئلة المادة: $subjectName');

      // التحقق من آخر تحديث محلي
      final lastUpdateTime = await LocalQuestionsCacheService.instance
          .getLastUpdateTime(subjectId);

      // التحقق من وجود تحديثات
      final hasUpdates = await SubjectQuestionsService.instance.hasUpdates(
        subjectId,
        lastUpdateTime ?? DateTime(2000), // إذا لم يكن هناك تحديث سابق
      );

      if (!hasUpdates) {
        debugPrint('✅ لديك آخر تحديث لمادة $subjectName');
        return false; // لا توجد تحديثات
      }

      // تحميل الأسئلة الجديدة من Firebase
      final newQuestions = await SubjectQuestionsService.instance
          .getAllSubjectQuestions(subjectId);

      if (newQuestions.isEmpty) {
        debugPrint('📭 لا توجد أسئلة للمادة $subjectName');
        return false;
      }

      // حفظ الأسئلة محلياً
      await LocalQuestionsCacheService.instance.saveSubjectQuestions(
        subjectId,
        newQuestions,
      );

      // إضافة المادة للمواد المشترك بها
      await LocalQuestionsCacheService.instance.addSubscribedSubject(subjectId);

      debugPrint('✅ تم تحديث ${newQuestions.length} سؤال لمادة $subjectName');
      return true; // تم التحديث بنجاح
    } catch (e) {
      debugPrint('❌ خطأ في تحديث أسئلة المادة $subjectName: $e');
      return false;
    }
  }

  /// تحديث جميع المواد المشترك بها
  Future<Map<String, bool>> updateAllSubscribedSubjects() async {
    try {
      debugPrint('🔄 بدء تحديث جميع المواد المشترك بها');

      final subscribedSubjects = await LocalQuestionsCacheService.instance
          .getSubscribedSubjects();
      final results = <String, bool>{};

      for (final subjectId in subscribedSubjects) {
        final success = await updateSubjectQuestions(
          subjectId,
          'المادة $subjectId',
        );
        results[subjectId] = success;
      }

      final updatedCount = results.values.where((success) => success).length;
      debugPrint(
        '✅ تم تحديث $updatedCount من ${subscribedSubjects.length} مادة',
      );

      return results;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المواد المشترك بها: $e');
      return {};
    }
  }

  /// تحميل أسئلة مادة لأول مرة
  Future<bool> downloadSubjectQuestions(
    String subjectId,
    String subjectName,
  ) async {
    try {
      debugPrint('📥 تحميل أسئلة المادة لأول مرة: $subjectName');

      // تحميل الأسئلة من Firebase
      final questions = await SubjectQuestionsService.instance
          .getAllSubjectQuestions(subjectId);

      if (questions.isEmpty) {
        debugPrint('📭 لا توجد أسئلة للمادة $subjectName');
        return false;
      }

      // حفظ الأسئلة محلياً
      await LocalQuestionsCacheService.instance.saveSubjectQuestions(
        subjectId,
        questions,
      );

      // إضافة المادة للمواد المشترك بها
      await LocalQuestionsCacheService.instance.addSubscribedSubject(subjectId);

      debugPrint('✅ تم تحميل ${questions.length} سؤال لمادة $subjectName');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أسئلة المادة $subjectName: $e');
      return false;
    }
  }
}
