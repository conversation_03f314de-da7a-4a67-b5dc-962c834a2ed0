import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/pricing_service.dart';
import '../../../../shared/services/firebase_service.dart';
import '../../../../shared/models/subscription_price_model.dart';
import '../../../../shared/models/subject_model.dart';

class AddPriceDialog extends StatefulWidget {
  const AddPriceDialog({super.key});

  @override
  State<AddPriceDialog> createState() => _AddPriceDialogState();
}

class _AddPriceDialogState extends State<AddPriceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _priceController = TextEditingController();

  String? _selectedSubjectId;
  String _selectedCurrency = 'USD';
  bool _isLoading = false;
  List<Subject> _subjects = [];
  List<SubscriptionPrice> _existingPrices = [];

  final List<String> _currencies = ['USD', 'SYP'];

  @override
  void initState() {
    super.initState();
    _loadSubjects();
    _existingPrices = context.read<PricingService>().subscriptionPrices;
  }

  @override
  void dispose() {
    _priceController.dispose();
    super.dispose();
  }

  Future<void> _loadSubjects() async {
    try {
      final subjects = await FirebaseService.instance.getAllSubjects();

      if (mounted) {
        setState(() {
          _subjects = subjects;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المواد: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل المواد: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.add_circle, color: AppTheme.primaryColor, size: 24.sp),
          SizedBox(width: 8.w),
          const Text('إضافة سعر جديد'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // اختيار المادة
              DropdownButtonFormField<String>(
                value:
                    _getAvailableSubjects().any(
                      (s) => s.id == _selectedSubjectId,
                    )
                    ? _selectedSubjectId
                    : null,
                decoration: const InputDecoration(
                  labelText: 'المادة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.book),
                ),
                items: _getAvailableSubjects().map((subject) {
                  return DropdownMenuItem(
                    value: subject.id,
                    child: Text(subject.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedSubjectId = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار المادة';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16.h),

              // إدخال السعر
              TextFormField(
                controller: _priceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'السعر',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال السعر';
                  }
                  final price = double.tryParse(value);
                  if (price == null || price <= 0) {
                    return 'يرجى إدخال سعر صحيح';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16.h),

              // اختيار العملة
              DropdownButtonFormField<String>(
                value: _selectedCurrency,
                decoration: const InputDecoration(
                  labelText: 'العملة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.currency_exchange),
                ),
                items: _currencies.map((currency) {
                  return DropdownMenuItem(
                    value: currency,
                    child: Text(_getCurrencyName(currency)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCurrency = value!;
                  });
                },
              ),

              if (_getAvailableSubjects().isEmpty) ...[
                SizedBox(height: 16.h),
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: AppTheme.warningColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: AppTheme.warningColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info,
                        color: AppTheme.warningColor,
                        size: 20.sp,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          'جميع المواد لديها أسعار محددة بالفعل',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppTheme.warningColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading || _getAvailableSubjects().isEmpty
              ? null
              : _addPrice,
          child: _isLoading
              ? SizedBox(
                  width: 20.w,
                  height: 20.w,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إضافة'),
        ),
      ],
    );
  }

  List<Subject> _getAvailableSubjects() {
    // إرجاع المواد التي لا تحتوي على أسعار مع إزالة المكررات
    final uniqueSubjects = <String, Subject>{};
    for (final subject in _subjects) {
      uniqueSubjects[subject.id] = subject;
    }

    final availableSubjects = uniqueSubjects.values.where((subject) {
      return !_existingPrices.any((price) => price.subjectId == subject.id);
    }).toList();

    // إضافة خيار "جميع المواد" إذا لم يكن موجوداً في الأسعار الحالية
    final hasAllSubjectsPrice = _existingPrices.any(
      (price) => price.subjectId == 'all_subjects',
    );
    if (!hasAllSubjectsPrice) {
      availableSubjects.insert(
        0,
        Subject(
          id: 'all_subjects',
          name: 'جميع المواد',
          description: 'اشتراك شامل لجميع المواد المتاحة',
          iconUrl: '',
          color: '#6C5CE7',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
    }

    return availableSubjects;
  }

  String _getCurrencyName(String currency) {
    switch (currency) {
      case 'USD':
        return 'دولار أمريكي (USD)';
      case 'SYP':
        return 'ليرة سورية (SYP)';
      default:
        return currency;
    }
  }

  Future<void> _addPrice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // البحث عن المادة المحددة في القائمة المتاحة (تتضمن "جميع المواد")
      final selectedSubject = _getAvailableSubjects().firstWhere(
        (s) => s.id == _selectedSubjectId,
      );
      final price = double.parse(_priceController.text);
      final now = DateTime.now();

      final subscriptionPrice = SubscriptionPrice(
        id: '', // سيتم تعيينه تلقائياً
        subjectId: selectedSubject.id,
        subjectName: selectedSubject.name,
        price: price,
        currency: _selectedCurrency,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      );

      final success = await context.read<PricingService>().addSubscriptionPrice(
        subscriptionPrice,
      );

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة السعر بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إضافة السعر'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
