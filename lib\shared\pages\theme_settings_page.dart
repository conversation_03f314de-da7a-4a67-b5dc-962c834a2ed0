import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/theme_service.dart';
import '../widgets/theme_toggle_widget.dart';

/// صفحة إعدادات الثيم
class ThemeSettingsPage extends StatelessWidget {
  const ThemeSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات المظهر'),
        centerTitle: true,
        actions: const [
          AppBarThemeToggle(),
          SizedBox(width: 8),
        ],
      ),
      body: Consumer<ThemeService>(
        builder: (context, themeService, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معاينة الثيم الحالي
                _buildCurrentThemePreview(context, themeService),
                const SizedBox(height: 24),
                
                // اختيار الثيم
                const ThemeSelectionWidget(
                  title: 'اختر مظهر التطبيق',
                ),
                const SizedBox(height: 24),
                
                // معلومات إضافية
                _buildThemeInfo(context),
                const SizedBox(height: 24),
                
                // معاينة الألوان
                _buildColorPreview(context),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentThemePreview(BuildContext context, ThemeService themeService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.preview,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'المظهر الحالي',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          themeService.themeIcon,
                          size: 48,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          themeService.currentThemeName,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).primaryColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          themeService.isDarkMode ? 'الوضع المظلم نشط' : 'الوضع الفاتح نشط',
                          style: Theme.of(context).textTheme.bodySmall,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () => themeService.toggleTheme(),
                  icon: const Icon(Icons.swap_horiz),
                  label: const Text('تبديل'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeInfo(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات المظهر',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoItem(
              context,
              Icons.brightness_auto,
              'تلقائي (حسب النظام)',
              'يتبع إعدادات النظام تلقائياً',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              context,
              Icons.light_mode,
              'الوضع الفاتح',
              'مناسب للاستخدام في النهار',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              context,
              Icons.dark_mode,
              'الوضع المظلم',
              'مناسب للاستخدام في الليل ويوفر البطارية',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(BuildContext context, IconData icon, String title, String description) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).iconTheme.color?.withValues(alpha: 0.7),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildColorPreview(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.palette,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'معاينة الألوان',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildColorChip(context, 'أساسي', Theme.of(context).primaryColor),
                _buildColorChip(context, 'ثانوي', Theme.of(context).colorScheme.secondary),
                _buildColorChip(context, 'خطأ', Theme.of(context).colorScheme.error),
                _buildColorChip(context, 'سطح', Theme.of(context).colorScheme.surface),
                _buildColorChip(context, 'خلفية', Theme.of(context).scaffoldBackgroundColor),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorChip(BuildContext context, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).dividerColor,
        ),
      ),
      child: Text(
        label,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getContrastColor(color),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getContrastColor(Color color) {
    // حساب اللون المتباين للنص
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
