import 'dart:io';
import 'dart:typed_data';
import 'package:cloudinary_public/cloudinary_public.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;
import '../../core/utils/logger.dart';
import '../config/cloudinary_config.dart';

class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  static ImageService get instance => _instance;

  CloudinaryPublic? _cloudinary;

  /// تهيئة Cloudinary
  void initializeCloudinary() {
    Logger.info('محاولة تهيئة Cloudinary...');
    Logger.info('Cloud Name: ${CloudinaryConfig.cloudName}');
    Logger.info('Upload Preset: ${CloudinaryConfig.uploadPreset}');
    Logger.info('Is Configured: ${CloudinaryConfig.isConfigured}');

    if (CloudinaryConfig.isConfigured) {
      _cloudinary = CloudinaryPublic(
        CloudinaryConfig.cloudName,
        CloudinaryConfig.uploadPreset,
        cache: false,
      );
      Logger.success('تم تهيئة Cloudinary بنجاح');
    } else {
      Logger.error('إعدادات Cloudinary غير مكتملة');
      Logger.error(CloudinaryConfig.configurationError);
    }
  }

  /// ضغط الصورة لتقليل الحجم
  Future<Uint8List> compressImage(Uint8List imageBytes) async {
    try {
      Logger.info('بدء ضغط الصورة - الحجم الأصلي: ${imageBytes.length} بايت');

      // ضغط الصورة باستخدام flutter_image_compress
      final compressedBytes = await FlutterImageCompress.compressWithList(
        imageBytes,
        minHeight: 600,
        minWidth: 800,
        quality: 70,
        format: CompressFormat.jpeg,
      );

      Logger.success(
        'تم ضغط الصورة - الحجم الجديد: ${compressedBytes.length} بايت '
        '(توفير ${((imageBytes.length - compressedBytes.length) / imageBytes.length * 100).toStringAsFixed(1)}%)',
      );

      return compressedBytes;
    } catch (e) {
      Logger.error('خطأ في ضغط الصورة', e);

      // في حالة فشل الضغط، نحاول الطريقة البديلة
      return await _fallbackCompress(imageBytes);
    }
  }

  /// طريقة بديلة لضغط الصورة
  Future<Uint8List> _fallbackCompress(Uint8List imageBytes) async {
    try {
      Logger.info('استخدام الطريقة البديلة لضغط الصورة');

      // فك تشفير الصورة
      img.Image? image = img.decodeImage(imageBytes);
      if (image == null) {
        Logger.warning('فشل في فك تشفير الصورة، إرجاع الصورة الأصلية');
        return imageBytes;
      }

      // تصغير الأبعاد إذا كانت كبيرة
      if (image.width > 800 || image.height > 600) {
        image = img.copyResize(
          image,
          width: image.width > image.height ? 800 : null,
          height: image.height > image.width ? 600 : null,
        );
      }

      // تحويل إلى JPEG مع ضغط
      final compressedBytes = Uint8List.fromList(
        img.encodeJpg(image, quality: 70),
      );

      Logger.success(
        'تم ضغط الصورة بالطريقة البديلة - الحجم الجديد: ${compressedBytes.length} بايت',
      );

      return compressedBytes;
    } catch (e) {
      Logger.error('خطأ في الطريقة البديلة لضغط الصورة', e);
      return imageBytes; // إرجاع الصورة الأصلية في حالة الفشل
    }
  }

  /// رفع صورة السؤال إلى Cloudinary
  Future<String> uploadQuestionImage(
    Uint8List imageBytes,
    String questionId,
  ) async {
    try {
      Logger.start('بدء رفع صورة السؤال إلى Cloudinary: $questionId');

      // التحقق من تهيئة Cloudinary
      if (_cloudinary == null) {
        initializeCloudinary();
        if (_cloudinary == null) {
          throw Exception('Cloudinary غير مُعد بشكل صحيح');
        }
      }

      // ضغط الصورة أولاً
      final compressedBytes = await compressImage(imageBytes);
      Logger.info(
        'تم ضغط الصورة من ${imageBytes.length} إلى ${compressedBytes.length} بايت',
      );

      // إنشاء ملف مؤقت للرفع
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/temp_question_$questionId.jpg');
      await tempFile.writeAsBytes(compressedBytes);

      try {
        Logger.info('محاولة رفع الصورة إلى Cloudinary...');
        Logger.info('Cloud Name: ${CloudinaryConfig.cloudName}');
        Logger.info('Upload Preset: ${CloudinaryConfig.uploadPreset}');
        Logger.info('File Path: ${tempFile.path}');
        Logger.info('Question ID: $questionId');

        // رفع الصورة إلى Cloudinary بدون folder و publicId لاختبار
        final response = await _cloudinary!.uploadFile(
          CloudinaryFile.fromFile(
            tempFile.path,
            resourceType: CloudinaryResourceType.Image,
          ),
        );

        // حذف الملف المؤقت
        if (await tempFile.exists()) {
          await tempFile.delete();
        }

        final imageUrl = response.secureUrl;
        Logger.success('تم رفع صورة السؤال بنجاح إلى Cloudinary: $imageUrl');
        return imageUrl;
      } catch (cloudinaryError) {
        // حذف الملف المؤقت في حالة الخطأ
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
        rethrow;
      }
    } catch (e) {
      Logger.error('فشل في رفع صورة السؤال إلى Cloudinary: $e');

      // في حالة الفشل، إرجاع رابط وهمي
      final tempUrl =
          'https://via.placeholder.com/400x300.jpg?text=Error+Image+$questionId';
      Logger.warning('تم إرجاع رابط وهمي بسبب الخطأ: $tempUrl');
      return tempUrl;
    }
  }

  /// حذف صورة من Cloudinary
  Future<void> deleteQuestionImage(String imageUrl) async {
    try {
      if (imageUrl.isEmpty) return;

      // تجاهل الروابط الوهمية
      if (imageUrl.contains('placeholder.com')) {
        Logger.info('تجاهل حذف الرابط الوهمي: $imageUrl');
        return;
      }

      Logger.start('بدء حذف صورة السؤال من Cloudinary: $imageUrl');

      // التحقق من تهيئة Cloudinary
      if (_cloudinary == null) {
        initializeCloudinary();
        if (_cloudinary == null) {
          throw Exception('Cloudinary غير مُعد بشكل صحيح');
        }
      }

      // ملاحظة: Cloudinary Public API لا يدعم الحذف
      // الحذف يتطلب Admin API مع API Secret
      // لذلك سنقوم فقط بحذف النسخة المحلية
      Logger.info('تم تجاهل حذف الصورة من Cloudinary (يتطلب Admin API)');
      Logger.info('سيتم حذف النسخة المحلية فقط');
    } catch (e) {
      Logger.error('خطأ في حذف صورة السؤال من Cloudinary: $e');
      // لا نرمي الخطأ هنا لأن حذف الصورة ليس أساسي
    }
  }

  /// تحميل الصورة وحفظها محلياً للاستخدام بدون إنترنت
  Future<String?> downloadAndSaveImage(
    String imageUrl,
    String questionId,
  ) async {
    try {
      if (imageUrl.isEmpty) return null;

      Logger.start('بدء تحميل وحفظ صورة السؤال: $questionId');

      // تحميل الصورة من الإنترنت
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode != 200) {
        throw Exception('فشل في تحميل الصورة: ${response.statusCode}');
      }

      // الحصول على مجلد التطبيق
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory(path.join(appDir.path, 'question_images'));

      // إنشاء المجلد إذا لم يكن موجوداً
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      // حفظ الصورة محلياً
      final fileName = 'question_$questionId.jpg';
      final localFile = File(path.join(imagesDir.path, fileName));
      await localFile.writeAsBytes(response.bodyBytes);

      Logger.success('تم حفظ صورة السؤال محلياً: ${localFile.path}');
      return localFile.path;
    } catch (e) {
      Logger.error('خطأ في تحميل وحفظ صورة السؤال', e);
      return null;
    }
  }

  /// حذف الصورة المحلية
  Future<void> deleteLocalImage(String localPath) async {
    try {
      if (localPath.isEmpty) return;

      final file = File(localPath);
      if (await file.exists()) {
        await file.delete();
        Logger.success('تم حذف الصورة المحلية: $localPath');
      }
    } catch (e) {
      Logger.error('خطأ في حذف الصورة المحلية', e);
    }
  }

  /// التحقق من وجود الصورة محلياً
  Future<bool> hasLocalImage(String localPath) async {
    try {
      if (localPath.isEmpty) return false;
      final file = File(localPath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// الحصول على حجم مجلد الصور المحلية
  Future<int> getLocalImagesSize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory(path.join(appDir.path, 'question_images'));

      if (!await imagesDir.exists()) return 0;

      int totalSize = 0;
      await for (final entity in imagesDir.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      Logger.error('خطأ في حساب حجم الصور المحلية', e);
      return 0;
    }
  }

  /// تنظيف الصور المحلية القديمة
  Future<void> cleanupOldImages({int maxAgeInDays = 30}) async {
    try {
      Logger.start('بدء تنظيف الصور المحلية القديمة');

      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory(path.join(appDir.path, 'question_images'));

      if (!await imagesDir.exists()) return;

      final cutoffDate = DateTime.now().subtract(Duration(days: maxAgeInDays));
      int deletedCount = 0;

      await for (final entity in imagesDir.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await entity.delete();
            deletedCount++;
          }
        }
      }

      Logger.success('تم حذف $deletedCount صورة قديمة');
    } catch (e) {
      Logger.error('خطأ في تنظيف الصور المحلية', e);
    }
  }
}
