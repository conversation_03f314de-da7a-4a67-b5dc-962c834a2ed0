import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/models/video_lesson_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/data_distribution_service.dart';

/// صفحة قائمة الفيديوهات للطالب
class VideoListPage extends StatefulWidget {
  final String lessonId;
  final VideoLesson lesson;
  final bool hasSubscription;

  const VideoListPage({
    super.key,
    required this.lessonId,
    required this.lesson,
    required this.hasSubscription,
  });

  @override
  State<VideoListPage> createState() => _VideoListPageState();
}

class _VideoListPageState extends State<VideoListPage> {
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;
  final DataDistributionService _dataService = DataDistributionService.instance;

  List<Video> _videos = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadVideosImmediately();
  }

  /// تحميل فوري للفيديوهات من النظام الجديد
  Future<void> _loadVideosImmediately() async {
    try {
      // استخدام خدمة فرز البيانات الجديدة
      final videos = _dataService.getVideosByLesson(widget.lessonId);

      if (mounted) {
        setState(() {
          _videos = videos;
        });
        debugPrint(
          '⚡ تم تحميل ${videos.length} فيديو فوراً من خدمة فرز البيانات',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفيديوهات: $e');
      // fallback للنظام القديم
      await _loadVideosFromService();
    }
  }

  /// تحميل الفيديوهات من الخدمة (fallback)
  Future<void> _loadVideosFromService() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // TODO: تطبيق تحميل الفيديوهات من Firebase مباشرة
      debugPrint('⚠️ fallback: لا توجد دالة getVideos في VideoService');

      if (mounted) {
        setState(() {
          _videos = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        debugPrint('❌ خطأ في تحميل الفيديوهات: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        widget.lesson.name,
        style: TextStyle(
          fontSize: 20.sp,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: AppTheme.primaryColor,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_videos.isEmpty) {
      return _buildEmptyState();
    }

    return _buildVideosList();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد فيديوهات',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الفيديوهات قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideosList() {
    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _videos.length,
      itemBuilder: (context, index) {
        final video = _videos[index];
        return _buildVideoCard(video);
      },
    );
  }

  Widget _buildVideoCard(Video video) {
    final hasAccess = widget.hasSubscription || video.isPreview;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16.r),
        onTap: () => _playVideo(video, hasAccess),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: [
              _buildVideoThumbnail(video),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      video.title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (video.description.isNotEmpty) ...[
                      SizedBox(height: 4.h),
                      Text(
                        video.description,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.textSecondaryColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        if (!hasAccess) ...[
                          Icon(Icons.lock, size: 16.sp, color: Colors.orange),
                          SizedBox(width: 4.w),
                          Text(
                            'مدفوع',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.orange,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ] else if (video.isPreview) ...[
                          Icon(
                            Icons.play_circle_outline,
                            size: 16.sp,
                            color: Colors.green,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            'مجاني',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                        const Spacer(),
                        if (video.durationInSeconds > 0) ...[
                          Icon(
                            Icons.access_time,
                            size: 14.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            _formatDuration(video.durationInSeconds),
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.play_arrow,
                color: hasAccess ? AppTheme.primaryColor : Colors.grey,
                size: 32.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoThumbnail(Video video) {
    return Container(
      width: 80.w,
      height: 60.h,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Icon(
        Icons.play_circle_filled,
        color: AppTheme.primaryColor,
        size: 32.sp,
      ),
    );
  }

  void _playVideo(Video video, bool hasAccess) {
    if (!hasAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تفعيل الاشتراك لمشاهدة هذا الفيديو'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // عرض رسالة مؤقتة حتى يتم تطبيق مشغل الفيديو
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم تشغيل الفيديو: ${video.title}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }
}
