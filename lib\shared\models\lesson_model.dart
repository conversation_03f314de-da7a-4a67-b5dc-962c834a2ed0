import 'package:cloud_firestore/cloud_firestore.dart';

class Lesson {
  final String id;
  final String unitId;
  final String subjectId;
  final String name;
  final String description;
  final int order;
  final String videoUrl;
  final String contentUrl;
  final String iconUrl;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdByAdminId;

  const Lesson({
    required this.id,
    required this.unitId,
    required this.subjectId,
    required this.name,
    required this.description,
    required this.order,
    required this.videoUrl,
    required this.contentUrl,
    required this.iconUrl,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.createdByAdminId,
  });

  factory Lesson.fromMap(Map<String, dynamic> map) {
    return Lesson(
      id: map['id'] ?? '',
      unitId: map['unitId'] ?? '',
      subjectId: map['subjectId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      order: map['order'] ?? 0,
      videoUrl: map['videoUrl'] ?? '',
      contentUrl: map['contentUrl'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      createdByAdminId: map['createdByAdminId'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'unitId': unitId,
      'subjectId': subjectId,
      'name': name,
      'description': description,
      'order': order,
      'videoUrl': videoUrl,
      'contentUrl': contentUrl,
      'iconUrl': iconUrl,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdByAdminId': createdByAdminId,
    };
  }

  /// تحويل إلى Map للتخزين المحلي (JSON)
  Map<String, dynamic> toLocalMap() {
    return {
      'id': id,
      'unitId': unitId,
      'subjectId': subjectId,
      'name': name,
      'description': description,
      'order': order,
      'videoUrl': videoUrl,
      'contentUrl': contentUrl,
      'iconUrl': iconUrl,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdByAdminId': createdByAdminId,
    };
  }

  /// إنشاء من Map للتخزين المحلي (JSON)
  factory Lesson.fromLocalMap(Map<String, dynamic> map) {
    return Lesson(
      id: map['id'] ?? '',
      unitId: map['unitId'] ?? '',
      subjectId: map['subjectId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      order: map['order'] ?? 0,
      videoUrl: map['videoUrl'] ?? '',
      contentUrl: map['contentUrl'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      isActive: map['isActive'] ?? true,
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      createdByAdminId: map['createdByAdminId'] ?? '',
    );
  }

  /// تحليل التاريخ من أنواع مختلفة
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is DateTime) return value;
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    if (value is String) return DateTime.tryParse(value) ?? DateTime.now();
    return DateTime.now();
  }

  Lesson copyWith({
    String? id,
    String? unitId,
    String? subjectId,
    String? name,
    String? description,
    int? order,
    String? videoUrl,
    String? contentUrl,
    String? iconUrl,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByAdminId,
  }) {
    return Lesson(
      id: id ?? this.id,
      unitId: unitId ?? this.unitId,
      subjectId: subjectId ?? this.subjectId,
      name: name ?? this.name,
      description: description ?? this.description,
      order: order ?? this.order,
      videoUrl: videoUrl ?? this.videoUrl,
      contentUrl: contentUrl ?? this.contentUrl,
      iconUrl: iconUrl ?? this.iconUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
    );
  }

  @override
  String toString() {
    return 'Lesson(id: $id, name: $name, unitId: $unitId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Lesson && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
