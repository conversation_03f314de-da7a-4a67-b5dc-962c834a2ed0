import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/unit_model.dart';
import '../../../../shared/models/lesson_model.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/exam_service.dart';
// النظام الجديد Offline-First
import '../../../../shared/services/data_distribution_service.dart';

class QuestionsPage extends StatefulWidget {
  final Subject subject;
  final bool isCourseQuestions;

  const QuestionsPage({
    super.key,
    required this.subject,
    required this.isCourseQuestions,
  });

  @override
  State<QuestionsPage> createState() => _QuestionsPageState();
}

class _QuestionsPageState extends State<QuestionsPage> {
  final DataDistributionService _dataService = DataDistributionService.instance;
  List<Unit> _units = [];
  final Map<String, List<Lesson>> _unitLessons = {};
  final Map<String, List<Question>> _unitQuestions = {};
  final Map<String, List<Question>> _lessonQuestions = {};

  @override
  void initState() {
    super.initState();
    _loadDataImmediately();
  }

  /// تحميل فوري للبيانات (النظام الجديد)
  Future<void> _loadDataImmediately() async {
    try {
      // استخدام خدمة فرز البيانات الجديدة
      final units = _dataService.getUnitsBySubject(widget.subject.id);
      final lessons = _dataService.getLessonsBySubject(widget.subject.id);
      final questions = _dataService.getQuestionsBySubject(widget.subject.id);

      // تنظيم البيانات
      for (final unit in units) {
        // تصفية الدروس حسب الوحدة
        final unitLessons = lessons
            .where((lesson) => lesson.unitId == unit.id)
            .toList();
        _unitLessons[unit.id] = unitLessons;

        // تصفية الأسئلة حسب الوحدة ونوع السؤال
        final unitQuestions = questions.where((question) {
          final isUnitMatch = question.unitId == unit.id;
          final isTypeMatch = widget.isCourseQuestions
              ? question.isCourseQuestion
              : true;
          return isUnitMatch && isTypeMatch;
        }).toList();
        _unitQuestions[unit.id] = unitQuestions;

        // تصفية الأسئلة حسب الدروس
        for (final lesson in unitLessons) {
          final lessonQuestions = questions.where((question) {
            final isLessonMatch = question.lessonId == lesson.id;
            final isTypeMatch = widget.isCourseQuestions
                ? question.isCourseQuestion
                : true;
            return isLessonMatch && isTypeMatch;
          }).toList();
          _lessonQuestions[lesson.id] = lessonQuestions;
        }
      }

      if (mounted) {
        setState(() {
          _units = units;
        });
        debugPrint(
          '⚡ تم تحميل ${units.length} وحدة فوراً من خدمة فرز البيانات',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');
      // fallback للنظام القديم
      await _loadData();
    }
  }

  Future<void> _loadData() async {
    try {
      final units = await ContentService.instance.getSubjectUnits(
        widget.subject.id,
      );

      for (final unit in units) {
        // تحميل الدروس
        final lessons = await ContentService.instance.getUnitLessons(unit.id);
        _unitLessons[unit.id] = lessons;

        // تحميل أسئلة الوحدة (عادية + دورات)
        List<Question> unitQuestions = [];

        if (widget.isCourseQuestions) {
          // إذا كان في قسم أسئلة الدورات، تحميل أسئلة الدورات فقط
          unitQuestions = await ExamService.instance.getQuestionsByUnit(
            unit.id,
            true,
          );
        } else {
          // إذا كان في قسم الأسئلة العادية، تحميل كلا النوعين
          final regularQuestions = await ExamService.instance
              .getQuestionsByUnit(unit.id, false);
          final courseQuestions = await ExamService.instance.getQuestionsByUnit(
            unit.id,
            true,
          );
          unitQuestions = [...regularQuestions, ...courseQuestions];
        }
        _unitQuestions[unit.id] = unitQuestions;

        // تحميل أسئلة الدروس (عادية + دورات)
        for (final lesson in lessons) {
          List<Question> lessonQuestions = [];

          if (widget.isCourseQuestions) {
            // إذا كان في قسم أسئلة الدورات، تحميل أسئلة الدورات فقط
            lessonQuestions = await ExamService.instance.getQuestionsByLesson(
              lesson.id,
              true,
            );
          } else {
            // إذا كان في قسم الأسئلة العادية، تحميل كلا النوعين
            final regularLessonQuestions = await ExamService.instance
                .getQuestionsByLesson(lesson.id, false);
            final courseLessonQuestions = await ExamService.instance
                .getQuestionsByLesson(lesson.id, true);
            lessonQuestions = [
              ...regularLessonQuestions,
              ...courseLessonQuestions,
            ];
          }
          _lessonQuestions[lesson.id] = lessonQuestions;
        }
      }

      setState(() {
        _units = units;
      });
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_units.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _units.length,
        itemBuilder: (context, index) {
          final unit = _units[index];
          final lessons = _unitLessons[unit.id] ?? [];
          final unitQuestions = _unitQuestions[unit.id] ?? [];
          return _buildUnitCard(unit, lessons, unitQuestions);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.isCourseQuestions
                ? Icons.school_outlined
                : Icons.quiz_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            widget.isCourseQuestions
                ? 'لا توجد أسئلة دورات'
                : 'لا توجد أسئلة متاحة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الأسئلة قريباً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitCard(
    Unit unit,
    List<Lesson> lessons,
    List<Question> unitQuestions,
  ) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: ExpansionTile(
        tilePadding: EdgeInsets.all(16.w),
        childrenPadding: EdgeInsets.symmetric(horizontal: 16.w),
        leading: Container(
          width: 50.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: Color(
              int.parse(unit.color.replaceFirst('#', '0xFF')),
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Icon(
            widget.isCourseQuestions ? Icons.school : Icons.quiz,
            color: Color(int.parse(unit.color.replaceFirst('#', '0xFF'))),
            size: 24.sp,
          ),
        ),
        title: Text(
          unit.name,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4.h),
            Text(
              unit.description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Icon(
                  Icons.quiz_outlined,
                  size: 16.sp,
                  color: AppTheme.primaryColor,
                ),
                SizedBox(width: 4.w),
                Text(
                  '${unitQuestions.length} سؤال',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(width: 16.w),
                Icon(
                  Icons.play_lesson,
                  size: 16.sp,
                  color: AppTheme.secondaryColor,
                ),
                SizedBox(width: 4.w),
                Text(
                  '${lessons.length} درس',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.secondaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
        children: [
          // أسئلة الوحدة
          if (unitQuestions.isNotEmpty) ...[
            _buildSectionHeader('أسئلة الوحدة', unitQuestions.length),
            ...unitQuestions.map((question) => _buildQuestionItem(question)),
            SizedBox(height: 16.h),
          ],

          // أسئلة الدروس
          ...lessons.map((lesson) {
            final lessonQuestions = _lessonQuestions[lesson.id] ?? [];
            if (lessonQuestions.isEmpty) return const SizedBox.shrink();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader(lesson.name, lessonQuestions.length),
                ...lessonQuestions.map(
                  (question) => _buildQuestionItem(question),
                ),
                SizedBox(height: 16.h),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
      margin: EdgeInsets.only(bottom: 8.h),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Icon(Icons.folder_open, size: 16.sp, color: AppTheme.primaryColor),
          SizedBox(width: 8.w),
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryColor,
            ),
          ),
          const Spacer(),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionItem(Question question) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppTheme.getBackgroundColor(context),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(6.w),
                decoration: BoxDecoration(
                  color: _getDifficultyColor(
                    question.difficulty,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getQuestionTypeIcon(question.type),
                  color: _getDifficultyColor(question.difficulty),
                  size: 16.sp,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  question.questionText,
                  textDirection: TextDirection.rtl, // من اليمين لليسار
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: _getDifficultyColor(
                    question.difficulty,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  question.difficultyDisplayName,
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: _getDifficultyColor(question.difficulty),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  '${question.points} نقطة',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                question.typeDisplayName,
                style: TextStyle(
                  fontSize: 10.sp,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return AppTheme.successColor;
      case DifficultyLevel.medium:
        return AppTheme.warningColor;
      case DifficultyLevel.hard:
        return AppTheme.errorColor;
    }
  }

  IconData _getQuestionTypeIcon(QuestionType type) {
    switch (type) {
      case QuestionType.multipleChoice:
        return Icons.radio_button_checked;
      case QuestionType.trueFalse:
        return Icons.check_box;
      case QuestionType.shortAnswer:
        return Icons.short_text;
      case QuestionType.essay:
        return Icons.article;
      case QuestionType.matching:
        return Icons.compare_arrows;
      case QuestionType.fillInTheBlank:
        return Icons.edit;
    }
  }
}
