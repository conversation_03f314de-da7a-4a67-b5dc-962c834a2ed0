import 'dart:io' if (dart.library.html) 'dart:html';
import 'package:flutter/foundation.dart';

// Conditional import for image_picker
// Only import on mobile platforms
// import 'package:image_picker/image_picker.dart'
//     if (dart.library.html) 'dart:html'
//     as picker;  // DISABLED FOR WINDOWS BUILD

/// Platform-aware image picker wrapper
/// Works on Android/iOS, gracefully handles Windows/Web
class PlatformImagePicker {
  static bool get _isImagePickerSupported {
    if (kIsWeb) return false;
    try {
      return Platform.isAndroid || Platform.isIOS;
    } catch (e) {
      return false; // Windows or other unsupported platforms
    }
  }

  // picker.ImagePicker? _imagePicker;  // DISABLED FOR WINDOWS BUILD

  PlatformImagePicker() {
    // if (_isImagePickerSupported) {
    //   _imagePicker = picker.ImagePicker();
    // } else {
    //   _imagePicker = null;
    // }
  }

  /// Check if image picker is supported on current platform
  bool get isSupported => _isImagePickerSupported;

  /// Pick image from gallery
  Future<File?> pickImageFromGallery() async {
    if (!_isImagePickerSupported) {
      throw UnsupportedError('Image picker not supported on this platform');
    }

    // DISABLED FOR WINDOWS BUILD
    throw UnsupportedError('Image picker disabled for Windows build');

    // try {
    //   final picker.XFile? image = await _imagePicker!.pickImage(
    //     source: picker.ImageSource.gallery,
    //     maxWidth: 1920,
    //     maxHeight: 1080,
    //     imageQuality: 85,
    //   );

    //   if (image != null) {
    //     return File(image.path);
    //   }
    //   return null;
    // } catch (e) {
    //   throw Exception('خطأ في اختيار الصورة: $e');
    // }
  }

  /// Pick image from camera
  Future<File?> pickImageFromCamera() async {
    if (!_isImagePickerSupported) {
      throw UnsupportedError('Image picker not supported on this platform');
    }

    // DISABLED FOR WINDOWS BUILD
    throw UnsupportedError('Image picker disabled for Windows build');

    // try {
    //   final picker.XFile? image = await _imagePicker!.pickImage(
    //     source: picker.ImageSource.camera,
    //     maxWidth: 1920,
    //     maxHeight: 1080,
    //     imageQuality: 85,
    //   );

    //   if (image != null) {
    //     return File(image.path);
    //   }
    //   return null;
    // } catch (e) {
    //   throw Exception('خطأ في التقاط الصورة: $e');
    // }
  }
}
