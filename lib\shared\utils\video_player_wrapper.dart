import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:video_player/video_player.dart';

/// Wrapper للتعامل مع video player على منصات مختلفة
/// يستخدم video_player_win تلقائياً على Windows
class VideoPlayerWrapper {
  /// إنشاء video player controller بناءً على المنصة
  static VideoPlayerController createController({
    required String? videoPath,
    required String? networkUrl,
    bool isLocalFile = false,
  }) {
    try {
      // video_player_win يعمل تلقائياً مع video_player على Windows
      // لا حاجة لتغيير الكود - video_player_win يتولى الأمر

      if (isLocalFile && videoPath != null) {
        // تشغيل ملف محلي
        debugPrint('🎬 إنشاء مشغل للملف المحلي: $videoPath');

        // تحسينات خاصة لـ Windows
        if (Platform.isWindows) {
          debugPrint('🪟 استخدام video_player_win للملف المحلي على Windows');
          // التأكد من صحة مسار الملف على Windows
          final file = File(videoPath);
          if (!file.existsSync()) {
            throw Exception('الملف غير موجود: $videoPath');
          }
          return VideoPlayerController.file(file);
        } else {
          return VideoPlayerController.file(File(videoPath));
        }
      } else if (networkUrl != null) {
        // تشغيل من الشبكة
        debugPrint('🌐 إنشاء مشغل للرابط: $networkUrl');

        // تحسينات خاصة لـ Windows
        if (Platform.isWindows) {
          debugPrint('🪟 استخدام video_player_win للشبكة على Windows');
          // التأكد من صحة الرابط
          final uri = Uri.tryParse(networkUrl);
          if (uri == null) {
            throw Exception('رابط غير صالح: $networkUrl');
          }
          return VideoPlayerController.networkUrl(uri);
        } else {
          return VideoPlayerController.networkUrl(Uri.parse(networkUrl));
        }
      } else {
        throw Exception('لا يوجد مصدر فيديو صالح');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء مشغل الفيديو: $e');

      // معلومات إضافية للتشخيص على Windows
      if (Platform.isWindows) {
        debugPrint('🔍 تشخيص Windows:');
        debugPrint('   - المنصة: ${Platform.operatingSystem}');
        debugPrint('   - نوع الملف: ${isLocalFile ? "محلي" : "شبكة"}');
        debugPrint('   - المسار/الرابط: ${videoPath ?? networkUrl}');
      }

      rethrow;
    }
  }

  /// التحقق من دعم تشغيل الفيديو على المنصة الحالية
  static bool isVideoPlaybackSupported() {
    if (kIsWeb) return true;

    try {
      if (Platform.isAndroid || Platform.isIOS) {
        debugPrint('📱 منصة محمولة مدعومة: ${Platform.operatingSystem}');
        return true;
      } else if (Platform.isWindows) {
        // video_player_win 3.2.0 مدعوم بالكامل
        debugPrint('🪟 Windows مكتشف - video_player_win 3.2.0 مدعوم بالكامل');
        debugPrint('   - نسخة Windows: ${Platform.operatingSystemVersion}');
        return true;
      } else if (Platform.isMacOS || Platform.isLinux) {
        debugPrint('🖥️ منصة سطح مكتب مدعومة: ${Platform.operatingSystem}');
        return true;
      }
      debugPrint('⚠️ منصة غير مدعومة: ${Platform.operatingSystem}');
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من دعم تشغيل الفيديو: $e');
      return false;
    }
  }

  /// تشخيص مشاكل تشغيل الفيديو
  static Future<Map<String, dynamic>> diagnoseVideoPlayback({
    String? videoPath,
    String? networkUrl,
    bool isLocalFile = false,
  }) async {
    final diagnosis = <String, dynamic>{
      'platform': Platform.operatingSystem,
      'platformVersion': Platform.operatingSystemVersion,
      'isSupported': isVideoPlaybackSupported(),
      'videoSource': isLocalFile ? 'local' : 'network',
      'sourcePath': videoPath ?? networkUrl,
      'issues': <String>[],
      'recommendations': <String>[],
    };

    try {
      // فحص المنصة
      if (Platform.isWindows) {
        diagnosis['windowsSpecific'] = {
          'videoPlayerWinAvailable': true,
          'mediaFoundationSupport': true,
        };
      }

      // فحص مصدر الفيديو
      if (isLocalFile && videoPath != null) {
        final file = File(videoPath);
        diagnosis['fileExists'] = file.existsSync();
        if (file.existsSync()) {
          diagnosis['fileSize'] = file.lengthSync();
          diagnosis['fileExtension'] = videoPath.split('.').last.toLowerCase();
        } else {
          diagnosis['issues'].add('الملف غير موجود: $videoPath');
          diagnosis['recommendations'].add('تحقق من مسار الملف');
        }
      } else if (networkUrl != null) {
        final uri = Uri.tryParse(networkUrl);
        diagnosis['validUrl'] = uri != null;
        if (uri == null) {
          diagnosis['issues'].add('رابط غير صالح: $networkUrl');
          diagnosis['recommendations'].add('تحقق من صحة الرابط');
        }
      }

      // توصيات عامة
      if (Platform.isWindows) {
        diagnosis['recommendations'].addAll([
          'تأكد من تثبيت video_player_win 3.2.0',
          'تحقق من دعم Media Foundation على Windows',
          'استخدم صيغ فيديو مدعومة (MP4, AVI, WMV)',
        ]);
      }
    } catch (e) {
      diagnosis['issues'].add('خطأ في التشخيص: $e');
    }

    return diagnosis;
  }

  /// طباعة تقرير تشخيص مفصل
  static Future<void> printDiagnosisReport({
    String? videoPath,
    String? networkUrl,
    bool isLocalFile = false,
  }) async {
    debugPrint('🔍 ===== تقرير تشخيص تشغيل الفيديو =====');

    final diagnosis = await diagnoseVideoPlayback(
      videoPath: videoPath,
      networkUrl: networkUrl,
      isLocalFile: isLocalFile,
    );

    debugPrint('📋 معلومات المنصة:');
    debugPrint('   - النظام: ${diagnosis['platform']}');
    debugPrint('   - الإصدار: ${diagnosis['platformVersion']}');
    debugPrint('   - مدعوم: ${diagnosis['isSupported']}');

    debugPrint('📹 معلومات الفيديو:');
    debugPrint('   - المصدر: ${diagnosis['videoSource']}');
    debugPrint('   - المسار: ${diagnosis['sourcePath']}');

    if (diagnosis['issues'].isNotEmpty) {
      debugPrint('⚠️ المشاكل المكتشفة:');
      for (final issue in diagnosis['issues']) {
        debugPrint('   - $issue');
      }
    }

    if (diagnosis['recommendations'].isNotEmpty) {
      debugPrint('💡 التوصيات:');
      for (final recommendation in diagnosis['recommendations']) {
        debugPrint('   - $recommendation');
      }
    }

    debugPrint('🔍 ===== نهاية التقرير =====');
  }

  /// الحصول على رسالة خطأ مناسبة للمنصة
  static String getUnsupportedPlatformMessage() {
    if (kIsWeb) {
      return 'تشغيل الفيديو غير مدعوم في المتصفح حالياً';
    }

    try {
      if (Platform.isWindows) {
        return 'تم تحديث دعم Windows - يرجى إعادة تشغيل التطبيق';
      } else {
        return 'تشغيل الفيديو غير مدعوم على هذه المنصة';
      }
    } catch (e) {
      return 'خطأ في تحديد نوع المنصة';
    }
  }

  /// معلومات المنصة للتشخيص
  static String getPlatformInfo() {
    if (kIsWeb) return 'Web Platform';

    try {
      return 'Platform: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}';
    } catch (e) {
      return 'Unknown Platform';
    }
  }
}
