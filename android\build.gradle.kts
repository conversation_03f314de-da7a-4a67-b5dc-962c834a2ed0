allprojects {
    repositories {
        // المستودعات الأساسية
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
                includeGroupByRegex("io\\.flutter.*")
            }
        }
        mavenCentral()

        // مستودعات بديلة محسنة
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
                includeGroupByRegex("io\\.flutter.*")
            }
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/central")
        }
        maven {
            url = uri("https://repo1.maven.org/maven2/")
        }
        maven {
            url = uri("https://jcenter.bintray.com/")
        }

        // مستودع Flutter المباشر
        maven {
            url = uri("https://storage.googleapis.com/download.flutter.io")
            content {
                includeGroupByRegex("io\\.flutter.*")
            }
        }
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
// تم حذف evaluationDependsOn لتجنب مشاكل البناء

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
