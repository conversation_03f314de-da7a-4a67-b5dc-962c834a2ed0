import 'package:flutter/foundation.dart';

/// 🔒 نظام Logging آمن - يمنع عرض الروابط في Release mode
class SafeLogger {
  /// طباعة آمنة - تعمل فقط في Debug mode
  static void safePrint(Object? message) {
    if (kDebugMode) {
      print(message);
    }
  }

  /// طباعة آمنة مع تصفية الروابط الحساسة
  static void safePrintFiltered(String message) {
    if (kDebugMode) {
      // تصفية الروابط الحساسة من الرسائل
      String filteredMessage = _filterSensitiveUrls(message);
      print(filteredMessage);
    }
  }

  /// تصفية الروابط الحساسة من النص
  static String _filterSensitiveUrls(String text) {
    // إخفاء روابط Google Drive
    text = text.replaceAll(
      RegExp(r'https://drive\.google\.com/[^\s]+'),
      '[PROTECTED_DRIVE_LINK]',
    );

    // إخفاء روابط Firebase Storage
    text = text.replaceAll(
      RegExp(r'https://firebasestorage\.googleapis\.com/[^\s]+'),
      '[PROTECTED_STORAGE_LINK]',
    );

    // إخفاء أي روابط أخرى تحتوي على معاملات حساسة
    text = text.replaceAll(
      RegExp(r'https://[^\s]*\?[^\s]*token[^\s]*'),
      '[PROTECTED_LINK]',
    );

    // إخفاء أي رابط يحتوي على id= أو key=
    text = text.replaceAll(
      RegExp(r'https://[^\s]*[?&](id|key)=[^\s&]*'),
      '[PROTECTED_LINK]',
    );

    return text;
  }

  /// طباعة معلومات الفيديو بشكل آمن
  static void logVideoInfo(String videoId, String title) {
    if (kDebugMode) {
      print('🎥 تحميل فيديو: $title (ID: ${videoId.substring(0, 8)}...)');
    }
  }

  /// طباعة حالة التحميل بشكل آمن
  static void logDownloadProgress(String fileName, int progress) {
    if (kDebugMode) {
      print('📥 تحميل $fileName: $progress%');
    }
  }

  /// طباعة الأخطاء بشكل آمن (بدون روابط حساسة)
  static void logError(String context, dynamic error) {
    if (kDebugMode) {
      String errorMessage = error.toString();
      String filteredError = _filterSensitiveUrls(errorMessage);
      print('❌ خطأ في $context: $filteredError');
    }
  }
}
