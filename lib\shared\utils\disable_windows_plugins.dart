// This file is used to disable plugins that cause issues on Windows
// It's imported in main.dart to ensure these plugins are not used on Windows

import 'dart:io' if (dart.library.html) 'dart:html';
import 'package:flutter/foundation.dart';

/// Check if we're running on Windows
bool get isWindowsPlatform {
  if (kIsWeb) return false;
  try {
    return Platform.isWindows;
  } catch (e) {
    return false;
  }
}

/// Disable problematic Windows plugins
void disableWindowsPlugins() {
  if (!isWindowsPlatform) return;
  
  // Add any Windows-specific plugin disabling code here
  // This is a placeholder function that will be called at app startup
  print('Windows platform detected - disabling incompatible plugins');
}
