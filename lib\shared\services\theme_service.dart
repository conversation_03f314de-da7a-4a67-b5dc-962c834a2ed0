import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة الثيمات (الوضع الليلي والنهاري)
class ThemeService extends ChangeNotifier {
  static final ThemeService _instance = ThemeService._internal();
  static ThemeService get instance => _instance;
  ThemeService._internal();

  // مفتاح حفظ الثيم في SharedPreferences
  static const String _themeKey = 'app_theme_mode';
  
  // الوضع الحالي للثيم
  ThemeMode _themeMode = ThemeMode.system;
  ThemeMode get themeMode => _themeMode;

  // هل الوضع الحالي مظلم؟
  bool get isDarkMode {
    if (_themeMode == ThemeMode.system) {
      // استخدام إعدادات النظام
      return WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }

  /// تهيئة الخدمة وتحميل الثيم المحفوظ
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString(_themeKey);
      
      if (savedTheme != null) {
        switch (savedTheme) {
          case 'light':
            _themeMode = ThemeMode.light;
            break;
          case 'dark':
            _themeMode = ThemeMode.dark;
            break;
          case 'system':
          default:
            _themeMode = ThemeMode.system;
            break;
        }
      }
      
      debugPrint('🎨 تم تحميل الثيم: ${_getThemeName(_themeMode)}');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الثيم: $e');
      // استخدام الثيم الافتراضي
      _themeMode = ThemeMode.system;
    }
  }

  /// تغيير الثيم
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      String themeString;
      
      switch (mode) {
        case ThemeMode.light:
          themeString = 'light';
          break;
        case ThemeMode.dark:
          themeString = 'dark';
          break;
        case ThemeMode.system:
        default:
          themeString = 'system';
          break;
      }
      
      await prefs.setString(_themeKey, themeString);
      debugPrint('🎨 تم تغيير الثيم إلى: ${_getThemeName(mode)}');
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الثيم: $e');
    }
  }

  /// تبديل بين الوضع الفاتح والمظلم
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      await setThemeMode(ThemeMode.dark);
    } else if (_themeMode == ThemeMode.dark) {
      await setThemeMode(ThemeMode.light);
    } else {
      // إذا كان النظام، تبديل حسب الوضع الحالي
      final currentBrightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
      if (currentBrightness == Brightness.dark) {
        await setThemeMode(ThemeMode.light);
      } else {
        await setThemeMode(ThemeMode.dark);
      }
    }
  }

  /// الحصول على اسم الثيم للعرض
  String _getThemeName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'مظلم';
      case ThemeMode.system:
        return 'تلقائي (حسب النظام)';
    }
  }

  /// الحصول على اسم الثيم الحالي
  String get currentThemeName => _getThemeName(_themeMode);

  /// الحصول على أيقونة الثيم الحالي
  IconData get themeIcon {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  /// قائمة جميع أوضاع الثيم المتاحة
  List<ThemeMode> get availableThemeModes => [
    ThemeMode.system,
    ThemeMode.light,
    ThemeMode.dark,
  ];

  /// الحصول على اسم وضع ثيم معين
  String getThemeModeName(ThemeMode mode) => _getThemeName(mode);

  /// الحصول على أيقونة وضع ثيم معين
  IconData getThemeModeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}
