import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../core/utils/logger.dart';
import '../models/subscription_price_model.dart';
import '../models/subject_model.dart';

class PricingService extends ChangeNotifier {
  static PricingService? _instance;
  static PricingService get instance => _instance ??= PricingService._();
  
  PricingService._();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String pricesCollection = 'subscription_prices';
  static const String specialPricesCollection = 'special_prices';

  List<SubscriptionPrice> _subscriptionPrices = [];
  List<SpecialPrice> _specialPrices = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<SubscriptionPrice> get subscriptionPrices => _subscriptionPrices;
  List<SpecialPrice> get specialPrices => _specialPrices;
  List<SpecialPrice> get validSpecialPrices => _specialPrices.where((p) => p.isValid).toList();
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await Future.wait([
        loadSubscriptionPrices(),
        loadSpecialPrices(),
      ]);
      _clearError();
    } catch (e) {
      _setError('فشل في تهيئة خدمة الأسعار: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل أسعار الاشتراكات
  Future<void> loadSubscriptionPrices() async {
    try {
      final querySnapshot = await _firestore
          .collection(pricesCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('subjectName')
          .get();

      _subscriptionPrices = querySnapshot.docs
          .map((doc) => SubscriptionPrice.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      notifyListeners();
    } catch (e) {
      Logger.error('Error loading subscription prices', e);
      _setError('فشل في تحميل أسعار الاشتراكات: $e');
    }
  }

  /// تحميل العروض الخاصة
  Future<void> loadSpecialPrices() async {
    try {
      final querySnapshot = await _firestore
          .collection(specialPricesCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('validUntil', descending: true)
          .get();

      _specialPrices = querySnapshot.docs
          .map((doc) => SpecialPrice.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      notifyListeners();
    } catch (e) {
      Logger.error('Error loading special prices', e);
      _setError('فشل في تحميل العروض الخاصة: $e');
    }
  }

  /// إضافة سعر اشتراك جديد (للأدمن)
  Future<bool> addSubscriptionPrice(SubscriptionPrice price) async {
    try {
      final docRef = await _firestore.collection(pricesCollection).add(price.toMap());
      
      final newPrice = price.copyWith(id: docRef.id);
      _subscriptionPrices.add(newPrice);
      _subscriptionPrices.sort((a, b) => a.subjectName.compareTo(b.subjectName));
      
      notifyListeners();
      return true;
    } catch (e) {
      Logger.error('Error adding subscription price', e);
      _setError('فشل في إضافة السعر: $e');
      return false;
    }
  }

  /// تحديث سعر اشتراك (للأدمن)
  Future<bool> updateSubscriptionPrice(SubscriptionPrice price) async {
    try {
      await _firestore
          .collection(pricesCollection)
          .doc(price.id)
          .update(price.copyWith(updatedAt: DateTime.now()).toMap());

      final index = _subscriptionPrices.indexWhere((p) => p.id == price.id);
      if (index != -1) {
        _subscriptionPrices[index] = price.copyWith(updatedAt: DateTime.now());
        _subscriptionPrices.sort((a, b) => a.subjectName.compareTo(b.subjectName));
        notifyListeners();
      }

      return true;
    } catch (e) {
      Logger.error('Error updating subscription price', e);
      _setError('فشل في تحديث السعر: $e');
      return false;
    }
  }

  /// حذف سعر اشتراك (للأدمن)
  Future<bool> deleteSubscriptionPrice(String priceId) async {
    try {
      await _firestore.collection(pricesCollection).doc(priceId).delete();

      _subscriptionPrices.removeWhere((p) => p.id == priceId);
      notifyListeners();

      return true;
    } catch (e) {
      Logger.error('Error deleting subscription price', e);
      _setError('فشل في حذف السعر: $e');
      return false;
    }
  }

  /// إضافة عرض خاص جديد (للأدمن)
  Future<bool> addSpecialPrice(SpecialPrice price) async {
    try {
      final docRef = await _firestore.collection(specialPricesCollection).add(price.toMap());
      
      final newPrice = price.copyWith(id: docRef.id);
      _specialPrices.add(newPrice);
      _specialPrices.sort((a, b) => b.validUntil.compareTo(a.validUntil));
      
      notifyListeners();
      return true;
    } catch (e) {
      Logger.error('Error adding special price', e);
      _setError('فشل في إضافة العرض: $e');
      return false;
    }
  }

  /// تحديث عرض خاص (للأدمن)
  Future<bool> updateSpecialPrice(SpecialPrice price) async {
    try {
      await _firestore
          .collection(specialPricesCollection)
          .doc(price.id)
          .update(price.copyWith(updatedAt: DateTime.now()).toMap());

      final index = _specialPrices.indexWhere((p) => p.id == price.id);
      if (index != -1) {
        _specialPrices[index] = price.copyWith(updatedAt: DateTime.now());
        _specialPrices.sort((a, b) => b.validUntil.compareTo(a.validUntil));
        notifyListeners();
      }

      return true;
    } catch (e) {
      Logger.error('Error updating special price', e);
      _setError('فشل في تحديث العرض: $e');
      return false;
    }
  }

  /// حذف عرض خاص (للأدمن)
  Future<bool> deleteSpecialPrice(String priceId) async {
    try {
      await _firestore.collection(specialPricesCollection).doc(priceId).delete();

      _specialPrices.removeWhere((p) => p.id == priceId);
      notifyListeners();

      return true;
    } catch (e) {
      Logger.error('Error deleting special price', e);
      _setError('فشل في حذف العرض: $e');
      return false;
    }
  }

  /// الحصول على سعر مادة معينة
  SubscriptionPrice? getPriceForSubject(String subjectId) {
    try {
      return _subscriptionPrices.firstWhere((p) => p.subjectId == subjectId);
    } catch (e) {
      return null;
    }
  }

  /// إنشاء أسعار تلقائية للمواد الجديدة (للأدمن)
  Future<bool> createPricesForSubjects(List<Subject> subjects, {double defaultPrice = 10.0, String currency = 'USD'}) async {
    try {
      final batch = _firestore.batch();
      final now = DateTime.now();

      for (final subject in subjects) {
        // التحقق من وجود سعر للمادة
        final existingPrice = getPriceForSubject(subject.id);
        if (existingPrice == null) {
          final price = SubscriptionPrice(
            id: '', // سيتم تعيينه تلقائياً
            subjectId: subject.id,
            subjectName: subject.name,
            price: defaultPrice,
            currency: currency,
            isActive: true,
            createdAt: now,
            updatedAt: now,
          );

          final docRef = _firestore.collection(pricesCollection).doc();
          batch.set(docRef, price.copyWith(id: docRef.id).toMap());
        }
      }

      await batch.commit();
      await loadSubscriptionPrices(); // إعادة تحميل البيانات

      return true;
    } catch (e) {
      Logger.error('Error creating prices for subjects', e);
      _setError('فشل في إنشاء الأسعار: $e');
      return false;
    }
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    await initialize();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
