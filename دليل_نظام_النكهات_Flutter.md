# دليل إنشاء نظام النكهات (Flavors) في Flutter - تطبيقين منفصلين (أدمن وطالب)

## نظرة عامة
هذا الدليل يوضح كيفية إنشاء نظام نكهات في Flutter لإنتاج تطبيقين منفصلين من نفس الكود:
- **تطبيق الطالب**: للطلاب لحل الأسئلة ومشاهدة الفيديوهات
- **تطبيق الأدمن**: للمدرسين لإدارة المحتوى والأسئلة

## 1. إعداد ملف النكهات الأساسي

### إنشاء ملف `lib/flavors.dart`:
```dart
enum Flavor { student, admin }

class F {
  static Flavor? appFlavor;

  static String get name => appFlavor?.name ?? '';

  static String get title {
    switch (appFlavor) {
      case Flavor.student:
        return 'Smart Edu - Student';
      case Flavor.admin:
        return 'Smart Edu - Admin';
      default:
        return 'title';
    }
  }

  static String get appId {
    switch (appFlavor) {
      case Flavor.student:
        return 'com.smarttest.student';
      case Flavor.admin:
        return 'com.smarttest.admin';
      default:
        return 'com.smarttest.app';
    }
  }

  static bool get isStudent => appFlavor == Flavor.student;
  static bool get isAdmin => appFlavor == Flavor.admin;
}
```

## 2. تعديل ملف main.dart

### تحديث `lib/main.dart`:
```dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import 'flavors.dart';
import 'app.dart';
import 'firebase_options_student.dart';
import 'firebase_options_admin.dart' as admin_options;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // تحديد النكهة بناءً على متغير البيئة
    const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'student');
    debugPrint('🚀 تشغيل التطبيق بنكهة: $flavor');

    if (flavor == 'admin') {
      F.appFlavor = Flavor.admin;
      await Firebase.initializeApp(
        options: admin_options.DefaultFirebaseOptions.currentPlatform,
      );
    } else {
      F.appFlavor = Flavor.student;
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    runApp(const MyApp());
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة التطبيق: $e');
  }
}
```

## 3. تعديل ملف التطبيق الرئيسي

### تحديث `lib/app.dart`:
```dart
import 'package:flutter/material.dart';
import 'flavors.dart';
import 'features/student/presentation/pages/student_home_page.dart';
import 'features/admin/presentation/pages/admin_home_page.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: F.title,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(primarySwatch: Colors.blue),
      home: F.isStudent 
          ? const StudentHomePage() 
          : const AdminHomePage(),
    );
  }
}
```

## 4. إعداد Android Flavors

### تحديث `android/app/build.gradle.kts`:
```kotlin
android {
    namespace = "com.example.smart_test"
    compileSdk = flutter.compileSdkVersion

    defaultConfig {
        applicationId = "com.smarttest.app"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    flavorDimensions += "default"
    productFlavors {
        create("student") {
            dimension = "default"
            applicationId = "com.smarttest.student"
            resValue("string", "app_name", "Smart Test - Student")
            buildConfigField("String", "FLAVOR", "\"student\"")
        }
        create("admin") {
            dimension = "default"
            applicationId = "com.smarttest.admin"
            resValue("string", "app_name", "Smart Test - Admin")
            buildConfigField("String", "FLAVOR", "\"admin\"")
        }
    }

    buildFeatures {
        buildConfig = true
    }

    buildTypes {
        debug {
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true
        }
        release {
            signingConfig = signingConfigs.getByName("debug")
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = false
        }
    }
}
```

## 5. إعداد Firebase منفصل لكل نكهة

### إنشاء مشاريع Firebase منفصلة:
1. **مشروع Firebase للطلاب**: `smart-test-student`
2. **مشروع Firebase للأدمن**: `smart-test-admin`

### تشغيل FlutterFire CLI لكل مشروع:

#### للطلاب:
```bash
flutterfire configure --project=smart-test-student --out=lib/firebase_options_student.dart --ios-bundle-id=com.smarttest.student --android-package-name=com.smarttest.student
```

#### للأدمن:
```bash
flutterfire configure --project=smart-test-admin --out=lib/firebase_options_admin.dart --ios-bundle-id=com.smarttest.admin --android-package-name=com.smarttest.admin
```

## 6. إعداد ملفات google-services.json

### إنشاء مجلدات منفصلة:
```
android/app/src/
├── student/
│   └── google-services.json  (من مشروع Firebase الطلاب)
└── admin/
    └── google-services.json  (من مشروع Firebase الأدمن)
```

## 7. تشغيل التطبيقات

### أوامر التشغيل:

#### تطبيق الطالب:
```bash
# Android
flutter run --flavor student --dart-define=FLAVOR=student

# Windows
flutter run -d windows --dart-define=FLAVOR=student

# Build APK
flutter build apk --flavor student --dart-define=FLAVOR=student
```

#### تطبيق الأدمن:
```bash
# Android
flutter run --flavor admin --dart-define=FLAVOR=admin

# Windows
flutter run -d windows --dart-define=FLAVOR=admin

# Build APK
flutter build apk --flavor admin --dart-define=FLAVOR=admin
```

## 8. إعداد الأيقونات المختلفة (اختياري)

### في `pubspec.yaml`:
```yaml
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/icon.png"
  min_sdk_android: 21
  adaptive_icon_background: "assets/images/icon.png"
```

### لأيقونات مختلفة لكل نكهة، أنشئ:
```
assets/images/
├── student_icon.png
└── admin_icon.png
```

## 9. استخدام النكهات في الكود

### مثال على استخدام النكهات:
```dart
// في أي صفحة
if (F.isStudent) {
  // كود خاص بتطبيق الطالب
  return StudentWidget();
} else if (F.isAdmin) {
  // كود خاص بتطبيق الأدمن
  return AdminWidget();
}

// أو استخدام العنوان
AppBar(title: Text(F.title))
```

## 10. ملاحظات مهمة

### قواعد Firebase منفصلة:
- **تطبيق الطالب**: قراءة فقط للمحتوى
- **تطبيق الأدمن**: قراءة وكتابة كاملة

### أمان البيانات:
- كل تطبيق يتصل بقاعدة بيانات Firebase منفصلة
- قواعد الأمان مختلفة لكل تطبيق

### التطوير:
- يمكن تشغيل كلا التطبيقين على نفس الجهاز
- لهما package names مختلفة

## 11. استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ Firebase**: تأكد من وجود ملفات google-services.json في المجلدات الصحيحة
2. **خطأ Build**: تأكد من تطابق package names في جميع الملفات
3. **خطأ Flavor**: تأكد من استخدام `--dart-define=FLAVOR=` في أوامر التشغيل

### فحص النكهة الحالية:
```dart
debugPrint('النكهة الحالية: ${F.name}');
debugPrint('هل هو تطبيق طالب؟ ${F.isStudent}');
debugPrint('هل هو تطبيق أدمن؟ ${F.isAdmin}');
```

هذا النظام يسمح بإنتاج تطبيقين منفصلين تماماً من نفس الكود المصدري، مع إمكانية تخصيص كل تطبيق حسب احتياجاته.
