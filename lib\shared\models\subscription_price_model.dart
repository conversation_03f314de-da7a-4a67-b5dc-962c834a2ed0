class SubscriptionPrice {
  final String id;
  final String subjectId;
  final String subjectName;
  final double price;
  final String currency;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SubscriptionPrice({
    required this.id,
    required this.subjectId,
    required this.subjectName,
    required this.price,
    this.currency = 'USD',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء من Map (Firebase)
  factory SubscriptionPrice.fromMap(Map<String, dynamic> map) {
    return SubscriptionPrice(
      id: map['id'] ?? '',
      subjectId: map['subjectId'] ?? '',
      subjectName: map['subjectName'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      currency: map['currency'] ?? 'USD',
      isActive: map['isActive'] ?? true,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
    );
  }

  /// تحويل إلى Map (Firebase)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'subjectId': subjectId,
      'subjectName': subjectName,
      'price': price,
      'currency': currency,
      'isActive': isActive,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  /// نسخ مع تعديل
  SubscriptionPrice copyWith({
    String? id,
    String? subjectId,
    String? subjectName,
    double? price,
    String? currency,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SubscriptionPrice(
      id: id ?? this.id,
      subjectId: subjectId ?? this.subjectId,
      subjectName: subjectName ?? this.subjectName,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تنسيق السعر للعرض
  String get formattedPrice {
    // تحويل السعر إلى عدد صحيح إذا كان لا يحتوي على كسور
    final priceString = price % 1 == 0
        ? price.toInt().toString()
        : price.toString();

    if (currency == 'USD') {
      return '\$$priceString';
    } else if (currency == 'SYP') {
      return '$priceString ل.س';
    } else {
      return '$priceString $currency';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubscriptionPrice &&
        other.id == id &&
        other.subjectId == subjectId &&
        other.subjectName == subjectName &&
        other.price == price &&
        other.currency == currency &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        subjectId.hashCode ^
        subjectName.hashCode ^
        price.hashCode ^
        currency.hashCode ^
        isActive.hashCode;
  }

  @override
  String toString() {
    return 'SubscriptionPrice(id: $id, subjectName: $subjectName, price: $formattedPrice, isActive: $isActive)';
  }
}

/// نموذج سعر خاص للعروض
class SpecialPrice {
  final String id;
  final String title;
  final String description;
  final List<String> subjectIds;
  final double originalPrice;
  final double discountedPrice;
  final double discountPercentage;
  final String currency;
  final bool isActive;
  final DateTime validFrom;
  final DateTime validUntil;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SpecialPrice({
    required this.id,
    required this.title,
    required this.description,
    required this.subjectIds,
    required this.originalPrice,
    required this.discountedPrice,
    required this.discountPercentage,
    this.currency = 'USD',
    this.isActive = true,
    required this.validFrom,
    required this.validUntil,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء من Map (Firebase)
  factory SpecialPrice.fromMap(Map<String, dynamic> map) {
    return SpecialPrice(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      subjectIds: List<String>.from(map['subjectIds'] ?? []),
      originalPrice: (map['originalPrice'] ?? 0.0).toDouble(),
      discountedPrice: (map['discountedPrice'] ?? 0.0).toDouble(),
      discountPercentage: (map['discountPercentage'] ?? 0.0).toDouble(),
      currency: map['currency'] ?? 'USD',
      isActive: map['isActive'] ?? true,
      validFrom: DateTime.fromMillisecondsSinceEpoch(map['validFrom'] ?? 0),
      validUntil: DateTime.fromMillisecondsSinceEpoch(map['validUntil'] ?? 0),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
    );
  }

  /// تحويل إلى Map (Firebase)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'subjectIds': subjectIds,
      'originalPrice': originalPrice,
      'discountedPrice': discountedPrice,
      'discountPercentage': discountPercentage,
      'currency': currency,
      'isActive': isActive,
      'validFrom': validFrom.millisecondsSinceEpoch,
      'validUntil': validUntil.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  /// تنسيق السعر الأصلي
  String get formattedOriginalPrice {
    final priceString = originalPrice % 1 == 0
        ? originalPrice.toInt().toString()
        : originalPrice.toString();

    if (currency == 'USD') {
      return '\$$priceString';
    } else if (currency == 'SYP') {
      return '$priceString ل.س';
    } else {
      return '$priceString $currency';
    }
  }

  /// تنسيق السعر المخفض
  String get formattedDiscountedPrice {
    final priceString = discountedPrice % 1 == 0
        ? discountedPrice.toInt().toString()
        : discountedPrice.toString();

    if (currency == 'USD') {
      return '\$$priceString';
    } else if (currency == 'SYP') {
      return '$priceString ل.س';
    } else {
      return '$priceString $currency';
    }
  }

  /// نسخ مع تعديل
  SpecialPrice copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? subjectIds,
    double? originalPrice,
    double? discountedPrice,
    double? discountPercentage,
    String? currency,
    bool? isActive,
    DateTime? validFrom,
    DateTime? validUntil,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SpecialPrice(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      subjectIds: subjectIds ?? this.subjectIds,
      originalPrice: originalPrice ?? this.originalPrice,
      discountedPrice: discountedPrice ?? this.discountedPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      currency: currency ?? this.currency,
      isActive: isActive ?? this.isActive,
      validFrom: validFrom ?? this.validFrom,
      validUntil: validUntil ?? this.validUntil,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من صحة العرض
  bool get isValid {
    final now = DateTime.now();
    return isActive && now.isAfter(validFrom) && now.isBefore(validUntil);
  }

  @override
  String toString() {
    return 'SpecialPrice(title: $title, originalPrice: $formattedOriginalPrice, discountedPrice: $formattedDiscountedPrice, discount: ${discountPercentage.toStringAsFixed(0)}%)';
  }
}
