import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/utils/text_direction_helper.dart';

class QuestionPracticePage extends StatefulWidget {
  final List<Question> questions;
  final String title;

  const QuestionPracticePage({
    super.key,
    required this.questions,
    required this.title,
  });

  @override
  State<QuestionPracticePage> createState() => _QuestionPracticePageState();
}

class _QuestionPracticePageState extends State<QuestionPracticePage> {
  int _currentQuestionIndex = 0;
  List<int?> _selectedAnswers = [];
  bool _showExplanation = false;
  bool _isAnswered = false;

  @override
  void initState() {
    super.initState();
    _selectedAnswers = List.filled(widget.questions.length, null);
  }

  Question get _currentQuestion => widget.questions[_currentQuestionIndex];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          widget.title,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.getPrimaryGradient(context),
          ),
        ),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _toggleFavorite,
            icon: Icon(
              _isFavorite() ? Icons.star : Icons.star_outline,
              color: _isFavorite() ? AppTheme.warningColor : Colors.white,
            ),
            tooltip: 'إضافة للمفضلة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط التقدم
          _buildProgressBar(),

          // محتوى السؤال
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات السؤال
                  _buildQuestionInfo(),

                  SizedBox(height: 16.h),

                  // نص السؤال
                  _buildQuestionText(),

                  SizedBox(height: 24.h),

                  // الخيارات
                  _buildOptions(),

                  SizedBox(height: 24.h),

                  // الشرح (إذا كان مرئياً)
                  if (_showExplanation) _buildExplanation(),
                ],
              ),
            ),
          ),

          // أزرار التحكم
          _buildControlButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    final progress = (_currentQuestionIndex + 1) / widget.questions.length;

    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السؤال ${_currentQuestionIndex + 1} من ${widget.questions.length}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppTheme.textSecondaryColor.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            minHeight: 6.h,
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionInfo() {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: _getDifficultyColor(_currentQuestion.difficulty),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            _getDifficultyText(_currentQuestion.difficulty),
            style: TextStyle(
              color: Colors.white,
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SizedBox(width: 8.w),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: AppTheme.accentColor,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            '${_currentQuestion.points} نقطة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionText() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Text(
          _currentQuestion.questionText,
          textDirection: TextDirection.rtl, // من اليمين لليسار
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w500,
            color: AppTheme.textPrimaryColor,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  Widget _buildOptions() {
    return Column(
      children: _currentQuestion.options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedAnswers[_currentQuestionIndex] == index;
        final isCorrect = _currentQuestion.correctAnswers.contains(option);

        Color? backgroundColor;
        Color? borderColor;
        Color? textColor;

        if (_isAnswered) {
          if (isCorrect) {
            backgroundColor = AppTheme.successColor.withValues(alpha: 0.1);
            borderColor = AppTheme.successColor;
            textColor = AppTheme.successColor;
          } else if (isSelected && !isCorrect) {
            backgroundColor = AppTheme.errorColor.withValues(alpha: 0.1);
            borderColor = AppTheme.errorColor;
            textColor = AppTheme.errorColor;
          }
        } else if (isSelected) {
          backgroundColor = AppTheme.primaryColor.withValues(alpha: 0.1);
          borderColor = AppTheme.primaryColor;
          textColor = AppTheme.primaryColor;
        }

        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          child: InkWell(
            onTap: _isAnswered ? null : () => _selectAnswer(index),
            borderRadius: BorderRadius.circular(12.r),
            child: Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: backgroundColor ?? Colors.white,
                border: Border.all(
                  color:
                      borderColor ??
                      AppTheme.textSecondaryColor.withValues(alpha: 0.3),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                children: [
                  Container(
                    width: 24.w,
                    height: 24.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected || (_isAnswered && isCorrect)
                          ? (textColor ?? AppTheme.primaryColor)
                          : Colors.transparent,
                      border: Border.all(
                        color: textColor ?? AppTheme.textSecondaryColor,
                        width: 2,
                      ),
                    ),
                    child: isSelected || (_isAnswered && isCorrect)
                        ? Icon(
                            _isAnswered && isCorrect
                                ? Icons.check
                                : _isAnswered && isSelected && !isCorrect
                                ? Icons.close
                                : Icons.circle,
                            color: Colors.white,
                            size: 12.sp,
                          )
                        : null,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      option,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: textColor ?? AppTheme.textPrimaryColor,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildExplanation() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          color: AppTheme.successColor.withValues(alpha: 0.1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: AppTheme.successColor,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  'الشرح',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.successColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            TextDirectionHelper.buildSmartText(
              _currentQuestion.explanation,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textPrimaryColor,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButtons() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        children: [
          if (_currentQuestionIndex > 0)
            Expanded(
              child: ElevatedButton(
                onPressed: _previousQuestion,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.textSecondaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: const Text('السابق'),
              ),
            ),

          if (_currentQuestionIndex > 0) SizedBox(width: 12.w),

          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isAnswered ? _nextQuestion : _checkAnswer,
              style: ElevatedButton.styleFrom(
                backgroundColor: _isAnswered
                    ? AppTheme.primaryColor
                    : AppTheme.successColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                _isAnswered
                    ? (_currentQuestionIndex < widget.questions.length - 1
                          ? 'التالي'
                          : 'إنهاء')
                    : 'تحقق من الإجابة',
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getDifficultyText(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return 'سهل';
      case DifficultyLevel.medium:
        return 'متوسط';
      case DifficultyLevel.hard:
        return 'صعب';
    }
  }

  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return AppTheme.successColor;
      case DifficultyLevel.medium:
        return AppTheme.warningColor;
      case DifficultyLevel.hard:
        return AppTheme.errorColor;
    }
  }

  void _selectAnswer(int index) {
    if (!_isAnswered) {
      setState(() {
        _selectedAnswers[_currentQuestionIndex] = index;
      });
    }
  }

  void _checkAnswer() {
    if (_selectedAnswers[_currentQuestionIndex] != null) {
      setState(() {
        _isAnswered = true;
        _showExplanation = true;
      });
    }
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < widget.questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _isAnswered = _selectedAnswers[_currentQuestionIndex] != null;
        _showExplanation = _isAnswered;
      });
    } else {
      _finishPractice();
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
        _isAnswered = _selectedAnswers[_currentQuestionIndex] != null;
        _showExplanation = _isAnswered;
      });
    }
  }

  void _finishPractice() {
    // حساب النتائج
    int correctAnswers = 0;
    for (int i = 0; i < widget.questions.length; i++) {
      if (_selectedAnswers[i] != null) {
        final selectedOption =
            widget.questions[i].options[_selectedAnswers[i]!];
        if (widget.questions[i].correctAnswers.contains(selectedOption)) {
          correctAnswers++;
        }
      }
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('انتهى التدريب'),
        content: Text(
          'لقد أجبت على $correctAnswers من ${widget.questions.length} أسئلة بشكل صحيح.\n'
          'النسبة: ${((correctAnswers / widget.questions.length) * 100).round()}%',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(); // العودة للصفحة السابقة
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  bool _isFavorite() {
    // هنا يجب فحص ما إذا كان السؤال في المفضلة
    return false;
  }

  void _toggleFavorite() {
    // هنا يجب إضافة/إزالة السؤال من المفضلة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isFavorite()
              ? 'تم إزالة السؤال من المفضلة'
              : 'تم إضافة السؤال للمفضلة',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
