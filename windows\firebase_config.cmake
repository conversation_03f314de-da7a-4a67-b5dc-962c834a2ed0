# Firebase configuration for Windows builds
# This file contains specific settings to help Firebase SDK build correctly on Windows

# Set Firebase specific compiler flags
if(MSVC)
    # Disable specific warnings that Firebase SDK generates
    add_compile_options(
        /wd4996  # Deprecated function warnings
        /wd4267  # Size_t conversion warnings
        /wd4244  # Type conversion warnings
        /wd4305  # Truncation warnings
        /wd4018  # Signed/unsigned mismatch
        /wd4101  # Unreferenced local variable
        /wd4099  # PDB warnings
        /wd4251  # DLL interface warnings
        /wd4275  # DLL interface warnings for base classes
        /wd4800  # Boolean conversion warnings
        /wd4005  # Macro redefinition warnings
    )
    
    # Set large object compilation for Firebase
    add_compile_options(/bigobj)
    
    # Set runtime library to match Firebase expectations
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL")
    
    # Increase linker heap size for large Firebase libraries
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /HEAP:8388608")
    set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} /HEAP:8388608")
    
    # Enable large address aware for 32-bit builds
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /LARGEADDRESSAWARE")
    
    # Disable incremental linking to avoid issues
    set(CMAKE_EXE_LINKER_FLAGS_DEBUG "${CMAKE_EXE_LINKER_FLAGS_DEBUG} /INCREMENTAL:NO")
    set(CMAKE_SHARED_LINKER_FLAGS_DEBUG "${CMAKE_SHARED_LINKER_FLAGS_DEBUG} /INCREMENTAL:NO")
endif()

# Set required system libraries for Firebase
set(FIREBASE_SYSTEM_LIBS
    ws2_32
    crypt32
    advapi32
    user32
    kernel32
    ole32
    oleaut32
    uuid
    shell32
    winmm
    version
    iphlpapi
    psapi
    userenv
    bcrypt
    ncrypt
)

# Function to apply Firebase settings to a target
function(apply_firebase_settings target_name)
    if(MSVC)
        target_compile_options(${target_name} PRIVATE /bigobj)
        target_link_options(${target_name} PRIVATE /LARGEADDRESSAWARE)
        
        # Link required system libraries
        foreach(lib ${FIREBASE_SYSTEM_LIBS})
            target_link_libraries(${target_name} PRIVATE ${lib})
        endforeach()
    endif()
endfunction()

# Set environment variables for Firebase build
set(ENV{FIREBASE_CPP_SDK_DIR} "${CMAKE_CURRENT_SOURCE_DIR}/build/windows/x64/firebase_cpp_sdk_windows")

# Force Firebase to use local SDK instead of downloading
set(FIREBASE_CPP_SDK_DIR "${CMAKE_CURRENT_SOURCE_DIR}/build/windows/x64/firebase_cpp_sdk_windows")
set(ENV{FIREBASE_CPP_SDK_VERSION} "12.8.0")

# Tell Firebase plugin to skip download
set(FIREBASE_SKIP_DOWNLOAD ON)
set(ENV{FIREBASE_SKIP_DOWNLOAD} "ON")

# Ensure proper threading model
set(CMAKE_THREAD_LIBS_INIT "-lpthread")
set(CMAKE_HAVE_THREADS_LIBRARY 1)
set(CMAKE_USE_WIN32_THREADS_INIT 0)
set(CMAKE_USE_PTHREADS_INIT 1)
set(THREADS_PREFER_PTHREAD_FLAG ON)
