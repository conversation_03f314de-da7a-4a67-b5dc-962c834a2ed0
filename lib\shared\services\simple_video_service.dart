import 'dart:io';
import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/video_model.dart';
import 'encryption_service.dart';
import 'persistent_storage_service.dart';
import 'single_read_data_service.dart';
import 'device_service.dart';

/// خدمة الفيديوهات المبسطة - تركز فقط على التشفير والتحميل
/// تحصل على بيانات الفيديوهات من SingleReadDataService
class SimpleVideoService extends ChangeNotifier {
  static final SimpleVideoService _instance = SimpleVideoService._internal();
  static SimpleVideoService get instance => _instance;
  SimpleVideoService._internal();

  final EncryptionService _encryption = EncryptionService.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;
  final SingleReadDataService _dataService = SingleReadDataService.instance;
  final DeviceService _deviceService = DeviceService.instance;

  // مجلد حفظ الفيديوهات المشفرة
  Directory? _videosDirectory;

  // حالة التحميل
  final Map<String, double> _downloadProgress = {};
  final Map<String, bool> _isDownloading = {};
  final Set<String> _downloadedVideos = {};

  // Getters
  Map<String, double> get downloadProgress =>
      Map.unmodifiable(_downloadProgress);
  Map<String, bool> get isDownloading => Map.unmodifiable(_isDownloading);
  Set<String> get downloadedVideos => Set.unmodifiable(_downloadedVideos);

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      debugPrint('🎬 تهيئة خدمة الفيديوهات المبسطة...');

      _encryption.initialize();
      await _persistentStorage.initialize();
      await _initializeVideosDirectory();
      await _loadDownloadedVideos();

      debugPrint('✅ تم تهيئة خدمة الفيديوهات المبسطة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الفيديوهات: $e');
    }
  }

  /// تهيئة مجلد الفيديوهات
  Future<void> _initializeVideosDirectory() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _videosDirectory = Directory('${appDir.path}/encrypted_videos');

      if (!await _videosDirectory!.exists()) {
        await _videosDirectory!.create(recursive: true);
      }

      debugPrint('📁 مجلد الفيديوهات: ${_videosDirectory!.path}');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مجلد الفيديوهات: $e');
    }
  }

  /// تحميل قائمة الفيديوهات المحملة
  Future<void> _loadDownloadedVideos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final downloadedList = prefs.getStringList('downloaded_videos') ?? [];
      _downloadedVideos.clear();
      _downloadedVideos.addAll(downloadedList);

      debugPrint('📱 تم تحميل ${_downloadedVideos.length} فيديو محمل');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل قائمة الفيديوهات المحملة: $e');
    }
  }

  /// حفظ قائمة الفيديوهات المحملة
  Future<void> _saveDownloadedVideos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(
        'downloaded_videos',
        _downloadedVideos.toList(),
      );
    } catch (e) {
      debugPrint('❌ خطأ في حفظ قائمة الفيديوهات المحملة: $e');
    }
  }

  /// التحقق من تحميل الفيديو
  bool isVideoDownloaded(String videoId) {
    return _downloadedVideos.contains(videoId);
  }

  /// التحقق من حالة التحميل
  bool isVideoDownloading(String videoId) {
    return _isDownloading[videoId] ?? false;
  }

  /// الحصول على تقدم التحميل
  double getDownloadProgress(String videoId) {
    return _downloadProgress[videoId] ?? 0.0;
  }

  /// تحميل فيديو
  Future<bool> downloadVideo(String videoId) async {
    if (_isDownloading[videoId] == true) {
      debugPrint('⚠️ الفيديو قيد التحميل بالفعل: $videoId');
      return false;
    }

    if (isVideoDownloaded(videoId)) {
      debugPrint('⚠️ الفيديو محمل بالفعل: $videoId');
      return true;
    }

    try {
      _isDownloading[videoId] = true;
      _downloadProgress[videoId] = 0.0;
      notifyListeners();

      debugPrint('📥 بدء تحميل الفيديو: $videoId');

      // الحصول على بيانات الفيديو من SingleReadDataService
      final video = _getVideoById(videoId);
      if (video == null) {
        throw Exception('لم يتم العثور على الفيديو');
      }

      // فك تشفير رابط الفيديو (استخدام أفضل جودة متاحة)
      final encryptedUrl =
          video.encryptedUrl1080 ??
          video.encryptedUrl720 ??
          video.encryptedUrl480 ??
          video.encryptedUrl360;

      if (encryptedUrl == null) {
        throw Exception('لا يوجد رابط فيديو متاح');
      }

      // تحويل الرابط المشفر إلى التنسيق المطلوب
      final encryptedData = {'encryptedData': encryptedUrl};
      final deviceId = await _deviceService.getDeviceId();
      final decryptedData = await _encryption.decryptVideoUrl(
        encryptedData,
        deviceId,
      );

      if (decryptedData == null || decryptedData['url'] == null) {
        throw Exception('فشل في فك تشفير رابط الفيديو');
      }

      final decryptedUrl = decryptedData['url'] as String;

      // تحميل الفيديو
      final success = await _downloadVideoFile(videoId, decryptedUrl);

      if (success) {
        _downloadedVideos.add(videoId);
        await _saveDownloadedVideos();
        debugPrint('✅ تم تحميل الفيديو بنجاح: $videoId');
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفيديو $videoId: $e');
      return false;
    } finally {
      _isDownloading[videoId] = false;
      _downloadProgress[videoId] = 0.0;
      notifyListeners();
    }
  }

  /// تحميل ملف الفيديو
  Future<bool> _downloadVideoFile(String videoId, String url) async {
    try {
      final dio = Dio();
      final filePath = '${_videosDirectory!.path}/$videoId.encrypted';

      await dio.download(
        url,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            _downloadProgress[videoId] = progress;
            notifyListeners();
          }
        },
      );

      // تشفير الملف
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final encryptedBytes = _encryption.encryptVideoData(bytes);
      await file.writeAsBytes(encryptedBytes);

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل ملف الفيديو: $e');
      return false;
    }
  }

  /// حذف فيديو محمل
  Future<bool> deleteVideo(String videoId) async {
    try {
      final filePath = '${_videosDirectory!.path}/$videoId.encrypted';
      final file = File(filePath);

      if (await file.exists()) {
        await file.delete();
      }

      _downloadedVideos.remove(videoId);
      await _saveDownloadedVideos();

      debugPrint('🗑️ تم حذف الفيديو: $videoId');
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف الفيديو: $e');
      return false;
    }
  }

  /// الحصول على مسار الفيديو المحمل
  String? getVideoPath(String videoId) {
    if (!isVideoDownloaded(videoId)) return null;
    return '${_videosDirectory!.path}/$videoId.encrypted';
  }

  /// فك تشفير وتشغيل الفيديو المحمل
  Future<File?> getDecryptedVideoFile(String videoId) async {
    try {
      final filePath = '${_videosDirectory!.path}/$videoId.encrypted';
      final encryptedFile = File(filePath);

      if (!await encryptedFile.exists()) {
        return null;
      }

      final encryptedBytes = await encryptedFile.readAsBytes();
      final decryptedBytes = await _encryption.decryptVideoData(encryptedBytes);

      // إنشاء ملف مؤقت للتشغيل
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$videoId.mp4');
      await tempFile.writeAsBytes(decryptedBytes);

      return tempFile;
    } catch (e) {
      debugPrint('❌ خطأ في فك تشفير الفيديو: $e');
      return null;
    }
  }

  /// الحصول على فيديو بالمعرف من SingleReadDataService
  Video? _getVideoById(String videoId) {
    return _dataService.videos
        .where((video) => video.id == videoId)
        .firstOrNull;
  }

  /// الحصول على جميع الفيديوهات للدرس
  List<Video> getVideosByLesson(String lessonId) {
    return _dataService.getVideosByLesson(lessonId);
  }

  /// الحصول على رابط الفيديو للتشغيل
  Future<String?> getVideoUrlForPlayback(String videoId) async {
    try {
      // إذا كان الفيديو محمل، استخدم النسخة المحلية
      if (isVideoDownloaded(videoId)) {
        final decryptedFile = await getDecryptedVideoFile(videoId);
        return decryptedFile?.path;
      }

      // إذا لم يكن محمل، استخدم الرابط المباشر
      final video = _getVideoById(videoId);
      if (video == null) return null;

      // فك تشفير رابط الفيديو (استخدام أفضل جودة متاحة)
      final encryptedUrl =
          video.encryptedUrl1080 ??
          video.encryptedUrl720 ??
          video.encryptedUrl480 ??
          video.encryptedUrl360;

      if (encryptedUrl == null) return null;

      // تحويل الرابط المشفر إلى التنسيق المطلوب
      final encryptedData = {'encryptedData': encryptedUrl};
      final deviceId = await _deviceService.getDeviceId();
      final decryptedData = await _encryption.decryptVideoUrl(
        encryptedData,
        deviceId,
      );

      return decryptedData?['url'] as String?;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على رابط الفيديو: $e');
      return null;
    }
  }

  /// تنظيف الملفات المؤقتة
  Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();

      for (final file in files) {
        if (file.path.endsWith('.mp4')) {
          await file.delete();
        }
      }

      debugPrint('🧹 تم تنظيف الملفات المؤقتة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الملفات المؤقتة: $e');
    }
  }

  /// الحصول على حجم الفيديوهات المحملة
  Future<int> getDownloadedVideosSize() async {
    try {
      int totalSize = 0;

      for (final videoId in _downloadedVideos) {
        final filePath = '${_videosDirectory!.path}/$videoId.encrypted';
        final file = File(filePath);

        if (await file.exists()) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      debugPrint('❌ خطأ في حساب حجم الفيديوهات: $e');
      return 0;
    }
  }

  /// حذف جميع الفيديوهات المحملة
  Future<void> deleteAllVideos() async {
    try {
      for (final videoId in _downloadedVideos.toList()) {
        await deleteVideo(videoId);
      }

      debugPrint('🗑️ تم حذف جميع الفيديوهات المحملة');
    } catch (e) {
      debugPrint('❌ خطأ في حذف جميع الفيديوهات: $e');
    }
  }
}
