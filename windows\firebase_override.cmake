# Firebase SDK Override Configuration
# This file forces Firebase to use local SDK instead of downloading

# Set the local Firebase SDK path
set(FIREBASE_CPP_SDK_DIR "${CMAKE_CURRENT_SOURCE_DIR}/build/windows/x64/firebase_cpp_sdk_windows")

# Override Firebase download function to use local SDK
function(firebase_cpp_sdk_download)
    message(STATUS "Using local Firebase SDK at: ${FIREBASE_CPP_SDK_DIR}")
    
    # Check if local SDK exists
    if(NOT EXISTS "${FIREBASE_CPP_SDK_DIR}")
        message(FATAL_ERROR "Local Firebase SDK not found at: ${FIREBASE_CPP_SDK_DIR}")
    endif()
    
    # Set Firebase SDK variables
    set(FIREBASE_CPP_SDK_DIR "${FIREBASE_CPP_SDK_DIR}" PARENT_SCOPE)
    set(ENV{FIREBASE_CPP_SDK_DIR} "${FIREBASE_CPP_SDK_DIR}")
    
    message(STATUS "Firebase SDK configured successfully")
endfunction()

# Override any download attempts
macro(ExternalProject_Add)
    # Check if this is a Firebase download
    if("${ARGV0}" MATCHES "firebase")
        message(STATUS "Skipping Firebase download, using local SDK")
        return()
    endif()
    
    # Call original function for non-Firebase projects
    _ExternalProject_Add(${ARGV})
endmacro()

# Backup original function
if(COMMAND ExternalProject_Add AND NOT COMMAND _ExternalProject_Add)
    get_property(_ExternalProject_Add_defined GLOBAL PROPERTY _ExternalProject_Add_defined)
    if(NOT _ExternalProject_Add_defined)
        # Backup the original function
        function(_ExternalProject_Add)
            # This will be replaced by the original ExternalProject_Add
        endfunction()
        set_property(GLOBAL PROPERTY _ExternalProject_Add_defined TRUE)
    endif()
endif()
