# دليل إعداد Cloudinary للمشروع

## الخطوة 1: إنشاء حساب Cloudinary مجاني

1. **اذهب إلى موقع Cloudinary:**
   - الرابط: https://cloudinary.com/users/register/free

2. **أنشئ حساب مجاني:**
   - أدخل بياناتك (الاسم، البريد الإلكتروني، كلمة المرور)
   - اختر "Developer" كنوع الحساب
   - أكمل التسجيل

3. **تأكيد البريد الإلكتروني:**
   - تحقق من بريدك الإلكتروني وأكد الحساب

## الخطوة 2: الحصول على Cloud Name

1. **اذهب إلى Dashboard:**
   - بعد تسجيل الدخول، ستكون في الصفحة الرئيسية (Dashboard)

2. **انسخ Cloud Name:**
   - في أعلى الصفحة، ستجد معلومات الحساب
   - انسخ قيمة **Cloud name** (مثل: `dxxxxx` أو اسم مخصص)

## الخطوة 3: إنشاء Upload Preset

1. **اذهب إلى Settings:**
   - انقر على أيقونة الترس (⚙️) في الأعلى
   - أو اذهب إلى: Settings > Upload

2. **أنشئ Upload Preset جديد:**
   - انقر على "Add upload preset"
   - أدخل اسم الـ preset: `smart_test_preset`
   - اختر Signing Mode: **"Unsigned"**
   - في قسم "Folder": أدخل `smart_test/questions`

3. **إعدادات إضافية (اختيارية):**
   - **Auto-optimize**: تفعيل (لضغط أفضل)
   - **Auto-format**: تفعيل (لاختيار أفضل صيغة)
   - **Quality**: Auto أو 70-80%

4. **احفظ الإعدادات:**
   - انقر على "Save"

## الخطوة 4: تحديث إعدادات المشروع

1. **افتح ملف الإعدادات:**
   ```
   lib/shared/config/cloudinary_config.dart
   ```

2. **استبدل القيم:**
   ```dart
   class CloudinaryConfig {
     // استبدل هذه القيمة بـ Cloud Name الخاص بك
     static const String cloudName = 'YOUR_ACTUAL_CLOUD_NAME';
     
     // استبدل هذه القيمة بـ Upload Preset الذي أنشأته
     static const String uploadPreset = 'smart_test_preset';
   }
   ```

## الخطوة 5: اختبار الإعداد

1. **شغل التطبيق:**
   ```bash
   flutter run --flavor admin --dart_define=FLAVOR=admin
   ```

2. **اختبر رفع صورة:**
   - اذهب إلى صفحة إضافة سؤال جديد
   - اختر "صورة" كنوع المحتوى
   - اختر صورة من المعرض أو الكاميرا
   - احفظ السؤال

3. **تحقق من النتائج:**
   - يجب أن ترى رسائل نجاح في الـ logs
   - تحقق من Cloudinary Dashboard لرؤية الصورة المرفوعة

## معلومات الخطة المجانية

**الحدود المجانية لـ Cloudinary:**
- ✅ **25GB bandwidth شهرياً** (كافي لآلاف الصور)
- ✅ **25,000 transformations شهرياً** (ضغط وتحسين)
- ✅ **25,000 images/videos storage** (مساحة تخزين)
- ✅ **CDN عالمي** (تحميل سريع)
- ✅ **ضغط تلقائي** (توفير مساحة)

## استكشاف الأخطاء

### خطأ: "Cloudinary غير مُعد بشكل صحيح"
- تأكد من تحديث قيم `cloudName` و `uploadPreset` في ملف الإعدادات

### خطأ: "Upload preset not found"
- تأكد من إنشاء Upload Preset بالاسم الصحيح: `smart_test_preset`
- تأكد من أن Signing Mode هو "Unsigned"

### خطأ: "Invalid cloud name"
- تأكد من نسخ Cloud Name بشكل صحيح من Dashboard
- لا تضع مسافات أو أحرف خاصة

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من Cloudinary Dashboard للتأكد من الإعدادات
2. راجع logs التطبيق للحصول على تفاصيل الخطأ
3. تأكد من اتصال الإنترنت عند رفع الصور
